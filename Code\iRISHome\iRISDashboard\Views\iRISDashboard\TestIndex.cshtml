﻿@inject Microsoft.AspNetCore.Http.IHttpContextAccessor HttpContextAccessor
@using Microsoft.AspNetCore.Http;
@using Newtonsoft.Json

@{
    var session = HttpContextAccessor.HttpContext.Session;
    var DisplayName = session.GetString("DisplayName");
    var IsAdminStatus = session.GetString("IsAdminStatus");
    int UserGroupID = Convert.ToInt32(session.GetString("UserGroupID"));
    string GroupName = session.GetString("GroupName");
    ViewBag.Title = "Dashboard";
    ViewBag.pTitle = "Dashboard";
    ViewBag.pageTitle = "Mobile Aspects";
    ViewBag.BrandName = "iRISHome";
}

@{
    var dt = JsonConvert.SerializeObject(ViewData["UserAttributes"] as System.Data.DataTable);
}

<link href="~/assets/Dashboard/assets/Vendors/CSS/bootstrap.min.css" rel="stylesheet">
<link href="~/assets/Dashboard/assets/style.css" rel="stylesheet">
<script src="~/assets/Dashboard/assets/Vendors/JS/bootstrap.bundle.min.js"></script>
<script src="~/assets/Dashboard/assets/Vendors/JS/jquery-3.6.0.min.js"></script>
<link href="~/assets/css/icons.min.css" rel="stylesheet" />
<link href="~/assets/css/app.min.css" id="app-style" rel="stylesheet" />

<div class="row">
    <header id="header" class="fixed-top d-flex align-items-center">
        <div class="container d-flex align-items-center">
            <h1 class="logo me-auto"><img src="~/assets/Dashboard/assets/img/Logo.png" alt="Logo"></h1>
            <nav id="navbar" class="navbar order-last order-lg-0">
                <ul class="NavBarList">
                    <!-- NavBar list will get updated through ajax call -->
                </ul>
            </nav>
        </div>
        <div class="text-end pe-2">
            <div class="dropdown d-inline-block">
                <button type="button" class="btn header-item waves-effect" id="page-header-user-dropdown"
                        data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <img class="rounded-circle header-profile-user" src="~/assets/images/UserAvatar.png"
                         alt="Header Avatar">
                    <span class="d-none d-xl-inline-block ms-1" key="t-henry">@DisplayName</span>
                    <i class="mdi mdi-chevron-down d-none d-xl-inline-block"></i>
                </button>
                <div class="dropdown-menu dropdown-menu-end">
                    @if (GroupName == "admin")
                    {
                        <a class="dropdown-item" href="@Url.Action("Admin", "iRISDashboard")">
                            <i class="fas fa-user-cog font-size-16 align-middle me-1"></i>
                            <span key="t-profile">Admin</span>
                        </a>
                    }
                    <a class="dropdown-item text-danger" href="@Url.Action("Logout", "AuthLogin")"><i class="bx bx-power-off font-size-16 align-middle me-1 text-danger"></i> <span key="t-logout">Logout</span></a>
                </div>
            </div>
        </div>
    </header>
</div>
<div class="row mb-5">
    <section class="services" id="services">
        <!-- Section will get updated through ajax call  -->
    </section>

</div>

<a href="#" class="back-to-top d-flex align-items-center justify-content-center">
    <i class='bx bxs-up-arrow-circle'></i>
</a>
@* <div class="fixed-bottom">
    @await Html.PartialAsync("~/Views/Shared/_footer.cshtml")
</div> *@


<script>
        let SiteDetailsArray = [];
    let ProjectNamesArray = [];

    async function GetSiteDetails() {
        debugger;
      try {
        let jsonData = @Html.Raw(dt);
        SiteDetailsArray.push(...jsonData);
        SiteDetailsArray.forEach(element => {
          if (!(ProjectNamesArray.includes(element.Project))) {
            ProjectNamesArray.push(element.Project);
          }
        });
      } catch (error) {
        console.error(error);
      }
    }

    async function CreateNavBar() {
        debugger;
      let cardContainer = document.getElementsByClassName('NavBarList')[0];
      let htmlContent = "";
      let count = 0;
      let navbarcard = "";
          ProjectNamesArray.forEach(element => {
                    let IdName = element.replace(/ /g, "");
                    if (count == 0) { navbarcard = `<li><a class="nav-link scrollto active" href="#${IdName}">${element}</a></li>` }
                    else { navbarcard = `<li><a class="nav-link scrollto" href="#${IdName}">${element}</a></li>` }
                    htmlContent += navbarcard;
                    count++;
          });
      cardContainer.innerHTML = htmlContent;
    }

    async function CreateSection() {
      let cardContainer = document.getElementById("services");
      let htmlContent = '';
      let count = 0;
      let section = '';

      if('admin' == "admin"){
          ProjectNamesArray.forEach(element => {
            if (count == 0) { section = `<section id="${element.replace(/\s+/g, '')}" class="mb-5 pt-5">` }
            else { section = `<section id="${element.replace(/\s+/g, '')}" class="mb-5">` }
            htmlContent += `
                ${section}
                    <div class="container">
                    <div class="section-title">
                        <h2>${element}</h2>
                    </div>
                    <div class="row d-flex justify-content-center" id="${element}CardDiv">
                    </div>
                    </div>
                </section>
                `;
            count++;
          });
      }
      else{
          ProjectNamesArray.forEach(element => {
              if(element =="iRISupply")
              {
                if (count == 0) { section = `<section id="${element.replace(/\s+/g, '')}" class="mb-5 pt-5">` }
                else { section = `<section id="${element.replace(/\s+/g, '')}" class="mb-5">` }
                htmlContent += `
                    ${section}
                        <div class="container">
                        <div class="section-title">
                            <h2>${element}</h2>
                        </div>
                        <div class="row d-flex justify-content-center" id="${element}CardDiv">
                        </div>
                        </div>
                    </section>
                    `;
                }
            count++;
          });
      }
      cardContainer.innerHTML = htmlContent;
    }

    async function InsertCards() {
        debugger;
      let projectCardContent = {};

      SiteDetailsArray.forEach(element => {
        let anchorOpenTag = "", anchorCloseTag = "", rdpOPenTag="",rdpCloseTag="";
        if (element.AppType == "Web") { anchorOpenTag = `<a href="${element.AppLink}" target="_blank">`; anchorCloseTag = "</a>" }
        else{rdpOPenTag=`<a href="./irisdemoserver1.rdp" download="irisdemoserver1.rdp">`; rdpCloseTag=`</a>`}
        let descriptionWords = element.Description.split(' ');
        if (descriptionWords.length > 12) {
          descriptionWords.splice(12, 0, '<span id="dotText" class="fs-5">...</span><span id="moreText">');
          descriptionWords.push('</span>');
        }
        let modifiedDescription = descriptionWords.join(' ');
        let htmlContent = `
        <div class="col-lg-4 col-md-6 d-flex align-items-stretch mb-3">
          ${rdpOPenTag}
            ${anchorOpenTag}
              <div class="icon-box">
                  <div class="icon"><i class=""><img style="height: 50px;" src="${element.ImgURL}" alt=""></i></div>
                  <h4>${element.AppName}</h4>
                  <p>${modifiedDescription}</p>
              </div>
              ${anchorCloseTag}
            ${rdpCloseTag}
        </div>`;

        if (!projectCardContent[element.Project]) {
          projectCardContent[element.Project] = '';
        }
        projectCardContent[element.Project] += htmlContent;
      });

      for (let project in projectCardContent) {
        let cardContainer = document.getElementById(project + 'CardDiv');
        if (cardContainer) {
          cardContainer.innerHTML = projectCardContent[project];
        }
      }

    }

    async function ApplyIndentation() {

      let select = (el, all = false) => {
        el = el.trim();
        if (all) {
          return [...document.querySelectorAll(el)];
        } else {
          return document.querySelector(el);
        }
      };

      let on = (type, el, listener, all = false) => {
        let selectEl = select(el, all);
        if (selectEl) {
          if (all) {
            selectEl.forEach(e => e.addEventListener(type, listener));
          } else {
            selectEl.addEventListener(type, listener);
          }
        }
      };

      let onscroll = (el, listener) => {
        el.addEventListener('scroll', listener);
      };

      let navbarlinks = select('#navbar .scrollto', true)
      let navbarlinksActive = () => {
        let position = window.scrollY + 300
        navbarlinks.forEach(navbarlink => {
          if (!navbarlink.hash) return
          let section = select(navbarlink.hash)
          if (!section) return
          if (position >= section.offsetTop && position <= (section.offsetTop + section.offsetHeight)) {
            navbarlink.classList.add('active')
          } else {
            navbarlink.classList.remove('active')
          }
        })
      }
      window.addEventListener('load', navbarlinksActive)
      onscroll(document, navbarlinksActive)


      let scrollto = (el) => {
        let header = select('.section-title')
        let offset = header.offsetHeight

        let elementPos = select(el).offsetTop
        window.scrollTo({
          top: elementPos - offset,
          behavior: 'smooth'
        })
      }

      let selectHeader = select('.section-title')
      if (selectHeader) {
        let headerScrolled = () => {
          if (window.scrollY > 50) {
            selectHeader.classList.add('header-scrolled')
          } else {
            selectHeader.classList.remove('header-scrolled')
          }
        }
        window.addEventListener('load', headerScrolled)
        onscroll(document, headerScrolled)
      }

      let backtotop = select('.back-to-top');
      if (backtotop) {
        let toggleBacktotop = () => {
          if (window.scrollY > 100) {
            backtotop.classList.add('active');
          } else {
            backtotop.classList.remove('active');
          }
        };
        window.addEventListener('load', toggleBacktotop);
        onscroll(document, toggleBacktotop);
      }

      $(document).ready(function () {
        function setBeforeWidth() {
          document.querySelectorAll('.section-title h2').forEach(h2Element => {
            let h2Width = h2Element.offsetWidth;
            h2Element.style.setProperty('--before-width', `${h2Width}px`);
          });
        }
        setBeforeWidth();

      });
    }

    GetSiteDetails()
      .then(async () => {
        await CreateNavBar();
        await CreateSection();
        await InsertCards();
        await ApplyIndentation();
      });
</script>