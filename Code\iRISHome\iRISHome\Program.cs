using Azure.Identity; // Add this namespace for TokenCredential support
using iRISHome.DbFunctions;
using iRISHome.Models;
using JSONLogger;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authentication.OpenIdConnect;
using Microsoft.Azure.AppConfiguration.AspNetCore;
using Microsoft.Extensions.Configuration.AzureAppConfiguration;
using Microsoft.Extensions.DependencyInjection.Extensions;
using Microsoft.Extensions.Hosting;
using Microsoft.FeatureManagement;

var builder = WebApplication.CreateBuilder(args);

// Add Azure App Configuration as a configuration source FIRST
var connectionString = builder.Configuration["AppConfig:ConnectionString"];
var endpoint = builder.Configuration["AppConfig:Endpoint"];

// Add Azure App Configuration before using its values
bool azureAppConfigAdded = false;
if (!string.IsNullOrEmpty(connectionString))
{
    try
    {
        builder.Configuration.AddAzureAppConfiguration(options =>
        {
            options.Connect(connectionString)
                  .Select(KeyFilter.Any, LabelFilter.Null); // Select keys without labels
        });
        azureAppConfigAdded = true;
        Console.WriteLine("Azure App Configuration connected using connection string");

        // Add a small delay to ensure configuration is loaded
        Thread.Sleep(1000);

        // Test configuration after Azure App Config is added
        Console.WriteLine("=== TESTING CONFIGURATION AFTER AZURE APP CONFIG ===");
        var testConfig = builder.Configuration;

        // Test simple key first
        Console.WriteLine($"Test Simple Key: {testConfig["TestKey"] ?? "NOT FOUND"}");

        // Test Keycloak keys
        Console.WriteLine($"Test ClientId: {testConfig["KeycloakAuthentication:OpenIdConnect:ClientId"] ?? "NOT FOUND"}");
        Console.WriteLine($"Test ClientSecret: {(string.IsNullOrEmpty(testConfig["KeycloakAuthentication:OpenIdConnect:ClientSecret"]) ? "NOT FOUND" : "FOUND")}");
        Console.WriteLine($"Test Authority: {testConfig["KeycloakAuthentication:OpenIdConnect:Authority"] ?? "NOT FOUND"}");

        // Test if Azure App Configuration is working at all
        if (testConfig["TestKey"] == "TestValue")
        {
            Console.WriteLine("✅ Azure App Configuration is working - TestKey retrieved successfully");
        }
        else
        {
            Console.WriteLine("❌ Azure App Configuration is NOT working - TestKey not found");
        }
    }
    catch (Exception ex)
    {
        Console.WriteLine($"Failed to connect to Azure App Configuration using connection string: {ex.Message}");
    }
}
else if (!string.IsNullOrEmpty(endpoint))
{
    try
    {
        builder.Configuration.AddAzureAppConfiguration(options =>
        {
            options.Connect(new Uri(endpoint), new Azure.Identity.DefaultAzureCredential())
                  .Select(KeyFilter.Any, LabelFilter.Null); // Select keys without labels
        });
        azureAppConfigAdded = true;
        Console.WriteLine("Azure App Configuration connected using endpoint and DefaultAzureCredential");
    }
    catch (Exception ex)
    {
        Console.WriteLine($"Failed to connect to Azure App Configuration using endpoint: {ex.Message}");
    }
}

if (!azureAppConfigAdded)
{
    Console.WriteLine("Warning: Azure App Configuration not configured. Using local configuration only.");
}
else
{
    // Force reload the configuration
    Console.WriteLine("=== FORCING CONFIGURATION RELOAD ===");
    if (builder.Configuration is IConfigurationRoot reloadConfigRoot)
    {
        reloadConfigRoot.Reload();
        Console.WriteLine("Configuration reloaded");

        // Test again after reload
        Console.WriteLine($"After reload - TestKey: {builder.Configuration["TestKey"] ?? "NOT FOUND"}");
        Console.WriteLine($"After reload - ClientId: {builder.Configuration["KeycloakAuthentication:OpenIdConnect:ClientId"] ?? "NOT FOUND"}");
    }
}

builder.AddServiceDefaults();
if (azureAppConfigAdded)
{
    builder.Services.AddAzureAppConfiguration();
}

// Register DBObjects as a scoped service
builder.Services.AddScoped<DBObjects>();

builder.Services.AddControllersWithViews();
builder.Services.TryAddSingleton<IHttpContextAccessor, HttpContextAccessor>();
builder.Services.AddSession();
builder.Services.AddKendo();

// Debug: Show all configuration keys to identify the issue
Console.WriteLine("=== ALL CONFIGURATION KEYS ===");
var allKeys = builder.Configuration.AsEnumerable().ToList();
Console.WriteLine($"Total configuration keys found: {allKeys.Count}");

// Show ALL keys for comprehensive debugging
Console.WriteLine("ALL configuration keys:");
foreach (var key in allKeys)
{
    var keyName = key.Key ?? "NULL_KEY";
    var keyValue = string.IsNullOrEmpty(key.Value) ? "NULL/EMPTY" : "HAS_VALUE";
    Console.WriteLine($"  {keyName} = {keyValue}");

    // Show actual value for Keycloak keys
    if (keyName.Contains("Keycloak", StringComparison.OrdinalIgnoreCase))
    {
        Console.WriteLine($"    ACTUAL VALUE: {key.Value ?? "NULL"}");
    }
}

// Show configuration providers
if (builder.Configuration is IConfigurationRoot configRoot)
{
    Console.WriteLine("=== CONFIGURATION PROVIDERS ===");
    foreach (var provider in configRoot.Providers)
    {
        Console.WriteLine($"Provider: {provider.GetType().Name}");

        // Try to get values directly from Azure App Configuration provider
        if (provider.GetType().Name.Contains("AzureAppConfiguration"))
        {
            Console.WriteLine("  Found Azure App Configuration provider!");

            // Try to get the keys directly
            if (provider.TryGet("KeycloakAuthentication:OpenIdConnect:ClientId", out var clientId))
            {
                Console.WriteLine($"  Direct ClientId from provider: {clientId ?? "NULL"}");
            }
            else
            {
                Console.WriteLine("  Direct ClientId from provider: NOT FOUND");
            }
        }
    }
}

// Also show all keys that start with common patterns
Console.WriteLine("=== KEYS STARTING WITH COMMON PATTERNS ===");
var patterns = new[] { "KeycloakAuthentication", "Keycloak", "OpenIdConnect", "Authentication" };
foreach (var pattern in patterns)
{
    var matchingKeys = allKeys.Where(k => k.Key.StartsWith(pattern, StringComparison.OrdinalIgnoreCase)).ToList();
    if (matchingKeys.Any())
    {
        Console.WriteLine($"Pattern '{pattern}' matches:");
        foreach (var key in matchingKeys)
        {
            Console.WriteLine($"  {key.Key} = {(string.IsNullOrEmpty(key.Value) ? "NULL/EMPTY" : "HAS_VALUE")}");
        }
    }
    else
    {
        Console.WriteLine($"Pattern '{pattern}' - NO MATCHES");
    }
}

/// <summary>
/// Gets Keycloak configuration with fallback mechanism.
/// First tries to get values from Azure App Configuration, then falls back to appsettings.json if any values are missing.
/// </summary>
/// <param name="configuration">The main configuration object that includes Azure App Configuration</param>
/// <param name="azureAppConfigAdded">Whether Azure App Configuration was successfully added</param>
/// <returns>Tuple containing ClientId, ClientSecret, and Authority</returns>
static (string? clientId, string? clientSecret, string? authority) GetKeycloakConfiguration(IConfiguration configuration, bool azureAppConfigAdded)
{
    string? clientId = null;
    string? clientSecret = null;
    string? authority = null;

    // First try to get from Azure App Configuration if it's available
    if (azureAppConfigAdded)
    {
        clientId = configuration["KeycloakAuthentication:OpenIdConnect:ClientId"];
        clientSecret = configuration["KeycloakAuthentication:OpenIdConnect:ClientSecret"];
        authority = configuration["KeycloakAuthentication:OpenIdConnect:Authority"];

        Console.WriteLine("=== TRYING AZURE APP CONFIGURATION ===");
        Console.WriteLine($"Azure ClientId: {(string.IsNullOrEmpty(clientId) ? "NOT FOUND" : "FOUND")}");
        Console.WriteLine($"Azure ClientSecret: {(string.IsNullOrEmpty(clientSecret) ? "NOT FOUND" : "FOUND")}");
        Console.WriteLine($"Azure Authority: {(string.IsNullOrEmpty(authority) ? "NOT FOUND" : authority)}");
    }

    // If any value is missing from Azure App Configuration, try appsettings.json as fallback
    if (string.IsNullOrEmpty(clientId) || string.IsNullOrEmpty(clientSecret) || string.IsNullOrEmpty(authority))
    {
        Console.WriteLine("=== USING APPSETTINGS.JSON FALLBACK ===");

        // Create a new configuration builder that only reads from appsettings files
        var fallbackConfig = new ConfigurationBuilder()
            .SetBasePath(Directory.GetCurrentDirectory())
            .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
            .AddJsonFile("appsettings.Development.json", optional: true, reloadOnChange: true)
            .Build();

        var fallbackClientId = fallbackConfig["KeycloakAuthentication:OpenIdConnect:ClientId"];
        var fallbackClientSecret = fallbackConfig["KeycloakAuthentication:OpenIdConnect:ClientSecret"];
        var fallbackAuthority = fallbackConfig["KeycloakAuthentication:OpenIdConnect:Authority"];

        Console.WriteLine($"Fallback ClientId: {(string.IsNullOrEmpty(fallbackClientId) ? "NOT FOUND" : "FOUND")}");
        Console.WriteLine($"Fallback ClientSecret: {(string.IsNullOrEmpty(fallbackClientSecret) ? "NOT FOUND" : "FOUND")}");
        Console.WriteLine($"Fallback Authority: {(string.IsNullOrEmpty(fallbackAuthority) ? "NOT FOUND" : fallbackAuthority)}");

        // Use fallback values for any missing configuration
        clientId = string.IsNullOrEmpty(clientId) ? fallbackClientId : clientId;
        clientSecret = string.IsNullOrEmpty(clientSecret) ? fallbackClientSecret : clientSecret;
        authority = string.IsNullOrEmpty(authority) ? fallbackAuthority : authority;

        Console.WriteLine("=== FINAL CONFIGURATION SOURCE ===");
        Console.WriteLine($"ClientId source: {(string.Equals(configuration["KeycloakAuthentication:OpenIdConnect:ClientId"], clientId) ? "Azure App Config" : "appsettings.json")}");
        Console.WriteLine($"ClientSecret source: {(string.Equals(configuration["KeycloakAuthentication:OpenIdConnect:ClientSecret"], clientSecret) ? "Azure App Config" : "appsettings.json")}");
        Console.WriteLine($"Authority source: {(string.Equals(configuration["KeycloakAuthentication:OpenIdConnect:Authority"], authority) ? "Azure App Config" : "appsettings.json")}");
    }
    else
    {
        Console.WriteLine("=== ALL VALUES FOUND IN AZURE APP CONFIGURATION ===");
    }

    return (clientId, clientSecret, authority);
}

// Get Keycloak configuration with fallback logic
var (keycloakClientId, keycloakClientSecret, keycloakAuthority) = GetKeycloakConfiguration(builder.Configuration, azureAppConfigAdded);

Console.WriteLine($"=== KEYCLOAK CONFIGURATION VALUES ===");
Console.WriteLine($"ClientId: {(string.IsNullOrEmpty(keycloakClientId) ? "NOT SET" : "SET")}");
Console.WriteLine($"ClientSecret: {(string.IsNullOrEmpty(keycloakClientSecret) ? "NOT SET" : "SET")}");
Console.WriteLine($"Authority: {(string.IsNullOrEmpty(keycloakAuthority) ? "NOT SET" : keycloakAuthority)}");

// Test Keycloak connectivity before configuring authentication
bool keycloakAvailable = false;
if (!string.IsNullOrEmpty(keycloakClientId) && !string.IsNullOrEmpty(keycloakClientSecret) && !string.IsNullOrEmpty(keycloakAuthority))
{
    try
    {
        using var httpClient = new HttpClient();
        httpClient.Timeout = TimeSpan.FromSeconds(10);
        var wellKnownUrl = $"{keycloakAuthority}/.well-known/openid-configuration";
        Console.WriteLine($"Testing Keycloak connectivity to: {wellKnownUrl}");

        var response = await httpClient.GetAsync(wellKnownUrl);
        if (response.IsSuccessStatusCode)
        {
            keycloakAvailable = true;
            Console.WriteLine("Keycloak server is available");
        }
        else
        {
            Console.WriteLine($"Keycloak server returned: {response.StatusCode} - {response.ReasonPhrase}");
        }
    }
    catch (Exception ex)
    {
        Console.WriteLine($"Keycloak connectivity test failed: {ex.Message}");
    }
}

// Only configure authentication if we have the required values AND Keycloak is available
if (keycloakAvailable)
{
    builder.Services.AddAuthentication(options =>
    {
        options.DefaultScheme = CookieAuthenticationDefaults.AuthenticationScheme;
        options.DefaultChallengeScheme = OpenIdConnectDefaults.AuthenticationScheme;
    })
    .AddCookie()
    .AddOpenIdConnect(options =>
    {
        options.ClientId = keycloakClientId;
        options.ClientSecret = keycloakClientSecret;
        options.Authority = keycloakAuthority;
        options.ResponseType = "code";
        //options.SaveTokens = true;
        options.Scope.Add("profile");
        options.Scope.Add("email");
        options.Scope.Add("openid");

        // Add SSL certificate validation bypass for development/testing
        options.BackchannelHttpHandler = new HttpClientHandler
        {
            ServerCertificateCustomValidationCallback = (message, cert, chain, errors) => true
        };

        // Add timeout configuration
        options.BackchannelTimeout = TimeSpan.FromSeconds(30);
    });
    Console.WriteLine("Keycloak authentication configured successfully");
}
else
{
    Console.WriteLine("Warning: Keycloak authentication not configured - server unavailable or missing configuration");
    // Add basic cookie authentication as fallback
    builder.Services.AddAuthentication(CookieAuthenticationDefaults.AuthenticationScheme)
        .AddCookie();
}


var app = builder.Build();

// Add Azure App Configuration middleware only if it was successfully configured
if (azureAppConfigAdded)
{
    app.UseAzureAppConfiguration();
    Console.WriteLine("Azure App Configuration middleware added");
}

// Test configuration again after app is built
Console.WriteLine("=== CONFIGURATION AFTER APP BUILD ===");
var finalConfig = app.Configuration;
Console.WriteLine($"Final ClientId: {finalConfig["KeycloakAuthentication:OpenIdConnect:ClientId"] ?? "NOT FOUND"}");
Console.WriteLine($"Final ClientSecret: {(string.IsNullOrEmpty(finalConfig["KeycloakAuthentication:OpenIdConnect:ClientSecret"]) ? "NOT FOUND" : "FOUND")}");
Console.WriteLine($"Final Authority: {finalConfig["KeycloakAuthentication:OpenIdConnect:Authority"] ?? "NOT FOUND"}");

// Show all configuration keys after build
var finalKeys = finalConfig.AsEnumerable().Where(k => k.Key.Contains("Keycloak", StringComparison.OrdinalIgnoreCase)).ToList();
Console.WriteLine($"Final Keycloak keys count: {finalKeys.Count}");
foreach (var key in finalKeys)
{
    Console.WriteLine($"  Final Key: {key.Key} = {(string.IsNullOrEmpty(key.Value) ? "NULL/EMPTY" : "HAS_VALUE")}");
}

app.MapDefaultEndpoints();

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())  
{
    app.UseExceptionHandler("/Home/Error");
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}
app.UseSession();

app.UseHttpsRedirection();
app.UseStaticFiles();

app.UseRouting();
app.UseAuthentication();
app.UseAuthorization();

// Add debug route for configuration testing
app.MapGet("/config-debug", async context =>
{
    var html = await System.IO.File.ReadAllTextAsync("Views/Shared/ConfigDebug.cshtml");
    context.Response.ContentType = "text/html";
    await context.Response.WriteAsync(html);
});

app.MapControllerRoute(
    name: "default",
    pattern: "{controller=AuthLogin}/{action=KeycloakValidation}/{id?}");

app.Run();

// Note: The provided code block is a NuGet package installation command and cannot be directly incorporated into the C# file. 
// To install the package, run the command `Install-Package Telerik.UI.for.AspNet.Core -Version 2024.1.130` in the NuGet Package Manager Console.
