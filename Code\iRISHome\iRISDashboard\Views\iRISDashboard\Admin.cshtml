﻿@using iRISDashboard.Models
@model List<SettingsViewModel>

@{
    ViewBag.Title = "Manage App Settings";
    ViewBag.pTitle = "Manage App Settings";
    ViewBag.pageTitle = "Mobile Aspects";
    ViewBag.BrandName = "iRISHome";
    Layout = "~/Views/Shared/_Layout.cshtml";
}


<div class="row">
    @foreach (var card in Model)
    {
        <div class="col-xl-4 col-sm-6">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex">
                        <div class="flex-shrink-0 me-4">
                            <div class="avatar-md">
                                <span class="avatar-title rounded-circle bg-info text-white font-size-16">

                                    <span class="@card.SettingIcon" style="font-size: 52px;"></span>
                                </span>
                            </div>
                        </div>
                        <div class="flex-grow-1 overflow-hidden">
                            <h5 title="Click to manage settings." class="text-truncate font-size-15"><a href="@card.LinkToConfigure" class="text-info">@card.SettingName</a></h5>
                            <p class="text-muted mb-4">@card.Description</p>

                        </div>
                    </div>
                </div>
            </div>
        </div>
    }

</div>


@* <div class="row">
    <div class="col-xl-4 col-sm-6">
        <div class="card">
            <div class="card-body">
                <div class="d-flex">
                    <div class="flex-shrink-0 me-4">
                        <div class="avatar-md">
                            <span class="avatar-title rounded-circle bg-info text-white font-size-16">

                                <span class="bx bx-lock" style="font-size: 52px;"></span>
                            </span>
                        </div>
                    </div>
                    <div class="flex-grow-1 overflow-hidden">
                        <h5 title="Click to manage settings." class="text-truncate font-size-15"><a href="/LoginSettings" class="text-info">Login Settings</a></h5>
                        <p class="text-muted mb-4">Manage login features like two factor auth etc.</p>

                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-4 col-sm-6">
        <div class="card">
            <div class="card-body">
                <div class="d-flex">
                    <div class="flex-shrink-0 me-4">
                        <div class="avatar-md">
                            <span class="avatar-title rounded-circle bg-info text-white font-size-16">

                                <span class="fa fa-lightbulb" style="font-size: 52px;"></span>
                            </span>
                        </div>
                    </div>
                    <div class="flex-grow-1 overflow-hidden">
                        <h5 title="Click to manage settings." class="text-truncate font-size-15"><a href="/ManageModules" class="text-info">Application Modules</a></h5>
                        <p class="text-muted mb-4">Manage console and web application modules.</p>

                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-4 col-sm-6">
        <div class="card">
            <div class="card-body">
                <div class="d-flex">
                    <div class="flex-shrink-0 me-4">
                        <div class="avatar-md">
                            <span class="avatar-title rounded-circle bg-info text-white font-size-16">

                                <span class="fa fa-user-tie" style="font-size: 52px;"></span>
                            </span>
                        </div>
                    </div>
                    <div class="flex-grow-1 overflow-hidden">
                        <h5 title="Click to manage settings." class="text-truncate font-size-15"><a href="/Settings?cat=c2M6cE2CcWM9LbYK2vfzxg==" class="text-info">Application Setting-Administration</a></h5>
                        <p class="text-muted mb-4">Manage console and web app Administration settings.</p>

                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-4 col-sm-6">
        <div class="card">
            <div class="card-body">
                <div class="d-flex">
                    <div class="flex-shrink-0 me-4">
                        <div class="avatar-md">
                            <span class="avatar-title rounded-circle bg-info text-white font-size-16">

                                <span class="bx bx-filter-alt" style="font-size: 52px;"></span>
                            </span>
                        </div>
                    </div>
                    <div class="flex-grow-1 overflow-hidden">
                        <h5 title="Click to manage settings." class="text-truncate font-size-15"><a href="/Settings?cat=Tpq1GjEgbx4jSfPUhFTJNg==" class="text-info">Application Setting-Filter</a></h5>
                        <p class="text-muted mb-4">Manage console and web app Filter settings.</p>

                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-4 col-sm-6">
        <div class="card">
            <div class="card-body">
                <div class="d-flex">
                    <div class="flex-shrink-0 me-4">
                        <div class="avatar-md">
                            <span class="avatar-title rounded-circle bg-info text-white font-size-16">

                                <span class="bx bx-list-check" style="font-size: 52px;"></span>
                            </span>
                        </div>
                    </div>
                    <div class="flex-grow-1 overflow-hidden">
                        <h5 title="Click to manage settings." class="text-truncate font-size-15"><a href="/Settings?cat=ghAtSouLTnNQCi11uZ1pJg==" class="text-info">Application Setting-General</a></h5>
                        <p class="text-muted mb-4">Manage console and web app General settings.</p>

                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-xl-4 col-sm-6">
        <div class="card">
            <div class="card-body">
                <div class="d-flex">
                    <div class="flex-shrink-0 me-4">
                        <div class="avatar-md">
                            <span class="avatar-title rounded-circle bg-info text-white font-size-16">

                                <span class="bx bx-cog" style="font-size: 52px;"></span>
                            </span>
                        </div>
                    </div>
                    <div class="flex-grow-1 overflow-hidden">
                        <h5 title="Click to manage settings." class="text-truncate font-size-15"><a href="/Settings?cat=Gn4lKl1MWm0JgvZxaT/LCw==" class="text-info">Application Setting-Operation</a></h5>
                        <p class="text-muted mb-4">Manage console and web app Operation settings.</p>

                    </div>
                </div>
            </div>
        </div>
    </div>

</div> *@

@section scripts {
    <script src="~/assets/js/app.js"></script>
}