let SiteDetailsArray = [];
let ProjectNamesArray = [];

async function GetSiteDetails(JsonFilePath = './ApplicationInfo.json') {
  try {
    const response = await fetch(`${JsonFilePath}?${new Date().getTime()}`);
    if (!response.ok) {
      throw new Error(`Failed to fetch data from ${JsonFilePath}`);
    }
    const jsonData = await response.json();
    SiteDetailsArray.push(...jsonData);
    SiteDetailsArray.forEach(element => {
      if (!(ProjectNamesArray.includes(element.Project))) {
        ProjectNamesArray.push(element.Project);
      }
    });
  } catch (error) {
    console.error(error);
  }
}

async function CreateNavBar() {
  const cardContainer = document.getElementsByClassName('NavBarList')[0];
  let htmlContent = "";
  let count = 0;
  let navbarcard = "";
  ProjectNamesArray.forEach(element => {
    let IdName = element.replace(/ /g, "");
    if (count == 0) { navbarcard = `<li><a class="nav-link scrollto active" href="#${IdName}">${element}</a></li>` }
    else { navbarcard = `<li><a class="nav-link scrollto" href="#${IdName}">${element}</a></li>` }
    htmlContent += navbarcard;
    count++;
  });
  cardContainer.innerHTML = htmlContent;
}

async function CreateSection() {
  const cardContainer = document.getElementById("services");
  let htmlContent = '';
  let count = 0;
  let section = '';
  ProjectNamesArray.forEach(element => {
    if (count == 0) { section = `<section id="${element}" class="mb-5 pt-5">` }
    else { section = `<section id="${element}" class="mb-5">` }
    htmlContent += `
        ${section}
            <div class="container">
            <div class="section-title">
                <h2>${element}</h2>
            </div>
            <div class="row d-flex justify-content-center" id="${element}CardDiv">
            </div>
            </div>
        </section>
        `;
    count++;
  });
  cardContainer.innerHTML = htmlContent;
}

async function InsertCards() {
  let projectCardContent = {};

  SiteDetailsArray.forEach(element => {
    let anchorOpenTag = "", anchorCloseTag = "", rdpOPenTag="",rdpCloseTag="";
    if (element.AppType == "Web") { anchorOpenTag = `<a href="${element.AppLink}" target="_blank">`; anchorCloseTag = "</a>" }
    else{rdpOPenTag=`<a href="./irisdemoserver1.rdp" download="irisdemoserver1.rdp">`; rdpCloseTag=`</a>`}
    let descriptionWords = element.Description.split(' ');
    if (descriptionWords.length > 12) {
      descriptionWords.splice(12, 0, '<span id="dotText" class="fs-5">...</span><span id="moreText">');
      descriptionWords.push('</span>');
    }
    let modifiedDescription = descriptionWords.join(' ');
    let htmlContent = `
    <div class="col-lg-4 col-md-6 d-flex align-items-stretch mb-3">
      ${rdpOPenTag}
        ${anchorOpenTag}
          <div class="icon-box">
              <div class="icon"><i class=""><img style="height: 50px;" src="${element.ImgURL}" alt=""></i></div>
              <h4>${element.AppName}</h4>
              <p>${modifiedDescription}</p>
          </div>
          ${anchorCloseTag}
        ${rdpCloseTag}
    </div>`;

    if (!projectCardContent[element.Project]) {
      projectCardContent[element.Project] = '';
    }
    projectCardContent[element.Project] += htmlContent;
  });

  for (let project in projectCardContent) {
    const cardContainer = document.getElementById(project + 'CardDiv');
    if (cardContainer) {
      cardContainer.innerHTML = projectCardContent[project];
    }
  }

}

async function ApplyIndentation() {

  const select = (el, all = false) => {
    el = el.trim();
    if (all) {
      return [...document.querySelectorAll(el)];
    } else {
      return document.querySelector(el);
    }
  };

  const on = (type, el, listener, all = false) => {
    let selectEl = select(el, all);
    if (selectEl) {
      if (all) {
        selectEl.forEach(e => e.addEventListener(type, listener));
      } else {
        selectEl.addEventListener(type, listener);
      }
    }
  };

  const onscroll = (el, listener) => {
    el.addEventListener('scroll', listener);
  };

  let navbarlinks = select('#navbar .scrollto', true)
  const navbarlinksActive = () => {
    let position = window.scrollY + 300
    navbarlinks.forEach(navbarlink => {
      if (!navbarlink.hash) return
      let section = select(navbarlink.hash)
      if (!section) return
      if (position >= section.offsetTop && position <= (section.offsetTop + section.offsetHeight)) {
        navbarlink.classList.add('active')
      } else {
        navbarlink.classList.remove('active')
      }
    })
  }
  window.addEventListener('load', navbarlinksActive)
  onscroll(document, navbarlinksActive)


  const scrollto = (el) => {
    let header = select('.section-title')
    let offset = header.offsetHeight

    let elementPos = select(el).offsetTop
    window.scrollTo({
      top: elementPos - offset,
      behavior: 'smooth'
    })
  }

  let selectHeader = select('.section-title')
  if (selectHeader) {
    const headerScrolled = () => {
      if (window.scrollY > 50) {
        selectHeader.classList.add('header-scrolled')
      } else {
        selectHeader.classList.remove('header-scrolled')
      }
    }
    window.addEventListener('load', headerScrolled)
    onscroll(document, headerScrolled)
  }

  let backtotop = select('.back-to-top');
  if (backtotop) {
    const toggleBacktotop = () => {
      if (window.scrollY > 100) {
        backtotop.classList.add('active');
      } else {
        backtotop.classList.remove('active');
      }
    };
    window.addEventListener('load', toggleBacktotop);
    onscroll(document, toggleBacktotop);
  }

  $(document).ready(function () {
    function setBeforeWidth() {
      document.querySelectorAll('.section-title h2').forEach(h2Element => {
        const h2Width = h2Element.offsetWidth;
        h2Element.style.setProperty('--before-width', `${h2Width}px`);
      });
    }
    setBeforeWidth();

  });
}

GetSiteDetails()
  .then(async () => {
    await CreateNavBar();
    await CreateSection();
    await InsertCards();
    await ApplyIndentation();
  });