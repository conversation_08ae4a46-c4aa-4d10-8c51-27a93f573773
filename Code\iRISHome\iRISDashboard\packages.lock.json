{"version": 2, "dependencies": {"net9.0": {"Microsoft.Data.SqlClient": {"type": "Direct", "requested": "[5.2.0, )", "resolved": "5.2.0", "contentHash": "3alfyqRN3ELRtdvU1dGtLBRNQqprr3TJ0WrUJfMISPwg1nPUN2P3Lelah68IKWuV27Ceb7ig95hWNHFTSXfxMg==", "dependencies": {"Azure.Identity": "1.10.3", "Microsoft.Data.SqlClient.SNI.runtime": "5.2.0", "Microsoft.Identity.Client": "4.56.0", "Microsoft.IdentityModel.JsonWebTokens": "6.35.0", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "6.35.0", "Microsoft.SqlServer.Server": "1.0.0", "System.Configuration.ConfigurationManager": "8.0.0", "System.Runtime.Caching": "8.0.0"}}, "Microsoft.Data.Sqlite.Core": {"type": "Direct", "requested": "[9.0.6, )", "resolved": "9.0.6", "contentHash": "3auiudiViGzj1TidUdjuDqtP3+f6PBk4xdw6r9sBaTtkYoGc3AZn0cP8LgYZaLRnJBqY5bXRLB+qhjoB+iATzA==", "dependencies": {"SQLitePCLRaw.core": "2.1.10"}}, "Microsoft.Identity.Client": {"type": "Direct", "requested": "[4.60.4, )", "resolved": "4.60.4", "contentHash": "7gLb5KF/tJMIoO891C8Ij7kaj5EoU1gTazdYPUZgkEhIZQ6ii74Md8mJXMDvX9fSb3EUQ4lyex5pI0jqpCJivw==", "dependencies": {"Microsoft.IdentityModel.Abstractions": "6.35.0", "System.Diagnostics.DiagnosticSource": "6.0.1"}}, "Newtonsoft.Json": {"type": "Direct", "requested": "[13.0.3, )", "resolved": "13.0.3", "contentHash": "HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ=="}, "Serilog.Enrichers.Process": {"type": "Direct", "requested": "[2.0.2, )", "resolved": "2.0.2", "contentHash": "T9EjKKLsL6qC/3eOLUAKEPBLEqPDmt5BLXaQdPMaxJzuex+MeXA8DuAiPboUaftp3kbnCN4ZgZpDvs+Fa7OHuw==", "dependencies": {"Serilog": "2.3.0"}}, "Serilog.Enrichers.Thread": {"type": "Direct", "requested": "[3.1.0, )", "resolved": "3.1.0", "contentHash": "85lWsGRJpRxvKT6j/H67no55SUBsBIvp556TKuBTGhjtoPeq+L7j/sDWbgAtvT0p7u7/phJyX6j35PQ4Vtqw0g==", "dependencies": {"Serilog": "2.3.0"}}, "SerilogTimings": {"type": "Direct", "requested": "[3.0.1, )", "resolved": "3.0.1", "contentHash": "Zs28eTgszAMwpIrbBnWHBI50yuxL50p/dmAUWmy75+axdZYK/Sjm5/5m1N/CisR8acJUhTVcjPZrsB1P5iv0Uw==", "dependencies": {"Serilog": "2.10.0"}}, "System.Configuration.ConfigurationManager": {"type": "Direct", "requested": "[8.0.0, )", "resolved": "8.0.0", "contentHash": "JlYi9XVvIREURRUlGMr1F6vOFLk7YSY4p1vHo4kX3tQ0AGrjqlRWHDi66ImHhy6qwXBG3BJ6Y1QlYQ+Qz6Xgww==", "dependencies": {"System.Diagnostics.EventLog": "8.0.0", "System.Security.Cryptography.ProtectedData": "8.0.0"}}, "System.IdentityModel.Tokens.Jwt": {"type": "Direct", "requested": "[8.0.1, )", "resolved": "8.0.1", "contentHash": "GJw3bYkWpOgvN3tJo5X4lYUeIFA2HD293FPUhKmp7qxS+g5ywAb34Dnd3cDAFLkcMohy5XTpoaZ4uAHuw0uSPQ==", "dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "8.0.1", "Microsoft.IdentityModel.Tokens": "8.0.1"}}, "Azure.Core": {"type": "Transitive", "resolved": "1.35.0", "contentHash": "hENcx03Jyuqv05F4RBEPbxz29UrM3Nbhnr6Wl6NQpoU9BCIbL3XLentrxDCTrH54NLS11Exxi/o8MYgT/cnKFA==", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "1.1.1", "System.Diagnostics.DiagnosticSource": "6.0.1", "System.Memory.Data": "1.0.2", "System.Numerics.Vectors": "4.5.0", "System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "4.7.2", "System.Threading.Tasks.Extensions": "4.5.4"}}, "Azure.Identity": {"type": "Transitive", "resolved": "1.10.3", "contentHash": "l1Xm2MWOF2Mzcwuarlw8kWQXLZk3UeB55aQXVyjj23aBfDwOZ3gu5GP2kJ6KlmZeZv2TCzw7x4L3V36iNr3gww==", "dependencies": {"Azure.Core": "1.35.0", "Microsoft.Identity.Client": "4.56.0", "Microsoft.Identity.Client.Extensions.Msal": "4.56.0", "System.Memory": "4.5.4", "System.Security.Cryptography.ProtectedData": "4.7.0", "System.Text.Json": "4.7.2", "System.Threading.Tasks.Extensions": "4.5.4"}}, "Microsoft.Bcl.AsyncInterfaces": {"type": "Transitive", "resolved": "1.1.1", "contentHash": "yuvf07qFWFqtK3P/MRkEKLhn5r2UbSpVueRziSqj0yJQIKFwG1pq9mOayK3zE5qZCTs0CbrwL9M6R8VwqyGy2w=="}, "Microsoft.Bcl.TimeProvider": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "f5Kr5JepAbiGo7uDmhgvMqhntwxqXNn6/IpTBSSI4cuHhgnJGrLxFRhMjVpRkLPp6zJXO0/G0l3j9p9zSJxa+w=="}, "Microsoft.Data.SqlClient.SNI.runtime": {"type": "Transitive", "resolved": "5.2.0", "contentHash": "po1jhvFd+8pbfvJR/puh+fkHi0GRanAdvayh/0e47yaM6CXWZ6opUjCMFuYlAnD2LcbyvQE7fPJKvogmaUcN+w=="}, "Microsoft.Extensions.AmbientMetadata.Application": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "KW4QZv6zVfytBV15Dflu2dwcR9IKiMeS5kwp+vuWfM+bDi8wAd8KdJ8xfMaX3xiIN/Futw/Kpzf01TA8ALRbXw==", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0"}}, "Microsoft.Extensions.Compliance.Abstractions": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "FZ+Y66GF0VwvPgQSmMMNNUxEtjy3Nh9r2iTvZW+UXUtHPmdtXg85NtVYYihhR8guNNQ0LZgfioxiRRjbMECfvw==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.ObjectPool": "8.0.0"}}, "Microsoft.Extensions.Configuration": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "0J/9YNXTMWSZP2p2+nvl8p71zpSwokZXZuJW+VjdErkegAnFdO1XlqtA62SJtgVYHdKu3uPxJHcMR/r35HwFBA==", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Configuration.Abstractions": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "3lE/iLSutpgX1CC0NOW70FJoGARRHbyKmG7dc0klnUZ9Dd9hS6N/POPWhKhMLCEuNN5nXEY5agmlFtH562vqhQ==", "dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Configuration.Binder": {"type": "Transitive", "resolved": "8.0.1", "contentHash": "2UKFJnLiBt7Od6nCnTqP9rTIUNhzmn9Hv1l2FchyKbz8xieB9ULwZTbQZMw+M24Qw3F5dzzH1U9PPleN0LNLOQ==", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}}, "Microsoft.Extensions.DependencyInjection": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "V8S3bsm50ig6JSyrbcJJ8bW2b9QLGouz+G1miK3UTaOWmMtFwNNNzUf4AleyDWUmTrWMLNnFSLEQtxmxgNQnNQ==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"type": "Transitive", "resolved": "8.0.1", "contentHash": "fGLiCRLMYd00JYpClraLjJTNKLmMJPnqxMaiRzEBIIvevlzxz33mXy39Lkd48hu1G+N21S7QpaO5ZzKsI6FRuA=="}, "Microsoft.Extensions.DependencyInjection.AutoActivation": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "NuHa4iADMRvcfaHCGV2ZDPwx5cn5GsA/bSkOnwBFkkdpgIsNounqYtyfuorJMwtxHjqGeWEhBLzPHvd+Oemjow==", "dependencies": {"Microsoft.Extensions.Hosting.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}}, "Microsoft.Extensions.DiagnosticAdapter": {"type": "Transitive", "resolved": "3.1.32", "contentHash": "oDv3wt+Q5cmaSfOQ3Cdu6dF6sn/x5gzWdNpOq4ajBwCMWYBr6CchncDvB9pF83ORlbDuX32MsVLOPGPxW4Lx4g==", "dependencies": {"System.Diagnostics.DiagnosticSource": "4.7.1"}}, "Microsoft.Extensions.Diagnostics": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "3PZp/YSkIXrF7QK7PfC1bkyRYwqOHpWFad8Qx+4wkuumAeXo1NHaxpS9LboNA9OvNSAu+QOVlXbMyoY+pHSqcw==", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Diagnostics.Abstractions": "8.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0"}}, "Microsoft.Extensions.Diagnostics.Abstractions": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "JHYCQG7HmugNYUhOl368g+NMxYE/N/AiclCYRNlgCY9eVyiBkOHMwK4x60RYMxv9EL3+rmj1mqHvdCiPpC+D4Q==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "System.Diagnostics.DiagnosticSource": "8.0.0"}}, "Microsoft.Extensions.Diagnostics.ExceptionSummarization": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "3gTLqY8tsiflTXweUdLy4pFj15x3kuGSglhem6UM3FZaui+Bcee7PT/tkV3WMcBRwQ2tsBEAS8XuaSEcbvGLPQ==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}}, "Microsoft.Extensions.Features": {"type": "Transitive", "resolved": "8.0.4", "contentHash": "ym56/tUtFHFHucqiyQq42Ha6n1CT3CkrSZmbb3Szfkl/KNEaNqotKXFQMWHlFk2WMvU4fepqUsW8VK5WEjMChA=="}, "Microsoft.Extensions.FileProviders.Abstractions": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "ZbaMlhJlpisjuWbvXr4LdAst/1XxH3vZ6A0BsgTphZ2L4PGuxRLz7Jr/S7mkAAnOn78Vu0fKhEgNF5JO3zfjqQ==", "dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Hosting.Abstractions": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "AG7HWwVRdCHlaA++1oKDxLsXIBxmDpMPb3VoyOoAghEWnkUvEAdYQUwnV4jJbAaa/nMYNiEh5ByoLauZBEiovg==", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Diagnostics.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0"}}, "Microsoft.Extensions.Http": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "cWz4caHwvx0emoYe7NkHPxII/KkTI8R/LC9qdqJqnKv2poTJ4e2qqPGQqvRoQ5kaSA4FU5IV3qFAuLuOhoqULQ==", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Diagnostics": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}}, "Microsoft.Extensions.Http.Diagnostics": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "OtaXg8tmoz47bf9M+9J9b9H4e1Wdr7mabSjQ+74eUiBAoIoddA59XtDIli5PcsVAjybzreTFEgOM+aw8/0U6FA==", "dependencies": {"Microsoft.Extensions.DependencyInjection.AutoActivation": "8.0.0", "Microsoft.Extensions.DiagnosticAdapter": "3.1.32", "Microsoft.Extensions.Http": "8.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0", "Microsoft.Extensions.Telemetry": "8.0.0", "Microsoft.IO.RecyclableMemoryStream": "2.3.2", "System.Text.Json": "8.0.0"}}, "Microsoft.Extensions.Logging": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "tvRkov9tAJ3xP51LCv3FJ2zINmv1P8Hi8lhhtcKGqM+ImiTCC84uOPEI4z8Cdq2C3o9e+Aa0Gw0rmrsJD77W+w==", "dependencies": {"Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}}, "Microsoft.Extensions.Logging.Abstractions": {"type": "Transitive", "resolved": "8.0.1", "contentHash": "RIFgaqoaINxkM2KTOw72dmilDmTrYA0ns2KW4lDz4gZ2+o6IQ894CzmdL3StM2oh7QQq44nCWiqKqc4qUI9Jmg==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.1"}}, "Microsoft.Extensions.Logging.Configuration": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "ixXXV0G/12g6MXK65TLngYN9V5hQQRuV+fZi882WIoVJT7h5JvoYoxTEwCgdqwLjSneqh1O+66gM8sMr9z/rsQ==", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0"}}, "Microsoft.Extensions.ObjectPool": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "4pm+XgxSukskwjzDDfSjG4KNUIOdFF2VaqZZDtTzoyQMOVSnlV6ZM8a9aVu5dg9LVZTB//utzSc8fOi0b0Mb2Q=="}, "Microsoft.Extensions.Options": {"type": "Transitive", "resolved": "8.0.2", "contentHash": "dWGKvhFybsaZpGmzkGCbNNwBD1rVlWzrZKANLW/CcbFJpCEceMCGzT7zZwHOGBCbwM0SzBuceMj5HN1LKV1QqA==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Options.ConfigurationExtensions": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "0f4DMRqEd50zQh+UyJc+/HiBsZ3vhAQALgdkcQEalSH1L2isdC7Yj54M3cyo5e+BeO5fcBQ7Dxly8XiBBcvRgw==", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Primitives": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "bXJEZrW9ny8vjMF1JV253WeLhpEVzFo1lyaZu1vQ4ZxWUlVvknZ/+ftFgVheLubb4eZPSwwxBeqS1JkCOjxd8g=="}, "Microsoft.Extensions.Resilience": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "QqpjuGYLQPd3vueN6dP5QAFlZRm+rE7GBSWu2h9EwJHt9A/YnO1iM3eJAUNajQ/OL8oqVn4VequbybTirY6vWw==", "dependencies": {"Microsoft.Extensions.Diagnostics": "8.0.0", "Microsoft.Extensions.Diagnostics.ExceptionSummarization": "8.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0", "Microsoft.Extensions.Telemetry.Abstractions": "8.0.0", "Polly.Core": "8.0.0", "Polly.Extensions": "8.0.0", "Polly.RateLimiting": "8.0.0"}}, "Microsoft.Extensions.ServiceDiscovery.Abstractions": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "db0gjxG8qLtEkSYBSOafxVr5v1Lf7O4dbWKhlj0uMB6dK6Y8KLQz3YBtxXDpmtww1mIzrHlzdWA+jN1dKjerkA==", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.1", "Microsoft.Extensions.Features": "8.0.4", "Microsoft.Extensions.Logging.Abstractions": "8.0.1", "Microsoft.Extensions.Options": "8.0.2", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Telemetry.Abstractions": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "lsFZSSPUO3KVHuiVUHB+HM4OF6dZywXlgab8S5Wh9I2GeXJPXAxWU7SX0wPiqdTkmH3J9z3bUu2lOwrPTXQsjQ==", "dependencies": {"Microsoft.Extensions.Compliance.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.ObjectPool": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "System.Diagnostics.DiagnosticSource": "8.0.0"}}, "Microsoft.Identity.Client.Extensions.Msal": {"type": "Transitive", "resolved": "4.56.0", "contentHash": "H12YAzEGK55vZ+QpxUzozhW8ZZtgPDuWvgA0JbdIR9UhMUplj29JhIgE2imuH8W2Nw9D8JKygR1uxRFtpSNcrg==", "dependencies": {"Microsoft.Identity.Client": "4.56.0", "System.IO.FileSystem.AccessControl": "5.0.0", "System.Security.Cryptography.ProtectedData": "4.5.0"}}, "Microsoft.IdentityModel.Abstractions": {"type": "Transitive", "resolved": "8.0.1", "contentHash": "OtlIWcyX01olfdevPKZdIPfBEvbcioDyBiE/Z2lHsopsMD7twcKtlN9kMevHmI5IIPhFpfwCIiR6qHQz1WHUIw=="}, "Microsoft.IdentityModel.JsonWebTokens": {"type": "Transitive", "resolved": "8.0.1", "contentHash": "s6++gF9x0rQApQzOBbSyp4jUaAlwm+DroKfL8gdOHxs83k8SJfUXhuc46rDB3rNXBQ1MVRxqKUrqFhO/M0E97g==", "dependencies": {"Microsoft.IdentityModel.Tokens": "8.0.1"}}, "Microsoft.IdentityModel.Logging": {"type": "Transitive", "resolved": "8.0.1", "contentHash": "UCPF2exZqBXe7v/6sGNiM6zCQOUXXQ9+v5VTb9gPB8ZSUPnX53BxlN78v2jsbIvK9Dq4GovQxo23x8JgWvm/Qg==", "dependencies": {"Microsoft.IdentityModel.Abstractions": "8.0.1"}}, "Microsoft.IdentityModel.Protocols": {"type": "Transitive", "resolved": "6.35.0", "contentHash": "BPQhlDzdFvv1PzaUxNSk+VEPwezlDEVADIKmyxubw7IiELK18uJ06RQ9QKKkds30XI+gDu9n8j24XQ8w7fjWcg==", "dependencies": {"Microsoft.IdentityModel.Logging": "6.35.0", "Microsoft.IdentityModel.Tokens": "6.35.0"}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect": {"type": "Transitive", "resolved": "6.35.0", "contentHash": "LMtVqnECCCdSmyFoCOxIE5tXQqkOLrvGrL7OxHg41DIm1bpWtaCdGyVcTAfOQpJXvzND9zUKIN/lhngPkYR8vg==", "dependencies": {"Microsoft.IdentityModel.Protocols": "6.35.0", "System.IdentityModel.Tokens.Jwt": "6.35.0"}}, "Microsoft.IdentityModel.Tokens": {"type": "Transitive", "resolved": "8.0.1", "contentHash": "kDimB6Dkd3nkW2oZPDkMkVHfQt3IDqO5gL0oa8WVy3OP4uE8Ij+8TXnqg9TOd9ufjsY3IDiGz7pCUbnfL18tjg==", "dependencies": {"Microsoft.IdentityModel.Logging": "8.0.1"}}, "Microsoft.IO.RecyclableMemoryStream": {"type": "Transitive", "resolved": "2.3.2", "contentHash": "Oh1qXXFdJFcHozvb4H6XYLf2W0meZFuG0A+TfapFPj9z5fd4vxiARGEhAaLj/6XWQaMYIv4SH/9Q6H78Hw0E2Q=="}, "Microsoft.NETCore.Platforms": {"type": "Transitive", "resolved": "5.0.0", "contentHash": "VyPlqzH2wavqquTcYpkIIAQ6WdenuKoFN0BdYBbCWsclXacSOHNQn66Gt4z5NBqEYW0FAPm5rlvki9ZiCij5xQ=="}, "Microsoft.SqlServer.Server": {"type": "Transitive", "resolved": "1.0.0", "contentHash": "N4KeF3cpcm1PUHym1RmakkzfkEv3GRMyofVv40uXsQhCQeglr2OHNcUk2WOG51AKpGO8ynGpo9M/kFXSzghwug=="}, "OpenTelemetry": {"type": "Transitive", "resolved": "1.7.0", "contentHash": "HNmOJg++4FtEJGCIn1dCJcDkHRLNuLKU047owrGXUOfz/C/c5oZHOPKrowKVOy2jOOh/F9+HDshzeOk5NnQEZg==", "dependencies": {"Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Configuration": "8.0.0", "OpenTelemetry.Api.ProviderBuilderExtensions": "1.7.0"}}, "OpenTelemetry.Api": {"type": "Transitive", "resolved": "1.8.0", "contentHash": "+daN4OqIXne3QLlFHEzb6ybAETgKs7Hg5jINYT5P8p8A/cEtuP6CRDYdDpOe6AlW8oAaV/nJJ5tLyPUVKKQu0w==", "dependencies": {"System.Diagnostics.DiagnosticSource": "8.0.0"}}, "OpenTelemetry.Api.ProviderBuilderExtensions": {"type": "Transitive", "resolved": "1.8.0", "contentHash": "BLo2IwO+sJZMsedvKyMtDJL1Pk7gto2B5lf0rkGKigihUUYDaWwh7HTYtftTamXn0UthCB9B+VUaF7h9AohXMg==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "OpenTelemetry.Api": "1.8.0"}}, "Polly.Core": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "g+8HnRjFTs8Ujz4Si4slrVrEK9M92DjvF75WvNpnF/lbT8M3/xW+tuD2udTiv2KuOsFOsRfOcZ12PddizrtXVA=="}, "Polly.Extensions": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "XGasiRYj4ecCLBrIH/caPRkrUb891KbNYSLB+JFy/wh7wVt8Su7dNqhiP7FjcJXTa+0MA96UR7l0uFmhSUSaYQ==", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": "6.0.0", "Microsoft.Extensions.Options": "6.0.0", "Polly.Core": "8.0.0", "System.Diagnostics.DiagnosticSource": "7.0.0"}}, "Polly.RateLimiting": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "x4GgLxNcSLILTyjLJxUItbfr4HyPq5DhMh8FpY8CZDCf4wrGXZtHzaD6NjNB93oVEZxntYv4rpx0SDMAdj3Nqw==", "dependencies": {"Polly.Core": "8.0.0", "System.Threading.RateLimiting": "7.0.0"}}, "Serilog": {"type": "Transitive", "resolved": "2.10.0", "contentHash": "+QX0hmf37a0/OZLxM3wL7V6/ADvC1XihXN4Kq/p6d8lCPfgkRdiuhbWlMaFjR9Av0dy5F0+MBeDmDdRZN/YwQA=="}, "SQLitePCLRaw.core": {"type": "Transitive", "resolved": "2.1.10", "contentHash": "Ii8JCbC7oiVclaE/mbDEK000EFIJ+ShRPwAvvV89GOZhQ+ZLtlnSWl6ksCNMKu/VGXA4Nfi2B7LhN/QFN9oBcw==", "dependencies": {"System.Memory": "4.5.3"}}, "System.Diagnostics.DiagnosticSource": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "c9xLpVz6PL9lp/djOWtk5KPDZq3cSYpmXoJQY524EOtuFl5z9ZtsotpsyrDW40U1DRnQSYvcPKEUV0X//u6gkQ=="}, "System.Diagnostics.EventLog": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "fdYxcRjQqTTacKId/2IECojlDSFvp7LP5N78+0z/xH7v/Tuw5ZAxu23Y6PTCRinqyu2ePx+Gn1098NC6jM6d+A=="}, "System.IO.FileSystem.AccessControl": {"type": "Transitive", "resolved": "5.0.0", "contentHash": "SxHB3nuNrpptVk+vZ/F+7OHEpoHUIKKMl02bUmYHQr1r+glbZQxs7pRtsf4ENO29TVm2TH3AEeep2fJcy92oYw==", "dependencies": {"System.Security.AccessControl": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "System.Memory": {"type": "Transitive", "resolved": "4.5.4", "contentHash": "1MbJTHS1lZ4bS4FmsJjnuGJOu88ZzTT2rLvrhW7Ygic+pC0NWA+3hgAen0HRdsocuQXCkUTdFn9yHJJhsijDXw=="}, "System.Memory.Data": {"type": "Transitive", "resolved": "1.0.2", "contentHash": "JGkzeqgBsiZwKJZ1IxPNsDFZDhUvuEdX8L8BDC8N3KOj+6zMcNU28CNN59TpZE/VJYy9cP+5M+sbxtWJx3/xtw==", "dependencies": {"System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "4.6.0"}}, "System.Numerics.Vectors": {"type": "Transitive", "resolved": "4.5.0", "contentHash": "QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ=="}, "System.Runtime.Caching": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "4TmlmvGp4kzZomm7J2HJn6IIx0UUrQyhBDyb5O1XiunZlQImXW+B8b7W/sTPcXhSf9rp5NR5aDtQllwbB5elOQ==", "dependencies": {"System.Configuration.ConfigurationManager": "8.0.0"}}, "System.Security.AccessControl": {"type": "Transitive", "resolved": "5.0.0", "contentHash": "dagJ1mHZO3Ani8GH0PHpPEe/oYO+rVdbQjvjJkBRNQkX4t0r1iaeGn8+/ybkSLEan3/slM0t59SVdHzuHf2jmw==", "dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "System.Security.Cryptography.ProtectedData": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "+TUFINV2q2ifyXauQXRwy4CiBhqvDEDZeVJU7qfxya4aRYOKzVBpN+4acx25VcPB9ywUN6C0n8drWl110PhZEg=="}, "System.Security.Principal.Windows": {"type": "Transitive", "resolved": "5.0.0", "contentHash": "t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA=="}, "System.Text.Encodings.Web": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "yev/k9GHAEGx2Rg3/tU6MQh4HGBXJs70y7j1LaM1i/ER9po+6nnQ6RRqTJn1E7Xu0fbIFK80Nh5EoODxrbxwBQ=="}, "System.Text.Json": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "OdrZO2WjkiEG6ajEFRABTRCi/wuXQPxeV6g8xvUJqdxMvvuCCEk86zPla8UiIQJz3durtUEbNyY/3lIhS0yZvQ==", "dependencies": {"System.Text.Encodings.Web": "8.0.0"}}, "System.Threading.RateLimiting": {"type": "Transitive", "resolved": "7.0.0", "contentHash": "1vzNiIzf11QzIcqTLf9d9+DcLt2xVT9XtP4pB/ahHngVH1VKEQ+S6yhpFlipb7JxM8VMUK9IBNlsDwWj0vUOTQ=="}, "System.Threading.Tasks.Extensions": {"type": "Transitive", "resolved": "4.5.4", "contentHash": "zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg=="}, "irishomeweb.servicedefaults": {"type": "Project", "dependencies": {"Microsoft.Extensions.Http.Resilience": "[8.0.0, )", "Microsoft.Extensions.ServiceDiscovery": "[8.0.0, )", "OpenTelemetry.Extensions.Hosting": "[1.7.0, )", "OpenTelemetry.Instrumentation.AspNetCore": "[1.8.1, )", "OpenTelemetry.Instrumentation.Http": "[1.8.1, )"}}, "Microsoft.Extensions.Http.Resilience": {"type": "CentralTransitive", "requested": "[8.0.0, )", "resolved": "8.0.0", "contentHash": "NyZ8XPwgPfyXRdSq1wYc16Zf71patMLPHtT+nBkMRP7Jdcxf+Q3XaE6MEk3mAQBfUsV6OxyL4vvnZTZ+S3wdIA==", "dependencies": {"Microsoft.Extensions.Http.Diagnostics": "8.0.0", "Microsoft.Extensions.ObjectPool": "8.0.0", "Microsoft.Extensions.Resilience": "8.0.0"}}, "Microsoft.Extensions.ServiceDiscovery": {"type": "CentralTransitive", "requested": "[8.0.0, )", "resolved": "8.0.0", "contentHash": "VXevCiq3yEfRorMyIf2XMZkwhnEbyo3fNoN3s7a/u/4BT8zlATacPxfTgH/fRUznxNjyHzplDT/rKdgdnQsjhg==", "dependencies": {"Microsoft.Extensions.Http": "8.0.0", "Microsoft.Extensions.ServiceDiscovery.Abstractions": "8.0.0"}}, "Microsoft.Extensions.Telemetry": {"type": "CentralTransitive", "requested": "[9.6.0, )", "resolved": "8.0.0", "contentHash": "JsIErSvWidQKIt17kF/Th5ag374D9duZKaDODeb3Sq6X5IpE7YHieykisy89XdKf+k8ocE+aqxcvInlbiRVXKw==", "dependencies": {"Microsoft.Bcl.TimeProvider": "8.0.0", "Microsoft.Extensions.AmbientMetadata.Application": "8.0.0", "Microsoft.Extensions.Compliance.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.AutoActivation": "8.0.0", "Microsoft.Extensions.Logging.Configuration": "8.0.0", "Microsoft.Extensions.ObjectPool": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Telemetry.Abstractions": "8.0.0"}}, "OpenTelemetry.Extensions.Hosting": {"type": "CentralTransitive", "requested": "[1.7.0, )", "resolved": "1.7.0", "contentHash": "MbB7CWWqb7xHK0jTF9Gtvw/eLWdaKqzkE1XAwLe05xyskHuwJWAbZFax4nGLA71YkMWQNO5iPIBlirvYXOLMlg==", "dependencies": {"Microsoft.Extensions.Diagnostics.Abstractions": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.0", "OpenTelemetry": "1.7.0"}}, "OpenTelemetry.Instrumentation.AspNetCore": {"type": "CentralTransitive", "requested": "[1.8.1, )", "resolved": "1.8.1", "contentHash": "dRb1LEXSH95LGEubk96kYyBmGuny9/qycH9KqL8FXcOv446Xi53EW56TVE4wTMv4HPfn+rL3B9pPQ5RX7zD4Yw==", "dependencies": {"OpenTelemetry.Api.ProviderBuilderExtensions": "1.8.0"}}, "OpenTelemetry.Instrumentation.Http": {"type": "CentralTransitive", "requested": "[1.8.1, )", "resolved": "1.8.1", "contentHash": "l1KaO1U+v11X/kfZ8tcONc5l1qoP6nPk6yPrXBJNH0Wb6NEBTdEgI1dtJBbqOnjOrI2XS09le0ZGooh9ZVkZ3Q==", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "OpenTelemetry.Api.ProviderBuilderExtensions": "1.8.0"}}}}}