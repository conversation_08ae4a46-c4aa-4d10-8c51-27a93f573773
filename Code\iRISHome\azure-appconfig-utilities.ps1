# Azure App Configuration Utilities
# Collection of utility functions for managing Azure App Configuration

# Function to list all keys in Azure App Configuration
function Get-AzureAppConfigKeys {
    param(
        [Parameter(Mandatory=$false)]
        [string]$ConnectionString = "",
        
        [Parameter(Mandatory=$false)]
        [string]$Endpoint = "",
        
        [Parameter(Mandatory=$false)]
        [string]$Label = $null
    )
    
    Write-Host "=== Listing Azure App Configuration Keys ===" -ForegroundColor Green
    
    $azCommand = "az appconfig kv list"
    
    if (-not [string]::IsNullOrEmpty($ConnectionString)) {
        $azCommand += " --connection-string `"$ConnectionString`""
    } elseif (-not [string]::IsNullOrEmpty($Endpoint)) {
        $azCommand += " --endpoint `"$Endpoint`""
    } else {
        Write-Host "❌ Please provide either ConnectionString or Endpoint" -ForegroundColor Red
        return
    }
    
    if (-not [string]::IsNullOrEmpty($Label)) {
        $azCommand += " --label `"$Label`""
    }
    
    $azCommand += " --output table"
    
    try {
        Invoke-Expression $azCommand
    } catch {
        Write-Host "❌ Failed to list keys: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Function to backup Azure App Configuration to JSON file
function Backup-AzureAppConfig {
    param(
        [Parameter(Mandatory=$true)]
        [string]$OutputPath,
        
        [Parameter(Mandatory=$false)]
        [string]$ConnectionString = "",
        
        [Parameter(Mandatory=$false)]
        [string]$Endpoint = "",
        
        [Parameter(Mandatory=$false)]
        [string]$Label = $null
    )
    
    Write-Host "=== Backing up Azure App Configuration ===" -ForegroundColor Green
    
    $azCommand = "az appconfig kv export"
    
    if (-not [string]::IsNullOrEmpty($ConnectionString)) {
        $azCommand += " --connection-string `"$ConnectionString`""
    } elseif (-not [string]::IsNullOrEmpty($Endpoint)) {
        $azCommand += " --endpoint `"$Endpoint`""
    } else {
        Write-Host "❌ Please provide either ConnectionString or Endpoint" -ForegroundColor Red
        return
    }
    
    $azCommand += " --destination file --path `"$OutputPath`" --format json"
    
    if (-not [string]::IsNullOrEmpty($Label)) {
        $azCommand += " --label `"$Label`""
    }
    
    try {
        Invoke-Expression $azCommand
        Write-Host "✅ Backup saved to: $OutputPath" -ForegroundColor Green
    } catch {
        Write-Host "❌ Failed to backup: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Function to delete specific keys from Azure App Configuration
function Remove-AzureAppConfigKeys {
    param(
        [Parameter(Mandatory=$true)]
        [string[]]$Keys,
        
        [Parameter(Mandatory=$false)]
        [string]$ConnectionString = "",
        
        [Parameter(Mandatory=$false)]
        [string]$Endpoint = "",
        
        [Parameter(Mandatory=$false)]
        [string]$Label = $null,
        
        [Parameter(Mandatory=$false)]
        [switch]$Force = $false
    )
    
    Write-Host "=== Removing Keys from Azure App Configuration ===" -ForegroundColor Yellow
    
    if (-not $Force) {
        Write-Host "Keys to delete:" -ForegroundColor Red
        foreach ($key in $Keys) {
            Write-Host "  - $key" -ForegroundColor Gray
        }
        
        $confirm = Read-Host "Are you sure you want to delete these keys? (y/N)"
        if ($confirm -ne "y" -and $confirm -ne "Y") {
            Write-Host "Operation cancelled." -ForegroundColor Yellow
            return
        }
    }
    
    $successCount = 0
    $errorCount = 0
    
    foreach ($key in $Keys) {
        $azCommand = "az appconfig kv delete"
        
        if (-not [string]::IsNullOrEmpty($ConnectionString)) {
            $azCommand += " --connection-string `"$ConnectionString`""
        } elseif (-not [string]::IsNullOrEmpty($Endpoint)) {
            $azCommand += " --endpoint `"$Endpoint`""
        } else {
            Write-Host "❌ Please provide either ConnectionString or Endpoint" -ForegroundColor Red
            return
        }
        
        $azCommand += " --key `"$key`" --yes"
        
        if (-not [string]::IsNullOrEmpty($Label)) {
            $azCommand += " --label `"$Label`""
        }
        
        try {
            $result = Invoke-Expression $azCommand 2>&1
            if ($LASTEXITCODE -eq 0) {
                Write-Host "✅ Deleted: $key" -ForegroundColor Green
                $successCount++
            } else {
                Write-Host "❌ Failed to delete $key`: $result" -ForegroundColor Red
                $errorCount++
            }
        } catch {
            Write-Host "❌ Exception deleting $key`: $($_.Exception.Message)" -ForegroundColor Red
            $errorCount++
        }
    }
    
    Write-Host "`nDeletion complete: $successCount successful, $errorCount failed" -ForegroundColor Cyan
}

# Main menu function
function Show-AzureAppConfigMenu {
    Write-Host "`n=== Azure App Configuration Utilities ===" -ForegroundColor Green
    Write-Host "1. Convert appsettings.json to Azure App Configuration" -ForegroundColor White
    Write-Host "2. List all keys in Azure App Configuration" -ForegroundColor White
    Write-Host "3. Backup Azure App Configuration to file" -ForegroundColor White
    Write-Host "4. Test Keycloak configuration fallback" -ForegroundColor White
    Write-Host "5. Exit" -ForegroundColor White
    
    $choice = Read-Host "`nSelect an option (1-5)"
    
    switch ($choice) {
        "1" {
            Write-Host "`n=== Convert appsettings.json ===" -ForegroundColor Cyan
            Write-Host "Usage examples:" -ForegroundColor Yellow
            Write-Host "  # Dry run (preview only):" -ForegroundColor Gray
            Write-Host "  .\convert-appsettings-to-azure-appconfig.ps1 -DryRun" -ForegroundColor Gray
            Write-Host ""
            Write-Host "  # Upload non-sensitive values only:" -ForegroundColor Gray
            Write-Host "  .\convert-appsettings-to-azure-appconfig.ps1" -ForegroundColor Gray
            Write-Host ""
            Write-Host "  # Upload including secrets:" -ForegroundColor Gray
            Write-Host "  .\convert-appsettings-to-azure-appconfig.ps1 -IncludeSecrets" -ForegroundColor Gray
            Write-Host ""
            Write-Host "  # Upload with custom label:" -ForegroundColor Gray
            Write-Host "  .\convert-appsettings-to-azure-appconfig.ps1 -Label `"Production`"" -ForegroundColor Gray
            
            $runNow = Read-Host "`nRun converter now? (y/N)"
            if ($runNow -eq "y" -or $runNow -eq "Y") {
                .\convert-appsettings-to-azure-appconfig.ps1 -DryRun
            }
        }
        "2" {
            Write-Host "`n=== List Azure App Configuration Keys ===" -ForegroundColor Cyan
            # Read connection details from appsettings.json
            if (Test-Path "iRISHome/appsettings.json") {
                $appSettings = Get-Content "iRISHome/appsettings.json" -Raw | ConvertFrom-Json
                if ($appSettings.AppConfig.ConnectionString) {
                    Get-AzureAppConfigKeys -ConnectionString $appSettings.AppConfig.ConnectionString
                } elseif ($appSettings.AppConfig.Endpoint) {
                    Get-AzureAppConfigKeys -Endpoint $appSettings.AppConfig.Endpoint
                } else {
                    Write-Host "❌ No Azure App Configuration connection details found in appsettings.json" -ForegroundColor Red
                }
            } else {
                Write-Host "❌ appsettings.json not found" -ForegroundColor Red
            }
        }
        "3" {
            Write-Host "`n=== Backup Azure App Configuration ===" -ForegroundColor Cyan
            $backupPath = "azure-appconfig-backup-$(Get-Date -Format 'yyyyMMdd-HHmmss').json"
            
            if (Test-Path "iRISHome/appsettings.json") {
                $appSettings = Get-Content "iRISHome/appsettings.json" -Raw | ConvertFrom-Json
                if ($appSettings.AppConfig.ConnectionString) {
                    Backup-AzureAppConfig -OutputPath $backupPath -ConnectionString $appSettings.AppConfig.ConnectionString
                } elseif ($appSettings.AppConfig.Endpoint) {
                    Backup-AzureAppConfig -OutputPath $backupPath -Endpoint $appSettings.AppConfig.Endpoint
                } else {
                    Write-Host "❌ No Azure App Configuration connection details found in appsettings.json" -ForegroundColor Red
                }
            } else {
                Write-Host "❌ appsettings.json not found" -ForegroundColor Red
            }
        }
        "4" {
            Write-Host "`n=== Test Keycloak Configuration Fallback ===" -ForegroundColor Cyan
            if (Test-Path "test-keycloak-fallback.ps1") {
                .\test-keycloak-fallback.ps1
            } else {
                Write-Host "❌ test-keycloak-fallback.ps1 not found" -ForegroundColor Red
            }
        }
        "5" {
            Write-Host "Goodbye!" -ForegroundColor Green
            return
        }
        default {
            Write-Host "Invalid option. Please select 1-5." -ForegroundColor Red
            Show-AzureAppConfigMenu
        }
    }
}

# If script is run directly, show menu
if ($MyInvocation.InvocationName -eq $MyInvocation.MyCommand.Name) {
    Show-AzureAppConfigMenu
}
