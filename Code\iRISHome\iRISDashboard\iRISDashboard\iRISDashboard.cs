﻿using iRISDashboard.DBObjects;
using iRISDashboard.Models;
using JSONLogger;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Data;
using System.IdentityModel.Tokens.Jwt;
using System.IO;
using System.Linq;
using System.Security.Claims;

namespace Dashboard.Controllers
{
    public class iRISDashboard : Controller
    {
        public IActionResult Index()
        {
            return View();
        }
        [Authorize]
        public IActionResult TestIndex()
        {
            try
            {
                JsonLog.Log("TestIndex controller hit");
                if (User.Identity.IsAuthenticated)
                {
                    var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                    var userName = User.FindFirst(ClaimTypes.GivenName)?.Value + " " + User.FindFirst(ClaimTypes.Surname)?.Value;
                    var userEmail = User.FindFirst(ClaimTypes.Email)?.Value;
                    var accessToken = HttpContext.GetTokenAsync("access_token");
                    var AtrributesDT = CreateDataTableFromJson(accessToken.Result);
                    ViewData["UserAttributes"] = AtrributesDT;

                    var userGroups = User.Claims
                   .Where(c => c.Type == ClaimTypes.Role || c.Type == "user_groups")
                   .Select(c => c.Value.StartsWith("/") ? c.Value.Substring(1) : c.Value)
                   .ToList();

                    if(userGroups.Count == 0) return RedirectToAction("Index", "AuthLogin");

                    ViewBag.UserId = userId;
                    ViewBag.UserName = userName;
                    ViewBag.UserEmail = userEmail;
                    ViewBag.UserGroups = userGroups;
                    HttpContext.Session.SetString("GroupName", userGroups.FirstOrDefault().ToString());
                    HttpContext.Session.SetString("DisplayName", userName);
                    HttpContext.Session.SetString("IsAdminStatus", "True");
                    return View();
                }
                return RedirectToAction("Index", "AuthLogin");
            }
            catch(Exception ex)
            {
                JsonLog.Log(ex.Message);
                return RedirectToAction("Index", "AuthLogin");
            }
            
        }
        public IConfiguration GetConfigurations()
        {
            var builder = new ConfigurationBuilder().SetBasePath(Directory.GetCurrentDirectory()).AddJsonFile("appsettings.json", optional: true, reloadOnChange: true);
            return builder.Build();
        }

        public IActionResult Admin()
        {
            DbObjects dbObjects = new DbObjects();
            List<SettingsViewModel> SettingsList = new List<SettingsViewModel>();
            SettingsList=dbObjects.GetDashboardSettings();
            return View(SettingsList);
        }

        public IActionResult GetProjectSettings(string ProjectType, string Status="false")
        {
            DbObjects dbObjects = new DbObjects();
            List<iRISDashboardViewModel> SettingsList = new List<iRISDashboardViewModel>();
            SettingsList=dbObjects.GetProjectSettings(ProjectType);
            if(Status!="false")
            {
                TempData["ToastAlertStatus"] = Status;
            }
            return View(SettingsList);
        }

        [HttpPost]
        public IActionResult AppendSettingData([FromBody] UpdateAppSettings updateSettingViewModel)
        {
            string jsonData = JsonConvert.SerializeObject(updateSettingViewModel);
            TempData["UpdateAppSettingsData"] = jsonData;
            return Json(new { success = true });
        }

        public IActionResult UpdateAppsettingsInfo()
        {
            UpdateAppSettings model = null;

            if (TempData["UpdateAppSettingsData"] is string jsonData)
            {
                model = JsonConvert.DeserializeObject<UpdateAppSettings>(jsonData);
                return View(model);
            }
            return RedirectToAction("GetProjectSettings", new { ProjectType = model?.Project, Status = "Setting Updated Successfully." });
        }

        [HttpPost]
        public IActionResult UpdateDashboardSettings([FromBody] UpdateAppSettings updateAppSettings)
        {
            string result = "";
            DbObjects dbObjects = new DbObjects();
            result = dbObjects.UpdateDashboardSetting(updateAppSettings);
            return Json(new { success = true });
        }

        public ActionResult AddNewSetting(string ImgURL, string Project)
        {
            ViewBag.ImgURL = ImgURL;
            ViewBag.Project = Project;
            return View();
        }

        [HttpPost]
        public IActionResult AddNewSettingToDB([FromBody] UpdateAppSettings updateAppSettings)
        {
            string result = "";
            DbObjects dbObjects = new DbObjects();
            result = dbObjects.InsertNewSetting(updateAppSettings);
            return Json(new { success = true });
        }

        public IActionResult DeleteSetting(string SettingID,string ProjectType)
        {
            string result = "";
            DbObjects dbObjects = new DbObjects();
            result = dbObjects.DeleteSetting(SettingID);
            return RedirectToAction("GetProjectSettings", new { ProjectType = ProjectType, Status = "Setting Deleted Successfully." });

        }

        
        public IActionResult KeycloakValidation()
        {
            if (User.Identity.IsAuthenticated)
            {
                var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                var userName = User.FindFirst(ClaimTypes.GivenName)?.Value + " " + User.FindFirst(ClaimTypes.Surname)?.Value;
                var userEmail = User.FindFirst(ClaimTypes.Email)?.Value;
                var accessToken = HttpContext.GetTokenAsync("access_token");
                var AtrributesDT = CreateDataTableFromJson(accessToken.Result);
                

                var userGroups = User.Claims
               .Where(c => c.Type == ClaimTypes.Role || c.Type == "user_groups")
               .Select(c => c.Value.StartsWith("/") ? c.Value.Substring(1) : c.Value)
               .ToList();

                var userAttributes = User.Claims
               .Where(c => c.Type == ClaimTypes.Role || c.Type == "user_attributes")
               .Select(c => c.Value.StartsWith("/") ? c.Value.Substring(1) : c.Value)
               .ToList();

                ViewBag.UserId = userId;
                ViewBag.UserName = userName;
                ViewBag.UserEmail = userEmail;
                ViewBag.UserGroups = userGroups;
                HttpContext.Session.SetString("GroupName", userGroups.FirstOrDefault().ToString());
                HttpContext.Session.SetString("DisplayName", userName);
                HttpContext.Session.SetString("IsAdminStatus", "True");
                return RedirectToAction("TestIndex", "iRISDashboard", new { UserAttributes = AtrributesDT });
            }
            return View("Error");
        }

        public static DataTable CreateDataTableFromJson(string json)
        {
            var handler = new JwtSecurityTokenHandler();
            var token = handler.ReadJwtToken(json);

            // Extract user_attributes as string
            if (!token.Payload.TryGetValue("user_attributes", out object attrObj))
                throw new Exception("user_attributes not found in JWT token");

            // Parse the inner JSON string
            string attrJson = attrObj.ToString();
            // Deserialize the JSON string into a list of AppData objects
            List<AppData> appList = JsonConvert.DeserializeObject<List<AppData>>(attrJson);

            // Create a new DataTable
            DataTable dataTable = new DataTable();

            // Define the columns based on the AppData properties
            dataTable.Columns.Add("AppName", typeof(string));
            dataTable.Columns.Add("Description", typeof(string));
            dataTable.Columns.Add("AppLink", typeof(string));
            dataTable.Columns.Add("AppType", typeof(string));
            dataTable.Columns.Add("ImgURL", typeof(string));
            dataTable.Columns.Add("Project", typeof(string));

            // Populate the DataTable with data from the appList
            foreach (AppData app in appList)
            {
                dataTable.Rows.Add(app.AppName, app.Description, app.AppLink, app.AppType, app.ImgURL, app.Project);
            }
            return dataTable;
        }
    }
}
