﻿using iRISHome.DbFunctions;
using iRISHome.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using System.Linq;

namespace iRISHome.ViewComponents
{
    public class MenuViewComponent : ViewComponent
    {
        public IViewComponentResult Invoke()
        {
        //    DBObjects dbo = new DBObjects();
        //    string UserGroupID = HttpContext.Session.GetString("UserGroupID");
        //    int UserID = (int)HttpContext.Session.GetInt32("UserID");
        //    var MenuItems = dbo.GetMenuItems(UserGroupID, UserID);
            return View();
        }
    }
}
