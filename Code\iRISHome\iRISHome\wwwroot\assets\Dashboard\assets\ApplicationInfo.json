[{"AppName": "Check Your Internet Speed", "Description": "Please check internet connection, Make sure that you have good internet connection.", "AppLink": "https://www.speedtest.net/", "AppType": "Web", "ImgURL": "/assets/Dashboard/assets/img/internetspeed.jpg", "Project": "To-Do List"}, {"AppName": "Demo Schedule", "Description": "Schedule demo once the internet connection has been checked.", "AppLink": "https://apps.powerapps.com/play/e/default-7762ab46-b5d7-43d2-a87b-c90167961067/a/e315da17-00f5-4195-b439-3ceee7790a1b?tenantId=7762ab46-b5d7-43d2-a87b-c90167961067&hint=86a2a2d1-68ed-40e5-8099-fdac93c88217&sourcetime=1712931610563&source=portal", "AppType": "Web", "ImgURL": "/assets/Dashboard/assets/img/Time.png", "Project": "To-Do List"}, {"AppName": "iRISupply Demo Instructions", "Description": "Demo steps will clearly provide step-by-step information regarding what needs to be done before the demo.", "AppLink": "https://www.mobileaspects.com/wp-content/uploads/2024/06/Demo-Instructions.pdf", "AppType": "Web", "ImgURL": "/assets/Dashboard/assets/img/ToDoList.png", "Project": "iRISupply"}, {"AppName": "iRlSupply PPT", "Description": "iRISupply power point presentation which gives clear cut information with respect to iRISupply product.", "AppLink": "https://mobileaspectshealth.sharepoint.com/:p:/s/BusinessOpsTeam/ETUZeK8Z4L1FjETPNM4b918Bd-OkI3dOcDbS4LYcq0_vZw?e=65kFvA", "AppType": "Web", "ImgURL": "/assets/Dashboard/assets/img/PPT.jpg", "Project": "iRISupply"}, {"AppName": "iRlSupply Demo Video", "Description": "iRlSupply Demo Video which gives clear cut information with respect to iRISupply product.", "AppLink": "https://mobileaspectshealth.sharepoint.com/sites/BusinessOpsTeam/_layouts/15/stream.aspx?id=%2Fsites%2FBusinessOpsTeam%2FShared%20Documents%2FMarketing%2FNew%20Marketing%20Assets%2FProduct%20Videos%207th%20Sept%202023%2FiRISupply%20Introduction%2Emp4&referrer=StreamWebApp%2EWeb&referrerScenario=AddressBarCopied%2Eview%2E7165415b%2D0745%2D4fc1%2Da1f3%2D37130370e7d2", "AppType": "Web", "ImgURL": "/assets/Dashboard/assets/img/Video Icon.png", "Project": "iRISupply"}, {"AppName": "iRISecure - Tissue Demo Video", "Description": "iRISupply Demo Video which gives clear cut information with respect to iRISupply product.", "AppLink": "https://mobileaspectshealth.sharepoint.com/sites/BusinessOpsTeam/_layouts/15/stream.aspx?id=%2Fsites%2FBusinessOpsTeam%2FShared%20Documents%2FMarketing%2FNew%20Marketing%20Assets%2FProduct%20Videos%207th%20Sept%202023%2FiRISecure%20Tissue%2Emp4&ga=1&referrer=StreamWebApp%2EWeb&referrerScenario=AddressBarCopied%2Eview%2E089e5e8f%2D197a%2D4d36%2Db3da%2D7de4c67e06c1", "AppType": "Web", "ImgURL": "/assets/Dashboard/assets/img/Video Icon.png", "Project": "iRISupply"}, {"AppName": "iRISupplyWeb 5.0", "Description": "This web application tracks patient usage and product details, providing insights into healthcare interactions and resource utilization and providing insights into healthcare interactions and resource utilization.", "AppLink": "https://irisdemoserver1.mobileaspects.com/irisweb", "AppType": "Web", "ImgURL": "/assets/Dashboard/assets/img/iRISupply.ico", "Project": "iRISupply"}, {"AppName": "iRlSupply Web Console Application", "Description": "iRlSupply Web Console Application.", "AppLink": "https://cch9appvm01.mobileaspectshealth.org/MAD-SUP-CCH9OR/iRISupplyHub/BETA", "AppType": "Web", "ImgURL": "/assets/Dashboard/assets/img/iRISupply.ico", "Project": "iRISupply"}, {"AppName": "iRISupplyWeb 6.0", "Description": "This web application exclusively showcases a dashboard for streamlined data visualization and insights.", "AppLink": "https://irisdemoserver1.mobileaspects.com:4444/", "AppType": "Web", "ImgURL": "/assets/Dashboard/assets/img/iRISupply.ico", "Project": "iRISupply"}, {"AppName": "iRISupply Standard QBR", "Description": "This is the iRISuppIy Standard QBR template which is used for supply application analytics.", "AppLink": "https://irisdemoserver1.mobileaspects.com/SUPPLY/irisanalytics/Dashboard/Dashboard/EmbedReport?groupId=0bc9c51f-a4c6-4339-bbd1-91cf926e3765&reportId=248112b3-36d6-4f55-8227-cbae23279280", "AppType": "Web", "ImgURL": "/assets/Dashboard/assets/img/iRISupply.ico", "Project": "iRISupply"}, {"AppName": "iRISupply Standard QBR Multi-Dept", "Description": "This is the iRISuppIy Standard QBR template for multipale departrment.", "AppLink": "https://irisdemoserver1.mobileaspects.com/SUPPLY/irisanalytics/Dashboard/Dashboard/EmbedReport?groupId=0bc9c51f-a4c6-4339-bbd1-91cf926e3765&reportId=1a5c1086-c6c8-40bc-b32d-0246c65bfdea", "AppType": "Web", "ImgURL": "/assets/Dashboard/assets/img/iRISupply.ico", "Project": "iRISupply"}, {"AppName": "iRIScope Demo Instructions", "Description": "Demo steps will clearly provide step-by-step information regarding what needs to be done before the demo.", "AppLink": "https://www.mobileaspects.com/wp-content/uploads/2024/06/Demo-Instructions.pdf", "AppType": "Web", "ImgURL": "/assets/Dashboard/assets/img/ToDoList.png", "Project": "iRIScope"}, {"AppName": "iRIScope PPT", "Description": "iRIScope power point presentation which gives clear cut information with respect to iRIScope product.", "AppLink": "https://mobileaspectshealth.sharepoint.com/:p:/s/BusinessOpsTeam/EYYObiTFW_dEjM59bgmKA_UByJC6xHWh4_JFiGjRazWxoA?e=aH21XU", "AppType": "Web", "ImgURL": "/assets/Dashboard/assets/img/PPT.jpg", "Project": "iRIScope"}, {"AppName": "iRIScope Demo Video", "Description": "iRIScope Demo Video which gives clear cut information with respect to iRIScope product.", "AppLink": "https://mobileaspectshealth.sharepoint.com/sites/BusinessOpsTeam/_layouts/15/stream.aspx?id=%2Fsites%2FBusinessOpsTeam%2FShared%20Documents%2FMarketing%2FNew%20Marketing%20Assets%2FProduct%20Videos%207th%20Sept%202023%2FiRIScope%20Introduction%2Emp4&ga=1&referrer=StreamWebApp%2EWeb&referrerScenario=AddressBarCopied%2Eview%2Ebb68449b%2D8851%2D499e%2D8d48%2D0f1cf1a79c9f", "AppType": "Web", "ImgURL": "/assets/Dashboard/assets/img/Video Icon.png", "Project": "iRIScope"}, {"AppName": "iRIScopeWeb", "Description": "This web Application is mainly used to Configure iRIScope Desktop Application, some of the Most widely Used Features are Reports, Scope tracking , Manage Users.", "AppLink": "https://mademoweb01.maspects.com/iRIScopeWeb3.0/Login/Login", "AppType": "Web", "ImgURL": "/assets/Dashboard/assets/img/iRIScope.ico", "Project": "iRIScope"}, {"AppName": "iRIScope Standard QBR", "Description": "This is the iRIScope standard QBR template which is used for scope application analytics.", "AppLink": "https://irisdemoserver1.mobileaspects.com/SCOPE/irisanalytics/Dashboard/Dashboard/EmbedReport?groupId=46d513e0-7075-4fc6-9eaf-4716fa399907&reportId=30b07db5-1e13-4d9c-b17d-f59a479728f2", "AppType": "Web", "ImgURL": "/assets/Dashboard/assets/img/iRIScope.ico", "Project": "iRIScope"}, {"AppName": "iRISecureBlood Desktop Application", "Description": "This application is used to track the blood bags.Please use shortcut icon present in desktop to launch console app.", "AppLink": "N/A", "AppType": "<PERSON><PERSON><PERSON>", "ImgURL": "/assets/Dashboard/assets/img/iRISecure-Blood.ico", "Project": "iRISecureBlood"}, {"AppName": "iRISecureBlood Web Application", "Description": "This application is used to configure the iRISecureBlood Desktop app and reporting.", "AppLink": "https://irisdemoserver1.mobileaspects.com/irisecurebloodweb/", "AppType": "Web", "ImgURL": "/assets/Dashboard/assets/img/iRISecure-Blood.ico", "Project": "iRISecureBlood"}, {"AppName": "iRISpecimen Desktop Application", "Description": "This application is used to track the specimen's.Please use shortcut icon present in desktop to launch console app.", "AppLink": "N/A", "AppType": "<PERSON><PERSON><PERSON>", "ImgURL": "/assets/Dashboard/assets/img/iRISecure-Specimen.ico", "Project": "iRISpecimen"}, {"AppName": "iRISpecimen Web Application", "Description": "This application is used to configure the iRISpecimen Desktop app and reporting.", "AppLink": "https://irisdemoserver1.mobileaspects.com/irisecurespecimenweb", "AppType": "Web", "ImgURL": "/assets/Dashboard/assets/img/iRISecure-Specimen.ico", "Project": "iRISpecimen"}]