<?xml version="1.0" encoding="utf-8"?>
<configuration>
	<packageSources>
		<clear />
		<add key="nuget.org" value="https://api.nuget.org/v3/index.json" protocolVersion="3" />
		<add key="telerik.com" value="https://nuget.telerik.com/v3/index.json" protocolVersion="3" />
	</packageSources>
	<packageSourceCredentials>
		<telerik.com>
			<add key="Username" value="<EMAIL>" />
			<add key="ClearTextPassword" value="M1t1g@t0r" />
		</telerik.com>
	</packageSourceCredentials>
	<packageSourceMapping>
		<packageSource key="telerik.com">
			<package pattern="Telerik.*" />
			<package pattern="Kendo.*" />
			<package pattern="Progress.*" />
		</packageSource>
		<packageSource key="nuget.org">
			<package pattern="Telerik.Licensing" />
			<package pattern="Telerik.FontIcons" />
			<package pattern="Telerik.SvgIcons" />			
			<package pattern="Microsoft.*" />
			<package pattern="Aspire.*" />
			<package pattern="OpenTelemetry.*" />
			<package pattern="System.*" />
			<package pattern="SQLitePCLRaw.*" />
			<package pattern="Newtonsoft.*" />
			<package pattern="Serilog.*" />
			<package pattern="Google*" />
			<package pattern="*" />
		</packageSource>
	</packageSourceMapping>
	<config>
		<add key="dependencyVersion" value="Highest" />
		<add key="globalPackagesFolder" value="%userprofile%\.nuget\packages" />
	</config>
</configuration>