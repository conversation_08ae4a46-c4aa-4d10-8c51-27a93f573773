﻿/// <summary>
/// Author :<PERSON><PERSON>
/// Date Created: 09-15-2023
/// Description: View models for application setting details upon user login.
/// </summary>
using System;
using System.Collections.Generic;
using System.Text;

namespace AuthLogin.Models
{
    public class SettingViewModel
    {
        public int SettingID { get; set; }
        public string? Description { get; set; }
        public string? SettingName { get; set; }
        public string? SettingValue { get; set; }
        
    }
    public class ProductDetails
    {
        public string? Name { get; set; }
    }
    public class StartUpSetting
    {
        public List<SettingViewModel> Settings = new List<SettingViewModel>();
        public ProductDetails ProductDetails= new ProductDetails();
        public string? SessionTimeout { get; set; }
    }
    public class LoginFeaturesViewModel
    {
        public string? FeatureName { get; set; }
        public bool Status { get; set; }
    }
}
