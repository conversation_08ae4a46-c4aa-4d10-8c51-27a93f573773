<Project>
  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <AddRazorSupportForMvc>true</AddRazorSupportForMvc>
    <LangVersion>preview</LangVersion>
    <!-- Suppress warnings about preview features -->
    <NoWarn>$(NoWarn);CS1591;NU5104</NoWarn>
  </PropertyGroup>

  <!-- Common package versions -->
  <PropertyGroup>
    <AspireVersion>9.0.0</AspireVersion>
  </PropertyGroup>

  <!-- Build order -->
  <PropertyGroup>
    <!-- Define root paths -->
    <SolutionDir>$(MSBuildThisFileDirectory)</SolutionDir>
    
    <!-- Projects in order of dependency -->
    <ServiceDefaultsProject>..\iRISHomeWeb.ServiceDefaults\iRISHomeWeb.ServiceDefaults.csproj</ServiceDefaultsProject>
    <AuthLoginProject>..\AuthLogin\AuthLogin.csproj</AuthLoginProject>
    <DashboardProject>..\iRISDashboard\iRISDashboard.csproj</DashboardProject>
    <WebProject>..\iRISHome\iRISHome.csproj</WebProject>
    <AppHostProject>..\iRISHomeWeb.AppHost\iRISHomeWeb.AppHost.csproj</AppHostProject>
  </PropertyGroup>

  <!-- Define project dependencies -->
  <ItemGroup>
    <!-- ServiceDefaults has no dependencies -->
    
    <!-- AuthLogin and Dashboard depend on ServiceDefaults -->
    <ProjectReference Condition="'$(MSBuildProjectName)' == 'AuthLogin' Or '$(MSBuildProjectName)' == 'iRISDashboard'" 
                     Include="$(ServiceDefaultsProject)" />
    
    <!-- Main web app depends on everything except AppHost -->
    <ProjectReference Condition="'$(MSBuildProjectName)' == 'iRISHome'" Include="$(AuthLoginProject)" />
    <ProjectReference Condition="'$(MSBuildProjectName)' == 'iRISHome'" Include="$(DashboardProject)" />
    <ProjectReference Condition="'$(MSBuildProjectName)' == 'iRISHome'" Include="$(ServiceDefaultsProject)" />
    
    <!-- AppHost depends on the web app -->
    <ProjectReference Condition="'$(MSBuildProjectName)' == 'iRISHomeWeb.AppHost'" Include="$(WebProject)" />
  </ItemGroup>
  
</Project>
