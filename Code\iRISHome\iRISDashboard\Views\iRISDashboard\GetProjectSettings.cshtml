﻿@using iRISDashboard.Models;
@model List<iRISDashboardViewModel>
@using Microsoft.AspNetCore.Http
@inject Microsoft.AspNetCore.Http.IHttpContextAccessor HttpContextAccessor

@{
    var session = HttpContextAccessor.HttpContext.Session;
    var appurl = session.GetString("BaseURL");
}

@{
    ViewBag.Title = "Manage iRISDashboard";
    ViewBag.pTitle = "Manage iRISDashboard";
    ViewBag.pageTitle = "Mobile Aspects";
    ViewBag.BrandName = "iRISHome";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<link rel="stylesheet" href="~/assets/libs/toastify/toastify.css">
<script src="~/assets/libs/toastify/toastify.js"></script>
<script src="~/assets/js/pages/form-validation.init.js"></script>

<style>
    .main_color_bg {
    background: #274256 !important;
    color: white !important;
    }
</style>

<script>
    function EditSetting(e) {
        var settingToEdit = $(e).closest('tr').data();

        // Build a model from the selected row's data
        var model = {
            AppName: settingToEdit.AppName,
            AppType: settingToEdit.AppType,
            ImgURL: settingToEdit.ImgURL,
            Project: settingToEdit.Project,
            AppLink: settingToEdit.AppLink,
            Description: settingToEdit.Description,
            ID: settingToEdit.ID
        };

        $.ajax({
            url: '@Url.Action("AppendSettingData", "iRISDashboard")',
            type: 'POST',
            data: JSON.stringify(model),
            datatype: 'json',
            contentType: 'application/json',
            success: function(response) {
                if (response.success) {
                    window.location="/iRISDashboard/UpdateAppsettingsInfo"
                } else {
                    alert("Failed to update settings.");
                }
            },
            error: function(xhr, status, error) {
                alert("An error occurred while updating the settings: " + error);
            }
        });
    }

    function AddNewSetting(e) {
        var model = {
            ImgURL: "",
            Project: "",
        };
        var queryString = $.param(model);
        window.location.href = '@Url.Action("AddNewSetting", "iRISDashboard")?' + queryString;
    }

    function DeleteSetting(ID, ProjectType) {
        TriggerConfirmationAlert("Confirm Deletion.", "Deleting this module will remove all associated data. Are you sure you want to proceed?", ID, ProjectType);
    }
</script>

<script>
    if('@TempData["ToastAlertStatus"]'=="Setting Deleted Successfully."){
        toastMessage("Setting Deleted Successfully.","linear-gradient(to right, #00b09b, #96c93d)");
    }
    else if('@TempData["ToastAlertStatus"]'=="Failed To Delete Setting."){
         toastMessage("Failed to update User.","linear-gradient(to right, red, yellow)");
    }
    else if('@TempData["ToastAlertStatus"]'=="New Setting Added Successfully.") {
        toastMessage("New Setting Added Successfully.","linear-gradient(to right, #00b09b, #96c93d)");
    } else if('@TempData["ToastAlertStatus"]'=='Issue in adding new setting.') {
        toastMessage("Issue in creating User.","linear-gradient(to right, red, yellow)");
    }
    else if('@TempData["ToastAlertStatus"]'=="Setting Updated Successfully.") {
        toastMessage("Setting Updated Successfully.","linear-gradient(to right, #00b09b, #96c93d)");
    } else if('@TempData["ToastAlertStatus"]'=='Issue in setting updation.') {
        toastMessage("Issue in creating User.","linear-gradient(to right, red, yellow)");
    }
    else {
    }
    function toastMessage(text,color){
     Toastify({
  text: text,
  duration: 3000,
  close: true,
  gravity: "top", 
  position: "right", 
  stopOnFocus: true, 
  style: {
    background: color,
  },
  onClick: function(){} 
}).showToast();
    }

    function TriggerConfirmationAlert(title, message,ID,ProjectType) {

        Swal.fire({
            title: title,
            text: message,
            icon: 'info',
            showDenyButton: true,
            showCancelButton: false,
            confirmButtonText: 'Yes, Delete',
            denyButtonText: 'No, Cancel',
        }).then((result) => {
            if (result.isConfirmed) {
                window.location = '/iRISDashboard/DeleteSetting?SettingID='+ID+'&ProjectType='+ProjectType;
            } else if (result.isDenied) {
            }
        })
    }
</script>