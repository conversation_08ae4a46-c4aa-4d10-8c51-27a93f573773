﻿<Project Sdk="Microsoft.NET.Sdk">
  <Sdk Name="Aspire.AppHost.Sdk" Version="9.0.0" />
  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net9.0</TargetFramework>
    <IsAspireHost>true</IsAspireHost>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Aspire.Hosting" />
    <PackageReference Include="Aspire.Hosting.AppHost" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\iRISHomeWeb.ServiceDefaults\iRISHomeWeb.ServiceDefaults.csproj" IsAspireProjectResource="false" />
    <ProjectReference Include="..\AuthLogin\AuthLogin.csproj" IsAspireProjectResource="false" />
    <ProjectReference Include="..\iRISDashboard\iRISDashboard.csproj" IsAspireProjectResource="false" />
    <ProjectReference Include="..\iRISHome\iRISHome.csproj" />
  </ItemGroup>
</Project>
