﻿using System.Collections.Generic;

namespace iRISHome.Models
{
    public class NotificationViewModel
    {
        public List<Notification> Notifications { get; set; } = new List<Notification>();
        public List<Bookmarks> Bookmarks { get; set; } = new List<Bookmarks>();
        public List<RecentUserHistory> UserHistory { get; set; } = new List<RecentUserHistory>();
    } 

    public class Notification
    {
        public string NotificationLabel { get; set; } = string.Empty;
        public string AlertCount { get; set; } = string.Empty;
        public string NotificationDetails { get; set; } = string.Empty;
        public string AlertUrl { get; set; } = string.Empty;
    }
    public class Bookmarks
    {
        public string Title { get; set; } = string.Empty;
        public string BookmarkUrl { get; set; } = string.Empty;
        public string BookmarkType { get; set; } = string.Empty;
        public string BookmarkDataID { get; set; } = string.Empty;
    }
    public class RecentUserHistory
    {
        public string ModuleName { get; set; } = string.Empty;
        public string ModuleURL { get; set; } = string.Empty;
    }
}
