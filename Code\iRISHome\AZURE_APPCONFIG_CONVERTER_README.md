# Azure App Configuration Converter

This collection of PowerShell scripts helps you convert your `appsettings.json` configuration to Azure App Configuration and manage the configuration lifecycle.

## Scripts Overview

### 1. `convert-appsettings-to-azure-appconfig.ps1`
Main script that converts appsettings.json to Azure App Configuration key-value pairs.

### 2. `azure-appconfig-utilities.ps1`
Utility functions for managing Azure App Configuration (list, backup, delete keys).

### 3. `test-keycloak-fallback.ps1`
Test script for the Keycloak configuration fallback mechanism.

## Prerequisites

1. **Azure CLI**: Install from [https://docs.microsoft.com/en-us/cli/azure/install-azure-cli](https://docs.microsoft.com/en-us/cli/azure/install-azure-cli)
2. **Azure Login**: Run `az login` to authenticate
3. **Azure App Configuration**: Ensure you have an Azure App Configuration resource

## Quick Start

### 1. Preview what will be uploaded (Dry Run)
```powershell
.\convert-appsettings-to-azure-appconfig.ps1 -DryRun
```

### 2. Upload non-sensitive configuration only
```powershell
.\convert-appsettings-to-azure-appconfig.ps1
```

### 3. Upload including secrets/sensitive values
```powershell
.\convert-appsettings-to-azure-appconfig.ps1 -IncludeSecrets
```

## Detailed Usage

### Basic Conversion
```powershell
# Uses connection string from appsettings.json
.\convert-appsettings-to-azure-appconfig.ps1

# Specify custom appsettings file
.\convert-appsettings-to-azure-appconfig.ps1 -AppSettingsPath "path/to/appsettings.json"

# Use specific connection string
.\convert-appsettings-to-azure-appconfig.ps1 -ConnectionString "your-connection-string"

# Use endpoint with managed identity
.\convert-appsettings-to-azure-appconfig.ps1 -Endpoint "https://your-appconfig.azconfig.io"
```

### Advanced Options
```powershell
# Upload with a specific label (useful for environments)
.\convert-appsettings-to-azure-appconfig.ps1 -Label "Production"

# Include sensitive values (secrets, passwords, keys)
.\convert-appsettings-to-azure-appconfig.ps1 -IncludeSecrets

# Dry run to preview changes
.\convert-appsettings-to-azure-appconfig.ps1 -DryRun
```

## Configuration Mapping

The script converts nested JSON structure to flat key-value pairs using colon (`:`) as separator:

### Input (appsettings.json):
```json
{
  "KeycloakAuthentication": {
    "OpenIdConnect": {
      "ClientId": "my-client-id",
      "ClientSecret": "my-secret",
      "Authority": "https://keycloak.example.com/realms/myrealm"
    }
  },
  "Office365Settings": {
    "Scopes": ["user.read", "profile"]
  }
}
```

### Output (Azure App Configuration):
```
KeycloakAuthentication:OpenIdConnect:ClientId = my-client-id
KeycloakAuthentication:OpenIdConnect:ClientSecret = my-secret
KeycloakAuthentication:OpenIdConnect:Authority = https://keycloak.example.com/realms/myrealm
Office365Settings:Scopes:0 = user.read
Office365Settings:Scopes:1 = profile
```

## Security Features

### Sensitive Data Protection
By default, the script **skips** keys containing these patterns:
- `*Secret*`
- `*Password*`
- `*Key*`
- `*Token*`
- `*ConnectionString*`

Use `-IncludeSecrets` to upload these values.

### What Gets Skipped (Default)
- `KeycloakAuthentication:OpenIdConnect:ClientSecret`
- `Office365Settings:ClientSecret`
- `TwoFactorAuthKey`
- `AppConfig:ConnectionString`

## Utility Functions

### List all keys in Azure App Configuration
```powershell
# Load utilities
. .\azure-appconfig-utilities.ps1

# List keys
Get-AzureAppConfigKeys -ConnectionString "your-connection-string"
```

### Backup Azure App Configuration
```powershell
# Backup to JSON file
Backup-AzureAppConfig -OutputPath "backup.json" -ConnectionString "your-connection-string"
```

### Interactive Menu
```powershell
# Run the utilities menu
.\azure-appconfig-utilities.ps1
```

## Example Workflow

### 1. Initial Setup
```powershell
# 1. Preview what will be uploaded
.\convert-appsettings-to-azure-appconfig.ps1 -DryRun

# 2. Upload non-sensitive configuration
.\convert-appsettings-to-azure-appconfig.ps1

# 3. Manually add sensitive values in Azure Portal or CLI
```

### 2. Production Deployment
```powershell
# 1. Create backup
. .\azure-appconfig-utilities.ps1
Backup-AzureAppConfig -OutputPath "prod-backup.json"

# 2. Upload with production label
.\convert-appsettings-to-azure-appconfig.ps1 -Label "Production" -IncludeSecrets
```

### 3. Testing Fallback
```powershell
# Test the Keycloak fallback mechanism
.\test-keycloak-fallback.ps1
```

## Troubleshooting

### Common Issues

1. **Azure CLI not found**
   - Install Azure CLI and restart PowerShell
   - Run `az login` to authenticate

2. **Permission denied**
   - Ensure you have `App Configuration Data Owner` role
   - Check if using managed identity with correct permissions

3. **Connection string issues**
   - Verify connection string format
   - Check if App Configuration resource exists

4. **Key upload failures**
   - Check for special characters in keys
   - Verify value length limits (10KB per value)

### Debug Mode
Add `-Verbose` to any script for detailed output:
```powershell
.\convert-appsettings-to-azure-appconfig.ps1 -DryRun -Verbose
```

## Best Practices

1. **Always run dry run first** to preview changes
2. **Backup before major changes** using the backup utility
3. **Use labels** for different environments (Dev, Staging, Production)
4. **Keep secrets secure** - consider using Azure Key Vault references
5. **Test fallback mechanism** after configuration changes
6. **Monitor configuration** in Azure Portal for changes

## Integration with Application

After uploading configuration, your application will:
1. Try to read from Azure App Configuration first
2. Fall back to appsettings.json if values are missing
3. Log which source is used for each configuration value

This ensures robust configuration management with centralized control and local fallback.
