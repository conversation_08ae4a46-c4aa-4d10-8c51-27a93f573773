﻿/// <summary>
/// Author :<PERSON><PERSON> <PERSON>
/// Date Created: 09-15-2023
/// Description: Contains view models which will be user across the applicatioin .
/// </summary>
using AuthLogin.Models;
using System;
using System.Collections.Generic;
using System.Text;

namespace AuthLogin.Models
{
    public class GlobalVariable
    {
        public string brandname { get; set; } = string.Empty;
        public string username { get; set; } = string.Empty;
        public string name { get; set; } = string.Empty;
        public string LoginStatus { get; set; } = string.Empty;
        public string ADLoginEnabled { get; set; } = string.Empty;
        public string ADBaseUri { get; set; } = string.Empty;
        public string ADRequestUri { get; set; } = string.Empty;
            
        public bool IsADLogin { get; set; } = false;
        public List<string> DBUsersList { get; set; } = new List<string>();
        public int SessionTimeOut { get; set; }

        public LoginViewModel LoginViewModel = new LoginViewModel();
    }
    public class User
    {
        public string UserName { get; set; } = string.Empty;
        public string Password { get; set; } = string.Empty;
        public string FirstName { get; set; } = string.Empty;
        public string MiddleName { get; set; } = string.Empty;
        public string LastName { get; set; } = string.Empty;
        public string Email { get; set; } = string.Empty;
        public string RFID { get; set; } = string.Empty;
        public string Designation { get; set; } = string.Empty;
        public string[] UserGroup { get; set; } = Array.Empty<string>();
        public string ErrorCode { get; set; } = string.Empty;
        public string message { get; set; } = string.Empty;
        public void SetSessionVariables()
        {

        }
    }
}
