﻿using iRISDashboard.Models;
using Microsoft.Data.Sqlite;
using System;
using System.Collections.Generic;

namespace iRISDashboard.DBObjects
{
    public class DbObjects
    {
        #region Sqlite

        public string connectionString = "Data Source=./wwwroot/assets/Dashboard/assets/Sqlite/iRISDashboard.db;";

        public string LoadQueryFromFile(string queryFile)
        {
            return System.IO.File.ReadAllText(queryFile);
        }

        public string InsertNewSetting(UpdateAppSettings updateAppSettings)
        {
            string query = LoadQueryFromFile("./wwwroot/assets/Dashboard/assets/Sqlite/SqliteOperations.sql");
            string FinalQuery = query.Replace("NewRecordValue", "New Record").Replace("AppNameValue", updateAppSettings.AppName).Replace("DescriptionValue", updateAppSettings.Description).Replace("AppLinkValue", updateAppSettings.AppLink).Replace("AppTypeValue", updateAppSettings.AppType).Replace("ImgURLValue", updateAppSettings.ImgURL).Replace("ProjectValue", updateAppSettings.Project);
            var connection = new SqliteConnectionStringBuilder(connectionString).ToString();
            using (SqliteConnection conn = new SqliteConnection(connection))
            {
                conn.Open();
                using (SqliteCommand cmd = new SqliteCommand(FinalQuery, conn))
                {
                    cmd.ExecuteNonQuery();
                }
            }
            return "Success";
        }

        public  List<iRISDashboardViewModel> GetProjectSettings(string ProjectType)
        {
            List<iRISDashboardViewModel> SettingsList = new List<iRISDashboardViewModel>();
            string query = LoadQueryFromFile("./wwwroot/assets/Dashboard/assets/Sqlite/SqliteOperations.sql");
            string FinalQuery = query.Replace("SelectRecordValue", "Select Record").Replace("ProjectTypeValue", ProjectType);
            var connection = new SqliteConnectionStringBuilder(connectionString).ToString();
            using (SqliteConnection conn = new SqliteConnection(connection))
            {
                conn.Open();
                using (SqliteCommand cmd = new SqliteCommand(FinalQuery, conn))
                {
                    using (SqliteDataReader dr = cmd.ExecuteReader())
                    {
                        while (dr.Read())
                        {
                            var Settings = new iRISDashboardViewModel();
                            Settings.ID = (int)Convert.ToInt64(dr["ID"]);
                            Settings.AppLink = dr["AppLink"].ToString();
                            Settings.AppType = dr["AppType"].ToString();
                            Settings.AppName = dr["AppName"].ToString();
                            Settings.ImgURL = dr["ImgURL"].ToString();
                            Settings.Description = dr["Description"].ToString();
                            Settings.Project = dr["Project"].ToString();
                            SettingsList.Add(Settings);
                        }
                    }
                }
            }
            return SettingsList;
        } 
        public  List<SettingsViewModel> GetDashboardSettings()
        {
            List<SettingsViewModel> SettingsList = new List<SettingsViewModel>();
            string query = LoadQueryFromFile("./wwwroot/assets/Dashboard/assets/Sqlite/SqliteGetDashboardSettings.sql");
            string FinalQuery = query.Replace("SelectAllRecordValue", "Select All Record");
            var connection = new SqliteConnectionStringBuilder(connectionString).ToString();
            using (SqliteConnection conn = new SqliteConnection(connection))
            {
                conn.Open();
                using (SqliteCommand cmd = new SqliteCommand(FinalQuery, conn))
                {
                    using (SqliteDataReader dr = cmd.ExecuteReader())
                    {
                        while (dr.Read())
                        {
                            var Settings = new SettingsViewModel();
                            Settings.SettingName = dr["SettingName"].ToString();
                            Settings.Description = dr["Description"].ToString();
                            Settings.LinkToConfigure = dr["LinkToConfigure"].ToString();
                            Settings.Category = dr["Category"].ToString();
                            Settings.SettingIcon = dr["SettingIcon"].ToString();
                            SettingsList.Add(Settings);
                        }
                    }
                }
            }
            return SettingsList;
        }

        public string UpdateDashboardSetting(UpdateAppSettings updateAppSettings)
        {
            string query = LoadQueryFromFile("./wwwroot/assets/Dashboard/assets/Sqlite/SqliteOperations.sql");
            string FinalQuery = query.Replace("UpdateRecordValue", "Update Record").Replace("AppNameValue", updateAppSettings.AppName).Replace("DescriptionValue", updateAppSettings.Description).Replace("AppLinkValue", updateAppSettings.AppLink).Replace("AppTypeValue", updateAppSettings.AppType).Replace("ImgURLValue", updateAppSettings.ImgURL).Replace("ProjectValue", updateAppSettings.Project).Replace("IDValue", updateAppSettings.ID.ToString());
            var connection = new SqliteConnectionStringBuilder(connectionString).ToString();
            using (SqliteConnection conn = new SqliteConnection(connection))
            {
                conn.Open();
                using (SqliteCommand cmd = new SqliteCommand(FinalQuery, conn))
                {
                    cmd.ExecuteNonQuery();
                }
            }
            return "Success";
        }

        public string DeleteSetting(string SettingID)
        {
            string query = LoadQueryFromFile("./wwwroot/assets/Dashboard/assets/Sqlite/SqliteOperations.sql");
            string FinalQuery = query.Replace("DeleteRecordValue", "Delete Record").Replace("IDValue", SettingID);
            var connection = new SqliteConnectionStringBuilder(connectionString).ToString();
            using (SqliteConnection conn = new SqliteConnection(connection))
            {
                conn.Open();
                using (SqliteCommand cmd = new SqliteCommand(FinalQuery, conn))
                {
                   var data= cmd.ExecuteScalar();
                }
            }
            return "Success";
        }
        #endregion
    }
}
