using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;

namespace iRISHome.Controllers
{
    public class StatusController : Controller
    {
        private readonly IConfiguration _configuration;

        public StatusController(IConfiguration configuration)
        {
            _configuration = configuration;
        }

        /// <summary>
        /// Simple status page showing authentication configuration
        /// Access via: /Status
        /// </summary>
        public IActionResult Index()
        {
            var status = new
            {
                ApplicationName = "iRISHome",
                Timestamp = DateTime.UtcNow,
                Authentication = new
                {
                    KeycloakConfigured = !string.IsNullOrEmpty(_configuration["KeycloakAuthentication:OpenIdConnect:ClientId"]),
                    KeycloakAuthority = _configuration["KeycloakAuthentication:OpenIdConnect:Authority"],
                    AzureAppConfigConnected = !string.IsNullOrEmpty(_configuration["AppConfig:ConnectionString"])
                },
                Environment = new
                {
                    AspNetCoreEnvironment = Environment.GetEnvironmentVariable("ASPNETCORE_ENVIRONMENT"),
                    MachineName = Environment.MachineName,
                    UserName = Environment.UserName
                }
            };

            ViewBag.Status = status;
            return View();
        }
    }
}
