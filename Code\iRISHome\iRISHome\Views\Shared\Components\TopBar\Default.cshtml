﻿@using iRISHome.Models
@* @model NotificationViewModel *@
@inject Microsoft.AspNetCore.Http.IHttpContextAccessor HttpContextAccessor
@using Microsoft.AspNetCore.Http;
@{
    var session = HttpContextAccessor.HttpContext?.Session;
    var appurl = session?.GetString("BaseURL") ?? string.Empty;
}
<!--Bookmarks section-->
@* <div class="dropdown d-inline-block">
    <button title="Favorites" type="button" class="btn header-item noti-icon waves-effect" id="page-header-notifications-dropdown"
            data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
        <i class="bx bx-bookmarks" style="font-size:27px;"></i>

    </button>
    
    <div class="dropdown-menu dropdown-menu dropdown-menuB-right"
         aria-labelledby="page-header-notifications-dropdown" style="width: 369px !important;" id="BookmarksWidth">
        <div data-simplebar style="max-height: 250px;">
            @if (Model.Bookmarks.Count <= 0)
            {
                <a href="#" class="dropdown-item notify-item" style="margin-left:-10px;">
                    Favorites is empty
                </a>
            }
            else
            {
                foreach (var bookmark in Model.Bookmarks)
                {
                    if (bookmark.BookmarkType == "Module")
                    {
                        <div class="mb-2">
                            <span title="Click to remove" data-id="@bookmark.Title" id="@bookmark.BookmarkDataID" bookmarkurl="" bookmarkstatus="1" class="@bookmark.BookmarkDataID bx bxs-star text-white fav_btn float-right mr-2" style="font-size:18px; cursor:pointer; color:#fd7106!important;"></span>
                        <a title="@bookmark.Title" href="@<EMAIL>" class="dropdown-item notify-item text-truncate" style="display:inline;">
                            @bookmark.Title
                        </a>
                        </div>
                       
                    }
                    else if (bookmark.BookmarkType == "Report")
                    {
                        <div  class="mb-2">
                            @{if (bookmark.Title.Length >= 39)
                                {
                                    <span title="Click to remove" data-id="@bookmark.Title" id="@bookmark.BookmarkDataID" bookmarkurl="" bookmarkstatus="1" class="@bookmark.BookmarkDataID bx bxs-star text-white fav_btn float-right mr-2" style="font-size:18px; cursor:pointer;color:#fd7106!important;"></span>

                                    string url = appurl + "/" + bookmark.BookmarkUrl + "?ReportName=" + bookmark.Title;

                                    <a title="@bookmark.Title" href="@url" class="dropdown-item notify-item text-truncate BookmarksName" style="display:inline;">

                                        <span>
                                            <span> @bookmark.Title.Substring(0, 40)</span> 
                                            <br />
                                            <span style="margin-left:23px;">@bookmark.Title.Substring(40)</span>
                                        </span>

                                    </a>
                                    
                            }
                                else
                                {
                                     <span title="Click to remove" data-id="@bookmark.Title" id="@bookmark.BookmarkDataID" bookmarkurl="" bookmarkstatus="1" class="@bookmark.BookmarkDataID bx bxs-star text-white fav_btn float-right mr-2" style="font-size:18px; cursor:pointer;color:#fd7106!important;"></span>
                            
                                     string url = appurl+"/"+bookmark.BookmarkUrl+"?ReportName="+bookmark.Title;
                            
                                    <a title="@bookmark.Title" href="@url" class="dropdown-item notify-item text-truncate BookmarksName" style="display:inline;" >
                                
                                     @bookmark.Title
                                    
                                    </a>
                                }}
                               
                        </div>
                        
                    }
                }
            }

        </div>

    </div>
</div> *@
<!--Bookmarks section End-->

<!--Recents section-->
@* <div class="dropdown d-inline-block">
    <button type="button" class="btn header-item noti-icon waves-effect" id="page-header-notifications-dropdown"
            data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
        <i class="bx bx-history" style="font-size:27px;"></i>

    </button>
    <div class="dropdown-menu dropdown-menu dropdown-menu-right"
         aria-labelledby="page-header-notifications-dropdown" style="width: 250px !important;">
        <div data-simplebar style="max-height: 230px;">

            <!-- item-->
            @if (Model.UserHistory.Count <= 0)
            {
                <a href="#" class="dropdown-item notify-item history" style="margin-left:-10px;">
                    No recent history
                </a>
            }
            else
            {
                foreach (var history in Model.UserHistory)
                {
                    <a href="@<EMAIL>" class="dropdown-item notify-item history">
                        @history.ModuleName
                    </a>
                }
            }

        </div>

    </div>
</div> *@
<!--Recents section Ended-->


@* <div class="dropdown d-inline-block">
    <button type="button" class="btn header-item noti-icon waves-effect" id="page-header-notifications-dropdown"
            data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
        <i class="bx bx-bell bx-tada"></i>
        @if ((Model.Notifications.Count > 0) && (Convert.ToInt32(Model.Notifications.Sum(x => int.Parse(x.AlertCount))) > 0))
        {
            <span class="badge bg-danger rounded-pill">@Model.Notifications.Sum(x => int.Parse(x.AlertCount))</span>
            <span class="badge badge-danger rounded-pill">@Model.Notifications.Sum(x => int.Parse(x.AlertCount))</span>
        }
        
    </button>
     @if ((Model.Notifications.Count > 0) && (Convert.ToInt32(Model.Notifications.Sum(x => int.Parse(x.AlertCount))) > 0))
    {
        <div class="dropdown-menu dropdown-menu-lg dropdown-menu-end p-0"
        aria-labelledby="page-header-notifications-dropdown">
        <div class="p-3">
            <div class="row align-items-center">
                <div class="col">
                    <h6 class="m-0" key="t-notifications"> Notifications </h6>
                </div>
                <div class="col-auto">
                    <a href="#!" class="small" key="t-view-all"> View All</a>
                </div>
            </div>
        </div>
         @foreach (var notification in Model.Notifications)
            {
                if (notification.AlertCount != "0")
                {
        <div data-simplebar style="max-height: 230px;">
                        <a href="@<EMAIL>" class="text-reset notification-item">
                <div class="d-flex">
                    <div class="avatar-xs me-3">
                        <span class="avatar-title bg-danger rounded-circle font-size-16">
                                        <b class="">@notification.AlertCount</b>
                        </span>
                    </div>
                    <div class="flex-grow-1">
                                    <h6 class="mb-1" key="t-your-order">@notification.NotificationLabel</h6>
                        <div class="font-size-12 text-muted">
                                        <p class="mb-1" key="t-grammer">@notification.NotificationDetails</p>
                        </div>
                    </div>
                </div>
            </a>
           
        </div>
                }
            }
        <div class="p-2 border-top d-grid">
            <a class="btn btn-sm btn-link font-size-14 text-center" href="javascript:void(0)">
                <i class="mdi mdi-arrow-right-circle me-1"></i> <span key="t-view-more">View More..</span>
            </a>
        </div>
    </div>
    }
        
</div> *@






