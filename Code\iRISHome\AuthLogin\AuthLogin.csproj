﻿<Project Sdk="Microsoft.NET.Sdk.Razor">  

  <ItemGroup>
    <FrameworkReference Include="Microsoft.AspNetCore.App" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Authentication.OpenIdConnect" />
    <PackageReference Include="Microsoft.Data.Sqlite.Core" />
    <PackageReference Include="Microsoft.Data.SqlClient" />
    <PackageReference Include="Microsoft.Identity.Client" />
    <PackageReference Include="System.Data.SqlClient" />
    <PackageReference Include="Newtonsoft.Json" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="AdvancedEncryptionStandard">
      <HintPath>..\iRISHome\Projects\AdvancedEncryptionStandard.dll</HintPath>
    </Reference>
    <Reference Include="JSONLogger">
      <HintPath>..\iRISHome\Projects\JSONLogger.dll</HintPath>
    </Reference>
    <Reference Include="PasswordUtil">
      <HintPath>..\iRISHome\Projects\PasswordUtil.dll</HintPath>
    </Reference>
    <Reference Include="TwoFactorAuthentication">
      <HintPath>..\iRISHome\Projects\TwoFactorAuthentication.dll</HintPath>
    </Reference>
  </ItemGroup>

</Project>
