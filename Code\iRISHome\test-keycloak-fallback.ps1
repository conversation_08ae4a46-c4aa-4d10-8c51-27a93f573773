# Test script for Keycloak configuration fallback mechanism
# This script demonstrates how the fallback works by temporarily modifying configuration

Write-Host "=== Testing Keycloak Configuration Fallback Mechanism ===" -ForegroundColor Green

# Store original appsettings.json content
$originalAppsettings = Get-Content "iRISHome/appsettings.json" -Raw
$appsettingsPath = "iRISHome/appsettings.json"

Write-Host "`n1. Testing with valid appsettings.json configuration..." -ForegroundColor Yellow

# Create test configuration in appsettings.json
$testConfig = @"
{
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning"
    }
  },
  "AllowedHosts": "*",
  "ADSettings": {
    "LOGIN_TYPE": "2",
    "AD_BASE_URI": "http://irisbloodsrv01",
    "AD_REQUEST_URI": "/irisecureservice/ActiveDirectory/UserAuthentication/"
  },
  "TwoFactorAuthKey": "M1t1g@t0r",
  "Office365Settings": {
    "ClinetID": "12d4ad27-5aa5-4d4d-9104-93e15a0c7125",
    "ClientSecret": "XNeao.b1b4VD-sD6eK2A09-Acyzw-Kyz2v",
    "AuthorityLoginUrl": "https://login.microsoftonline.com/common/oauth2/authorize/",
    "AuthorityLogoutUrl": "https://login.windows.net/common/oauth2/logout/",
    "AppRedirectUrl": "https://localhost:7085/SSO/RedirectUrl",
    "Scopes": [ "user.read" ],
    "Active": "1"
  },
  "BaseUrl": {
    "URL": ""
  },  
  "AppConfig": {
    "Endpoint": "https://mah9-all-all0-cfg-d01.azconfig.io",
    "ConnectionString": "Endpoint=https://mah9-all-all0-cfg-d01.azconfig.io;Id=****;Secret=FiisVkKme8E0Aw7IRV92PmQ8zRZBP5lVqcNBT249b0DjEguaPObWJQQJ99BFACHYHv6csuC5AAABAZACoqOL"
  },
  "KeycloakAuthentication": {
    "OpenIdConnect": {
      "ClientId": "test-client-from-appsettings",
      "ClientSecret": "test-secret-from-appsettings",
      "Authority": "https://test-keycloak.example.com/realms/test-realm"
    }
  }
}
"@

# Write test configuration
$testConfig | Out-File -FilePath $appsettingsPath -Encoding UTF8

Write-Host "✅ Test configuration written to appsettings.json" -ForegroundColor Green
Write-Host "   - ClientId: test-client-from-appsettings"
Write-Host "   - Authority: https://test-keycloak.example.com/realms/test-realm"

Write-Host "`n2. Expected behavior when running the application:" -ForegroundColor Yellow
Write-Host "   - If Azure App Configuration has Keycloak settings: Uses Azure values"
Write-Host "   - If Azure App Configuration is missing Keycloak settings: Uses appsettings.json values"
Write-Host "   - If Azure App Configuration is unavailable: Uses appsettings.json values"

Write-Host "`n3. Console output to look for:" -ForegroundColor Yellow
Write-Host "   - '=== TRYING AZURE APP CONFIGURATION ===' - Shows Azure attempt"
Write-Host "   - '=== USING APPSETTINGS.JSON FALLBACK ===' - Shows fallback activation"
Write-Host "   - '=== FINAL CONFIGURATION SOURCE ===' - Shows which source was used for each value"

Write-Host "`n4. To test the fallback mechanism:" -ForegroundColor Cyan
Write-Host "   a) Run the application: dotnet run --project iRISHome"
Write-Host "   b) Check console output for configuration source messages"
Write-Host "   c) Look for 'ClientId source:', 'ClientSecret source:', 'Authority source:' messages"

Write-Host "`n5. Test scenarios:" -ForegroundColor Cyan
Write-Host "   Scenario A: Azure App Config has all Keycloak values"
Write-Host "   → Should see: 'ALL VALUES FOUND IN AZURE APP CONFIGURATION'"
Write-Host ""
Write-Host "   Scenario B: Azure App Config missing some/all Keycloak values"
Write-Host "   → Should see: 'USING APPSETTINGS.JSON FALLBACK'"
Write-Host "   → Should see: Individual source indicators for each value"
Write-Host ""
Write-Host "   Scenario C: Azure App Config unavailable"
Write-Host "   → Should see: 'Warning: Azure App Configuration not configured'"
Write-Host "   → Should see: 'USING APPSETTINGS.JSON FALLBACK'"

Write-Host "`n6. Restoring original configuration..." -ForegroundColor Yellow

# Restore original configuration
$originalAppsettings | Out-File -FilePath $appsettingsPath -Encoding UTF8 -NoNewline

Write-Host "✅ Original appsettings.json restored" -ForegroundColor Green

Write-Host "`n=== Test Setup Complete ===" -ForegroundColor Green
Write-Host "The fallback mechanism is now implemented and ready for testing." -ForegroundColor White
Write-Host "Run 'dotnet run --project iRISHome' to see the fallback in action." -ForegroundColor White
