﻿using iRISHome.DbFunctions;
using iRISHome.Models;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;

namespace iRISHome.ViewComponents
{
    public class TopBarViewComponent : ViewComponent
    {
        public IViewComponentResult Invoke()
        {
            //    //DBObjects dbo = new DBObjects();
            //    //int userid = (int)HttpContext.Session.GetInt32("UserID");
            //    //var Notifications = dbo.GetNotificationData(userid);
            //    //NotificationViewModel Notifications= new NotificationViewModel();    
            return View();

        }
    }
}
