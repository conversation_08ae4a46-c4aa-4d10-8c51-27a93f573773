[{"Timestamp": "2024-08-22 02:28:37", "LogType": "Info", "Message": "Loading hardware module handler", "Username": "Application", "FilePath": "HardwareCommands.cs > .ctor > 81", "ModuleName": "iRISHardware", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-22 02:28:39", "LogType": "Info", "Message": "Loaded hardware module handler successfully", "Username": "Application", "FilePath": "HardwareCommands.cs > .ctor > 86", "ModuleName": "iRISHardware", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-22 02:28:39", "LogType": "Info", "Message": "Fetched Help information", "Username": "Application", "FilePath": "DbObjects.cs > GetHelpInfo > 87", "ModuleName": "iRISecureDBObjects", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-22 02:28:39", "LogType": "Info", "Message": "Splash Screen Loaded Successfully", "Username": "Application", "FilePath": "SplashScreenCS.cs > SplashScreenOnLoad > 468", "ModuleName": "iRISecureSpecimen", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-22 09:37:09", "LogType": "Hardware", "Message": "Response from hardware: pModName: HID_01 pTagID: 37593415670208963541 pTagFormat: UserID", "Username": "Application", "FilePath": "HardwareCommands.cs > ModProcessResponse > 138", "ModuleName": "iRISHardware", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-22 09:37:09", "LogType": "Info", "Message": "RFID Scanned : 37593415670208963541", "Username": "Application", "FilePath": "SplashScreenCS.cs > OnRFIDFoundEventHandler > 224", "ModuleName": "iRISecureSpecimen", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-22 09:37:10", "LogType": "Info", "Message": "User Logged in Succesfully", "Username": "<PERSON>", "FilePath": "SplashScreenCS.cs > ValidatingUser > 795", "ModuleName": "iRISecureSpecimen", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-22 09:37:17", "LogType": "Info", "Message": "Fetched Help information", "Username": "Application", "FilePath": "DbObjects.cs > GetHelpInfo > 87", "ModuleName": "iRISecureDBObjects", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-22 09:37:17", "LogType": "Info", "Message": "Splash Screen Loaded Successfully", "Username": "Application", "FilePath": "SplashScreenCS.cs > SplashScreenOnLoad > 468", "ModuleName": "iRISecureSpecimen", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-22 09:37:17", "LogType": "Info", "Message": "LoginReason - Button Clicked : <PERSON><PERSON>ut", "Username": "<PERSON>", "FilePath": "LoginReasonWindowCS.cs > ButtonLogoutClick > 196", "ModuleName": "iRISecureSpecimen", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-22 09:37:17", "LogType": "Info", "Message": "User Logged out Succesfully", "Username": "<PERSON>", "FilePath": "LoginReasonWindowCS.cs > ButtonLogoutClick > 197", "ModuleName": "iRISecureSpecimen", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-22 09:37:17", "LogType": "Info", "Message": "Session Cleared LoginReason Screen", "Username": "", "FilePath": "LoginReasonWindowCS.cs > ClearUserSession > 214", "ModuleName": "iRISecureSpecimen", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-22 14:06:46", "LogType": "Hardware", "Message": "Response from hardware: pModName: HID_01 pTagID: 37593415670317105913 pTagFormat: UserID", "Username": "Application", "FilePath": "HardwareCommands.cs > ModProcessResponse > 138", "ModuleName": "iRISHardware", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-22 14:06:46", "LogType": "Info", "Message": "RFID Scanned : 37593415670317105913", "Username": "Application", "FilePath": "SplashScreenCS.cs > OnRFIDFoundEventHandler > 224", "ModuleName": "iRISecureSpecimen", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-22 14:06:47", "LogType": "Info", "Message": "User Logged in Succesfully", "Username": "<PERSON>", "FilePath": "SplashScreenCS.cs > ValidatingUser > 795", "ModuleName": "iRISecureSpecimen", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-22 14:07:00", "LogType": "Info", "Message": "Menu screen,clicked on cyto flow module", "Username": "<PERSON>", "FilePath": "LoginReasonWindowCS.cs > ButtonCytoFlowClick > 350", "ModuleName": "iRISecureSpecimen", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-22 14:07:00", "LogType": "Info", "Message": "Main Menu Page Loaded", "Username": "<PERSON>", "FilePath": "MainMenu.xaml.cs > Page_Loaded > 86", "ModuleName": "iRISecureSpecimen", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-22 14:07:00", "LogType": "Info", "Message": "Cyto Flow hardware initialized", "Username": "<PERSON>", "FilePath": "CytoFlow.xaml.cs > HardwareInitialize > 78", "ModuleName": "RemoveBloodBag", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-22 14:07:00", "LogType": "Info", "Message": "Door Unlock Command Initiated", "Username": "Application", "FilePath": "HardwareCommands.cs > UnlockAllCompartments > 383", "ModuleName": "iRISHardware", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-22 14:07:00", "LogType": "Info", "Message": "Cyto flow Page Loaded", "Username": "<PERSON>", "FilePath": "CytoFlow.xaml.cs > CytoFlowPage_Loaded > 46", "ModuleName": "RemoveBloodBag", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-22 14:11:47", "LogType": "Info", "Message": "MainMenu - <PERSON>ton Clicked : <PERSON><PERSON>ut", "Username": "<PERSON>", "FilePath": "MainMenu.xaml.cs > Logout_Click > 175", "ModuleName": "iRISecureSpecimen", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-22 14:11:47", "LogType": "Info", "Message": "Door Lock Command Initiated", "Username": "Application", "FilePath": "HardwareCommands.cs > LockAllCompartments > 394", "ModuleName": "iRISHardware", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-22 14:11:47", "LogType": "Info", "Message": "Fetched Help information", "Username": "Application", "FilePath": "DbObjects.cs > GetHelpInfo > 87", "ModuleName": "iRISecureDBObjects", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-22 14:11:47", "LogType": "Info", "Message": "Splash Screen Loaded Successfully", "Username": "Application", "FilePath": "SplashScreenCS.cs > SplashScreenOnLoad > 468", "ModuleName": "iRISecureSpecimen", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-22 14:11:47", "LogType": "Info", "Message": "User Logged out Succesfully", "Username": "<PERSON>", "FilePath": "MainMenu.xaml.cs > Logout_Click > 195", "ModuleName": "iRISecureSpecimen", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-22 14:11:47", "LogType": "Info", "Message": "Session Cleared <PERSON>u <PERSON>", "Username": "Application", "FilePath": "MainMenu.xaml.cs > ClearUserSession > 212", "ModuleName": "iRISecureSpecimen", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-22 14:11:47", "LogType": "Info", "Message": "<PERSON>u Menu Page_UnLoaded", "Username": "", "FilePath": "MainMenu.xaml.cs > Page_Unloaded > 283", "ModuleName": "iRISecureSpecimen", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-22 14:11:47", "LogType": "Info", "Message": "Cyto Flow hardware instances destroyed", "Username": "", "FilePath": "CytoFlow.xaml.cs > CytoFlowPage_Unloaded > 62", "ModuleName": "RemoveBloodBag", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-22 14:11:47", "LogType": "Info", "Message": "Cyto flow Page UnLoaded", "Username": "", "FilePath": "CytoFlow.xaml.cs > CytoFlowPage_Unloaded > 66", "ModuleName": "RemoveBloodBag", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-22 14:11:59", "LogType": "Hardware", "Message": "Response from hardware: pModName: HID_01 pTagID: 37593415670208963541 pTagFormat: UserID", "Username": "Application", "FilePath": "HardwareCommands.cs > ModProcessResponse > 138", "ModuleName": "iRISHardware", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-22 14:11:59", "LogType": "Info", "Message": "RFID Scanned : 37593415670208963541", "Username": "Application", "FilePath": "SplashScreenCS.cs > OnRFIDFoundEventHandler > 224", "ModuleName": "iRISecureSpecimen", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-22 14:11:59", "LogType": "Info", "Message": "User Logged in Succesfully", "Username": "<PERSON>", "FilePath": "SplashScreenCS.cs > ValidatingUser > 795", "ModuleName": "iRISecureSpecimen", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-22 14:12:01", "LogType": "Info", "Message": "Menu screen,clicked on cyto flow module", "Username": "<PERSON>", "FilePath": "LoginReasonWindowCS.cs > ButtonCytoFlowClick > 350", "ModuleName": "iRISecureSpecimen", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-22 14:12:01", "LogType": "Info", "Message": "Main Menu Page Loaded", "Username": "<PERSON>", "FilePath": "MainMenu.xaml.cs > Page_Loaded > 86", "ModuleName": "iRISecureSpecimen", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-22 14:12:01", "LogType": "Info", "Message": "Cyto Flow hardware initialized", "Username": "<PERSON>", "FilePath": "CytoFlow.xaml.cs > HardwareInitialize > 78", "ModuleName": "RemoveBloodBag", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-22 14:12:01", "LogType": "Info", "Message": "Door Unlock Command Initiated", "Username": "Application", "FilePath": "HardwareCommands.cs > UnlockAllCompartments > 383", "ModuleName": "iRISHardware", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-22 14:12:01", "LogType": "Info", "Message": "Cyto flow Page Loaded", "Username": "<PERSON>", "FilePath": "CytoFlow.xaml.cs > CytoFlowPage_Loaded > 46", "ModuleName": "RemoveBloodBag", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-22 14:13:47", "LogType": "Info", "Message": "MainMenu - <PERSON>ton Clicked : <PERSON><PERSON>ut", "Username": "<PERSON>", "FilePath": "MainMenu.xaml.cs > Logout_Click > 175", "ModuleName": "iRISecureSpecimen", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-22 14:13:47", "LogType": "Info", "Message": "Door Lock Command Initiated", "Username": "Application", "FilePath": "HardwareCommands.cs > LockAllCompartments > 394", "ModuleName": "iRISHardware", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-22 14:13:47", "LogType": "Info", "Message": "Fetched Help information", "Username": "Application", "FilePath": "DbObjects.cs > GetHelpInfo > 87", "ModuleName": "iRISecureDBObjects", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-22 14:13:47", "LogType": "Info", "Message": "Splash Screen Loaded Successfully", "Username": "Application", "FilePath": "SplashScreenCS.cs > SplashScreenOnLoad > 468", "ModuleName": "iRISecureSpecimen", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-22 14:13:47", "LogType": "Info", "Message": "User Logged out Succesfully", "Username": "<PERSON>", "FilePath": "MainMenu.xaml.cs > Logout_Click > 195", "ModuleName": "iRISecureSpecimen", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-22 14:13:47", "LogType": "Info", "Message": "Session Cleared <PERSON>u <PERSON>", "Username": "Application", "FilePath": "MainMenu.xaml.cs > ClearUserSession > 212", "ModuleName": "iRISecureSpecimen", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-22 14:13:47", "LogType": "Info", "Message": "<PERSON>u Menu Page_UnLoaded", "Username": "", "FilePath": "MainMenu.xaml.cs > Page_Unloaded > 283", "ModuleName": "iRISecureSpecimen", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-22 14:13:47", "LogType": "Info", "Message": "Cyto Flow hardware instances destroyed", "Username": "", "FilePath": "CytoFlow.xaml.cs > CytoFlowPage_Unloaded > 62", "ModuleName": "RemoveBloodBag", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-22 14:13:47", "LogType": "Info", "Message": "Cyto flow Page UnLoaded", "Username": "", "FilePath": "CytoFlow.xaml.cs > CytoFlowPage_Unloaded > 66", "ModuleName": "RemoveBloodBag", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-23 11:34:35", "LogType": "Hardware", "Message": "Response from hardware: pModName: HID_01 pTagID: 37593415600048216161 pTagFormat: UserID", "Username": "Application", "FilePath": "HardwareCommands.cs > ModProcessResponse > 138", "ModuleName": "iRISHardware", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-23 11:34:35", "LogType": "Info", "Message": "RFID Scanned : 37593415600048216161", "Username": "Application", "FilePath": "SplashScreenCS.cs > OnRFIDFoundEventHandler > 224", "ModuleName": "iRISecureSpecimen", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-23 11:34:35", "LogType": "Info", "Message": "User Logged in Succesfully", "Username": "<PERSON>", "FilePath": "SplashScreenCS.cs > ValidatingUser > 795", "ModuleName": "iRISecureSpecimen", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-23 11:34:38", "LogType": "Info", "Message": "MainMenu - <PERSON><PERSON> Clicked: Drop Specimen", "Username": "<PERSON>", "FilePath": "LoginReasonWindowCS.cs > ButtonReturnBloodBagClick > 318", "ModuleName": "iRISecureSpecimen", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-23 11:34:38", "LogType": "Info", "Message": "Main Menu Page Loaded", "Username": "<PERSON>", "FilePath": "MainMenu.xaml.cs > Page_Loaded > 86", "ModuleName": "iRISecureSpecimen", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-23 11:34:38", "LogType": "Info", "Message": "Drop specimen hardware initialized", "Username": "<PERSON>", "FilePath": "ReturnBloodBagCS.cs > HardwareInitialize > 136", "ModuleName": "RemoveBloodBag", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-23 11:34:38", "LogType": "Info", "Message": "Door Unlock Command Initiated", "Username": "Application", "FilePath": "HardwareCommands.cs > UnlockAllCompartments > 383", "ModuleName": "iRISHardware", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-23 11:34:38", "LogType": "Info", "Message": "Drop Specimen Page Loaded", "Username": "<PERSON>", "FilePath": "ReturnBloodBag.xaml.cs > Page_Loaded > 99", "ModuleName": "RemoveBloodBag", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-23 11:34:43", "LogType": "Hardware", "Message": "Response from hardware: pModName: BCIO_01 pTagID: 24-236-05498B             pTagFormat: UserID", "Username": "Application", "FilePath": "HardwareCommands.cs > ModProcessResponse > 138", "ModuleName": "iRISHardware", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-23 11:34:43", "LogType": "Info", "Message": "Drop Specimen : Barcode Scanned24-236-05498B", "Username": "<PERSON>", "FilePath": "ReturnBloodBagCS.cs > RFID_Scanned > 363", "ModuleName": "RemoveBloodBag", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-23 11:34:43", "LogType": "Info", "Message": "Drop Specimen : Fetching data from database", "Username": "<PERSON>", "FilePath": "DbObjects.cs > ReturnItem > 1547", "ModuleName": "iRISecureDBObjects", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-23 11:34:43", "LogType": "Info", "Message": "Specimen dropped successfully", "Username": "<PERSON>", "FilePath": "DbObjects.cs > ReturnItem > 1578", "ModuleName": "iRISecureDBObjects", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-23 11:34:43", "LogType": "Info", "Message": "Drop Specimen : Data fetched from database ", "Username": "<PERSON>", "FilePath": "DbObjects.cs > ReturnItem > 1614", "ModuleName": "iRISecureDBObjects", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-23 11:35:11", "LogType": "Info", "Message": "MainMenu - <PERSON>ton Clicked : <PERSON><PERSON>ut", "Username": "<PERSON>", "FilePath": "MainMenu.xaml.cs > Logout_Click > 175", "ModuleName": "iRISecureSpecimen", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-23 11:35:11", "LogType": "Info", "Message": "Door Lock Command Initiated", "Username": "Application", "FilePath": "HardwareCommands.cs > LockAllCompartments > 394", "ModuleName": "iRISHardware", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-23 11:35:12", "LogType": "Info", "Message": "Fetched Help information", "Username": "Application", "FilePath": "DbObjects.cs > GetHelpInfo > 87", "ModuleName": "iRISecureDBObjects", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-23 11:35:12", "LogType": "Info", "Message": "Splash Screen Loaded Successfully", "Username": "Application", "FilePath": "SplashScreenCS.cs > SplashScreenOnLoad > 468", "ModuleName": "iRISecureSpecimen", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-23 11:35:12", "LogType": "Info", "Message": "User Logged out Succesfully", "Username": "<PERSON>", "FilePath": "MainMenu.xaml.cs > Logout_Click > 195", "ModuleName": "iRISecureSpecimen", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-23 11:35:12", "LogType": "Info", "Message": "Session Cleared <PERSON>u <PERSON>", "Username": "Application", "FilePath": "MainMenu.xaml.cs > ClearUserSession > 212", "ModuleName": "iRISecureSpecimen", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-23 11:35:12", "LogType": "Info", "Message": "<PERSON>u Menu Page_UnLoaded", "Username": "", "FilePath": "MainMenu.xaml.cs > Page_Unloaded > 283", "ModuleName": "iRISecureSpecimen", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-23 11:35:12", "LogType": "Info", "Message": "Drop specimen hardware instances destoyed", "Username": "", "FilePath": "ReturnBloodBagCS.cs > PageUnloaded > 305", "ModuleName": "RemoveBloodBag", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-23 11:35:12", "LogType": "Info", "Message": "Drop Specimen Page UnLoaded", "Username": "", "FilePath": "ReturnBloodBagCS.cs > PageUnloaded > 309", "ModuleName": "RemoveBloodBag", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-26 05:47:47", "LogType": "Hardware", "Message": "Response from hardware: pModName: HID_01 pTagID: 37593415670263962775 pTagFormat: UserID", "Username": "Application", "FilePath": "HardwareCommands.cs > ModProcessResponse > 138", "ModuleName": "iRISHardware", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-26 05:47:47", "LogType": "Info", "Message": "RFID Scanned : 37593415670263962775", "Username": "Application", "FilePath": "SplashScreenCS.cs > OnRFIDFoundEventHandler > 224", "ModuleName": "iRISecureSpecimen", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-26 05:47:47", "LogType": "Info", "Message": "User Logged in Succesfully", "Username": "<PERSON>", "FilePath": "SplashScreenCS.cs > ValidatingUser > 795", "ModuleName": "iRISecureSpecimen", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-26 05:47:48", "LogType": "Info", "Message": "MainMenu - <PERSON><PERSON> Clicked: Check Stock", "Username": "<PERSON>", "FilePath": "LoginReasonWindowCS.cs > ButtonCheckSpecimenStockClick > 403", "ModuleName": "iRISecureSpecimen", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-26 05:47:48", "LogType": "Info", "Message": "Check stock page blood bag hardwares initialized", "Username": "<PERSON>", "FilePath": "CheckSpecimenStocksCS.cs > HardwareInitialize > 170", "ModuleName": "RemoveBloodBag", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-26 05:47:48", "LogType": "Info", "Message": "Door Unlock Command Initiated", "Username": "Application", "FilePath": "HardwareCommands.cs > UnlockAllCompartments > 383", "ModuleName": "iRISHardware", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-26 05:47:48", "LogType": "Info", "Message": "Check Stock Specimen : Fetching data from database for getting inventory list", "Username": "<PERSON>", "FilePath": "DbObjects.cs > GetStockList > 2839", "ModuleName": "iRISecureDBObjects", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-26 05:47:48", "LogType": "Info", "Message": "Check Stock Specimen : Fetched data from database", "Username": "<PERSON>", "FilePath": "DbObjects.cs > GetStockList > 2870", "ModuleName": "iRISecureDBObjects", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-26 05:47:48", "LogType": "Info", "Message": "Check Stock Page Loaded", "Username": "<PERSON>", "FilePath": "CheckSpecimenStocksCS.cs > UpdateUIOnPageLoad > 83", "ModuleName": "RemoveBloodBag", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-26 05:47:48", "LogType": "Info", "Message": "Main Menu Page Loaded", "Username": "<PERSON>", "FilePath": "MainMenu.xaml.cs > Page_Loaded > 86", "ModuleName": "iRISecureSpecimen", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-26 05:48:18", "LogType": "Info", "Message": "MainMenu - <PERSON><PERSON> Clicked : Home", "Username": "<PERSON>", "FilePath": "MainMenu.xaml.cs > Home_Click > 97", "ModuleName": "iRISecureSpecimen", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-26 05:48:18", "LogType": "Info", "Message": "Door Lock Command Initiated", "Username": "Application", "FilePath": "HardwareCommands.cs > LockAllCompartments > 394", "ModuleName": "iRISHardware", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-26 05:48:18", "LogType": "Info", "Message": "<PERSON>u Menu Page_UnLoaded", "Username": "<PERSON>", "FilePath": "MainMenu.xaml.cs > Page_Unloaded > 283", "ModuleName": "iRISecureSpecimen", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-26 05:48:18", "LogType": "Info", "Message": "Check Stock hardware instances destroyed", "Username": "<PERSON>", "FilePath": "CheckSpecimenStocksCS.cs > PageUnloaded > 433", "ModuleName": "RemoveBloodBag", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-26 05:48:18", "LogType": "Info", "Message": "Check Stock Page UnLoaded", "Username": "<PERSON>", "FilePath": "CheckSpecimenStocksCS.cs > PageUnloaded > 436", "ModuleName": "RemoveBloodBag", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-26 05:48:26", "LogType": "Info", "Message": "Fetched Help information", "Username": "Application", "FilePath": "DbObjects.cs > GetHelpInfo > 87", "ModuleName": "iRISecureDBObjects", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-26 05:48:26", "LogType": "Info", "Message": "Splash Screen Loaded Successfully", "Username": "Application", "FilePath": "SplashScreenCS.cs > SplashScreenOnLoad > 468", "ModuleName": "iRISecureSpecimen", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-26 05:48:26", "LogType": "Info", "Message": "LoginReason - Button Clicked : <PERSON><PERSON>ut", "Username": "<PERSON>", "FilePath": "LoginReasonWindowCS.cs > ButtonLogoutClick > 196", "ModuleName": "iRISecureSpecimen", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-26 05:48:26", "LogType": "Info", "Message": "User Logged out Succesfully", "Username": "<PERSON>", "FilePath": "LoginReasonWindowCS.cs > ButtonLogoutClick > 197", "ModuleName": "iRISecureSpecimen", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-26 05:48:26", "LogType": "Info", "Message": "Session Cleared LoginReason Screen", "Username": "", "FilePath": "LoginReasonWindowCS.cs > ClearUserSession > 214", "ModuleName": "iRISecureSpecimen", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-26 07:16:33", "LogType": "Hardware", "Message": "Response from hardware: pModName: HID_01 pTagID: 37593415670208963541 pTagFormat: UserID", "Username": "Application", "FilePath": "HardwareCommands.cs > ModProcessResponse > 138", "ModuleName": "iRISHardware", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-26 07:16:33", "LogType": "Info", "Message": "RFID Scanned : 37593415670208963541", "Username": "Application", "FilePath": "SplashScreenCS.cs > OnRFIDFoundEventHandler > 224", "ModuleName": "iRISecureSpecimen", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-26 07:16:33", "LogType": "Info", "Message": "User Logged in Succesfully", "Username": "<PERSON>", "FilePath": "SplashScreenCS.cs > ValidatingUser > 795", "ModuleName": "iRISecureSpecimen", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-26 07:16:35", "LogType": "Info", "Message": "Menu screen,clicked on cyto flow module", "Username": "<PERSON>", "FilePath": "LoginReasonWindowCS.cs > ButtonCytoFlowClick > 350", "ModuleName": "iRISecureSpecimen", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-26 07:16:35", "LogType": "Info", "Message": "Main Menu Page Loaded", "Username": "<PERSON>", "FilePath": "MainMenu.xaml.cs > Page_Loaded > 86", "ModuleName": "iRISecureSpecimen", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-26 07:16:35", "LogType": "Info", "Message": "Cyto Flow hardware initialized", "Username": "<PERSON>", "FilePath": "CytoFlow.xaml.cs > HardwareInitialize > 78", "ModuleName": "RemoveBloodBag", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-26 07:16:35", "LogType": "Info", "Message": "Door Unlock Command Initiated", "Username": "Application", "FilePath": "HardwareCommands.cs > UnlockAllCompartments > 383", "ModuleName": "iRISHardware", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-26 07:16:35", "LogType": "Info", "Message": "Cyto flow Page Loaded", "Username": "<PERSON>", "FilePath": "CytoFlow.xaml.cs > CytoFlowPage_Loaded > 46", "ModuleName": "RemoveBloodBag", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-26 07:16:47", "LogType": "Info", "Message": "MainMenu - <PERSON>ton Clicked : <PERSON><PERSON>ut", "Username": "<PERSON>", "FilePath": "MainMenu.xaml.cs > Logout_Click > 175", "ModuleName": "iRISecureSpecimen", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-26 07:16:47", "LogType": "Info", "Message": "Door Lock Command Initiated", "Username": "Application", "FilePath": "HardwareCommands.cs > LockAllCompartments > 394", "ModuleName": "iRISHardware", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-26 07:16:48", "LogType": "Info", "Message": "Fetched Help information", "Username": "Application", "FilePath": "DbObjects.cs > GetHelpInfo > 87", "ModuleName": "iRISecureDBObjects", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-26 07:16:48", "LogType": "Info", "Message": "Splash Screen Loaded Successfully", "Username": "Application", "FilePath": "SplashScreenCS.cs > SplashScreenOnLoad > 468", "ModuleName": "iRISecureSpecimen", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-26 07:16:48", "LogType": "Info", "Message": "User Logged out Succesfully", "Username": "<PERSON>", "FilePath": "MainMenu.xaml.cs > Logout_Click > 195", "ModuleName": "iRISecureSpecimen", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-26 07:16:48", "LogType": "Info", "Message": "Session Cleared <PERSON>u <PERSON>", "Username": "Application", "FilePath": "MainMenu.xaml.cs > ClearUserSession > 212", "ModuleName": "iRISecureSpecimen", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-26 07:16:48", "LogType": "Info", "Message": "<PERSON>u Menu Page_UnLoaded", "Username": "", "FilePath": "MainMenu.xaml.cs > Page_Unloaded > 283", "ModuleName": "iRISecureSpecimen", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-26 07:16:48", "LogType": "Info", "Message": "Cyto Flow hardware instances destroyed", "Username": "", "FilePath": "CytoFlow.xaml.cs > CytoFlowPage_Unloaded > 62", "ModuleName": "RemoveBloodBag", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-26 07:16:48", "LogType": "Info", "Message": "Cyto flow Page UnLoaded", "Username": "", "FilePath": "CytoFlow.xaml.cs > CytoFlowPage_Unloaded > 66", "ModuleName": "RemoveBloodBag", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-26 08:59:47", "LogType": "Hardware", "Message": "Response from hardware: pModName: HID_01 pTagID: 37593415660383947044 pTagFormat: UserID", "Username": "Application", "FilePath": "HardwareCommands.cs > ModProcessResponse > 138", "ModuleName": "iRISHardware", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-26 08:59:47", "LogType": "Info", "Message": "RFID Scanned : 37593415660383947044", "Username": "Application", "FilePath": "SplashScreenCS.cs > OnRFIDFoundEventHandler > 224", "ModuleName": "iRISecureSpecimen", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}, {"Timestamp": "2024-08-26 08:59:47", "LogType": "Info", "Message": "User Logged in Succesfully", "Username": "Lexi", "FilePath": "SplashScreenCS.cs > ValidatingUser > 795", "ModuleName": "iRISecureSpecimen", "DeviceName": "UABORSEC05", "Location": "5th Floor OR Refrigerator"}]