﻿<!DOCTYPE html>
<html lang="en">

<head>
    <partial name="_title_meta" />
    @RenderSection("styles", false)
    <partial name="_head_css" />
</head>

<body data-sidebar="dark">

    <!-- Begin page -->
    <div id="layout-wrapper">
        <partial name="_topbar" />
         @await Component.InvokeAsync("Menu") 
        @* <partial name="_sidebar" /> *@

        <!-- ============================================================== -->
        <!-- Start right Content here -->
        <!-- ============================================================== -->

        <div class="main-content">
            <div class="page-content">
                <div class="container-fluid">
                    <partial name="_page_title" />

                    @RenderBody()
                </div> <!-- container-fluid -->
            </div>
            <!-- End Page-content -->
            <partial name="_footer" />
        </div>
        <!-- end main content-->

    </div>

    @RenderSection("externalhtml", required: false)

    <!-- END layout-wrapper -->
    <partial name="_right_sidebar" />

    <partial name="_vendor_scripts" />
    @* <link rel="stylesheet" src="~/assets/Kendo/Bootstrap/Bootstrap-main.css" />
    <script src="https://kendo.cdn.telerik.com/2024.3.806/js/kendo.all.min.js"></script>
    <script src="~/assets/Kendo/JS/AspnetMvc.min.js"></script> *@
    @RenderSection("scripts", required: false)
    
</body>

</html>