﻿@using AuthLogin.Models
@model LoginViewModel
@{
    ViewBag.Title = "Login";
    ViewBag.pTitle = "Login";
    ViewBag.pageTitle = "Mobile Aspects";
    ViewBag.BrandName = "iRISHome";
    Layout = "~/Views/Shared/_BlankLayout.cshtml";
}

<script src="~/assets/Dashboard/assets/Vendors/JS/jquery-3.6.0.min.js"></script>
<div class="account-pages my-3 pt-sm-5">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-6 col-xl-5">
                <div class="card overflow-hidden">
                    <div class="bg-primary-subtle">
                        <div class="row">
                            <div class="col-7">
                                <div class="text-primary p-4">
                                    <h5 class="text-primary">iRISHome</h5>
                                    <p>Sign in.</p>
                                </div>
                            </div>
                            
                        </div>
                    </div>
                    <div class="card-body pt-0">
                        <div class="auth-logo">
                            <a href="#" class="auth-logo-light">
                                <div class="avatar-md profile-user-wid mb-2">
                                    <span class="avatar-title rounded-circle bg-light" style="display:contents;opacity: 3;">
                                        <img src="~/assets/images/maicon.png" alt="" class="rounded-circle" height="60">
                                    </span>
                                </div>
                            </a>

                            <a href="#" class="auth-logo-dark">
                                <div class="avatar-md profile-user-wid mb-2">
                                    <span class="avatar-title rounded-circle bg-light" style="display:contents;opacity: 3;">
                                        <img src="~/assets/images/maicon.png" alt="" class="rounded-circle" height="60">
                                    </span>
                                </div>
                            </a>
                        </div>
                       @*  <div class="p-2">
                            @if (Model.Features.Any(x => (x.FeatureName == "DB Login" || x.FeatureName == "AD Login") && x.Status == true))
                            {
                            <form class="form-horizontal" method="post" action=@Url.Action("AuthenticateLogin", "AuthLogin")>
                                @Html.AntiForgeryToken()
                                <div class="mb-3">
                                    <label for="username" class="form-label">Username</label>
                                    <input type="text" class="form-control" id="username" placeholder="Enter username" asp-for="@Model.UserName">
                                </div>

                                <div class="mb-3">
                                    <label class="form-label">Password</label>
                                    <div class="input-group auth-pass-inputgroup">
                                        <input type="password" class="form-control" placeholder="Enter password" aria-label="Password" aria-describedby="password-addon" asp-for="@Model.Password">
                                        <button class="btn btn-light " type="button" id="password-addon"><i class="mdi mdi-eye-outline"></i></button>
                                    </div>
                                </div>

                                <div class="mt-3 d-grid">
                                    <button class="btn btn-primary waves-effect waves-light" type="submit">Log In</button>
                                </div>
                               
                                    <div class="mt-4 text-center">
                                        <a href=@Url.Action("Recoverpw", "Auth") class="text-muted"><i class="mdi mdi-lock me-1"></i> Forgot your password?</a>
                                    </div>
                              
                            </form>
                            }
                        </div> *@
                       @*  @if (Model.Features.Where(x => x.FeatureName == "SSO Login").Select(x => x.Status).FirstOrDefault())
                        { *@
                            @* <div class="text-center">
                                <button type="button" class="btn btn-primary" onclick="location.href='@Url.Action("Login", "SSO")'">SSO-Login using office 365 account.</button>
                            </div> *@
                            <div class="text-center mt-3">
                        @* <button type="button" class="btn btn-primary KeyclockBTN" onclick="location.href='@Url.Action("KeycloakValidation", "AuthLogin")'"><i></i> Login using Keycloak</button> *@
                        <button type="button" class="btn btn-primary KeyclockBTN" onclick="location.href='@Url.Action("TestIndex", "iRISDashboard")'"><i></i> Login using Keycloak</button>
                        </div>
                       @*  } *@
                    </div>
                </div>
                <div class="mt-3 text-center">

                    <div>
                        <p>
                            Powered by Mobile Aspects. All rights reserved.
                        </p>
                        <p>Version @ViewBag.ApplicationVersion</p>
                    </div>
                </div>

            </div>
        </div>
    </div>
</div>


<script>
    // $(document).ready(function(){
    //     $(".KeyclockBTN").click();
    // });
</script>

<!-- end account-pages -->
@section scripts {
    <!-- App js -->
    <script src="~/assets/js/app.js"></script>
}