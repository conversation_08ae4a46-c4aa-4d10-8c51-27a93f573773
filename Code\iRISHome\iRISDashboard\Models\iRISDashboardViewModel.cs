﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace iRISDashboard.Models
{
    public class iRISDashboardViewModel
    {
        public int ID { get; set; }
        public string? AppName { get; set; }
        public string? Description { get; set; }
        public string? AppLink { get; set; }
        public string? AppType { get; set; }
        public string? ImgURL { get; set; }
        public string? Project { get; set; }
    }

    public class SettingsViewModel
    {
        public string? SettingName { get; set; }
        public string? Description { get; set; }
        public string? LinkToConfigure { get; set; }
        public string? Category { get; set; }
        public string? SettingIcon { get; set; }
    }

    public class AppData
    {
        public string? AppName { get; set; }
        public string? Description { get; set; }
        public string? AppLink { get; set; }
        public string? AppType { get; set; }
        public string? ImgURL { get; set; }
        public string? Project { get; set; }
    }

}
