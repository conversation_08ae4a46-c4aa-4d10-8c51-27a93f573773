﻿/// <summary>
/// Author :<PERSON><PERSON> <PERSON>
/// Date Created: 09-15-2023
/// Description: View model used to get user details after login.
/// </summary>
using System;
using System.Collections.Generic;
using System.Security;
using System.Text;

namespace AuthLogin.Models
{
    public partial class UserViewModel
    {
        public string? UserGroupID { get; set; }
        public string? UserGroup { get; set; }
        public string? UserName { get; set; }
        public string? EmployeeID { get; set; }
        public SecureString? Password { get; set; }
        public string? ConfirmPassword { get; set; }
        public string? Pwd { get; set; }

        public int Pin { get; set; }

        public string? RFID { get; set; }

        // Tab-2 Inputs
        public string? FirstName { get; set; }
        public string? MiddleName { get; set; }
        public string? LastName { get; set; }
        public string? Title { get; set; }
        public string? Suffix { get; set; }


        // Tab-3 Inputs
        public string? Company { get; set; }
        public string? Department { get; set; }
        public string? Designation { get; set; }

        // Tab-4 Inputs
        public string? Office { get; set; }
        public string? OfficeStreetAddress { get; set; }
        public string? OfficeCity { get; set; }
        public string? OfficeState { get; set; }
        public string? OfficeZip { get; set; }
        public string? OfficeCountry { get; set; }

        // Tab-5 Inputs
        public string? HomeStreetAddress { get; set; }
        public string? HomeCity { get; set; }
        public string? HomeState { get; set; }
        public string? HomeZip { get; set; }
        public string? HomeCountry { get; set; }

        // Tab-7 Inputs
        public string? HomePhoneNumber { get; set; }
        public string? OfficePhoneNumber { get; set; }
        public string? MobilePhoneNumber { get; set; }
        public string? FaxNumber { get; set; }
        public string? PagerNumber { get; set; }
        public string? Email { get; set; }

        public int UserID { get; set; }
        public string? Name { get; set; }

        public string? DateAdded { get; set; }
        public string? Status { get; set; }
        public string? Action { get; set; }

        public string? StateID { get; set; }
        public string? StateName { get; set; }

        public string? CountryID { get; set; }
        public string? CountryName { get; set; }

        //Used while adding/updating new user
        public string? ComputerName { get; set; }
        public string? Domain { get; set; }

        public int TwoFactorAuthStatus {get;set;}
    }
}
