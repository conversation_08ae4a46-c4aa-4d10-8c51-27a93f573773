﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Security;

/// <summary>
/// Author :<PERSON><PERSON>
/// Date Created: 09-15-2023
/// Description:View model for user login. 
/// </summary>

namespace AuthLogin.Models
{
    public class LoginViewModel
    {
        public string? UserName { get; set; } = string.Empty;
        public string? Password { get; set; }
        public string? LoginStatus { get; set; } = string.Empty;
        public string? CaptchaCode { get; set; }
        public List<LoginFeaturesViewModel> Features { get; set; } = new List<LoginFeaturesViewModel>();
    }
    public class BaseUrl
    {
        public string? URL { get; set; }
    }
    public class ClientIPDetails
    {
        public string? IpAddress { get; set; }
        public DateTime dt { get; set; }
    }
    public class LoginPageDataViewModel
    {
        public DataTable CaptchaSetting = new DataTable();
    }
}
