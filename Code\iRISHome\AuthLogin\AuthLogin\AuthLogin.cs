﻿using AuthLogin.DbFunctions;
using AuthLogin.Models;
using JSONLogger;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authentication.OpenIdConnect;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;
using System.Data;
using System.IdentityModel.Tokens.Jwt;
using System.Net.Http.Headers;
using System.Reflection;
using System.Security.Claims;

namespace AuthLogin.AuthLogin
{
    public class AuthLogin : Controller
    {
        public static string ProductName = string.Empty;
        public static string BrandName = string.Empty;
        public static string UserName = string.Empty;
        public static string Name = string.Empty;
        public static string LoginStatus = string.Empty;
        public static string ADLoginEnabled = string.Empty;
        public static string ADBaseUri = string.Empty;
        public static string ADRequestUri = string.Empty;
        public static bool IsADLogin = false;
        GlobalVariable globalVariable = new GlobalVariable();

        public IActionResult Index()
        {
            try
            {
                ViewBag.ApplicationVersion = GetApplicationVersion();
            }
            catch (Exception ex)
            {
                JsonLog.Log(ex.Message);
            }
            return View();
        }

        public string GetApplicationVersion()
        {
            var assemblies = AppDomain.CurrentDomain.GetAssemblies();
            string? version = null;
            foreach (var assembly in assemblies)
            {
                if (assembly.GetName().Name == "iRISHome")
                {
                    version = assembly.GetName().Version?.ToString();
                    break;
                }
            }
            return version ?? string.Empty;
        }

        public void GetADSettings()
        {
            try
            {
                var ADSettingValue = GetConfigurations();
                ADLoginEnabled = ADSettingValue.GetSection("ADSettings").GetSection("LOGIN_TYPE").Value;
                globalVariable.ADBaseUri = ADSettingValue.GetSection("ADSettings").GetSection("AD_BASE_URI").Value;
                globalVariable.ADRequestUri = ADSettingValue.GetSection("ADSettings").GetSection("AD_REQUEST_URI").Value;
            }
            catch (Exception ex)
            {
                JsonLog.Log(ex.Message);
            }
        }

        public IConfiguration GetConfigurations()
        {
            var builder = new ConfigurationBuilder().SetBasePath(Directory.GetCurrentDirectory()).AddJsonFile("appsettings.json", optional: true, reloadOnChange: true);
            return builder.Build();
        }

        [ValidateAntiForgeryToken]
        public IActionResult AuthenticateLogin(LoginViewModel model)
        {
            bool isMAUserLogin = false;
            try
            {
                string status = string.Empty;
                ViewData["LoginStatus"] = "";
                ViewData["Name"] = "";
                ADViewModel AdSettings = new ADViewModel();
                GetADSettings();
                DBObjects dbo = new DBObjects();
                if (HttpContext.Session.GetString("CaptchaValue") != null)
                {
                    string? captchavalue = HttpContext.Session.GetString("CaptchaValue");
                    if (!string.IsNullOrEmpty(captchavalue) && captchavalue.Replace(" ", "") != (model.CaptchaCode ?? string.Empty))
                    {
                        TempData["CaptchaError"] = "Invlaid captcha code.";
                        return RedirectToAction("Index");
                    }
                }

                if (isMAUserLogin)
                {
                    status = dbo.LoginValidation(model);
                }
                else if (ADLoginEnabled == "0")
                {
                    status = dbo.LoginValidation(model);
                }
                else if (ADLoginEnabled == "1")
                {
                    status = ValidationActiveDirectoryUserLogin(model.UserName ?? string.Empty, model.Password ?? string.Empty);
                }
                else if (ADLoginEnabled == "2")
                {
                    status = ValidationActiveDirectoryUserLogin(model.UserName ?? string.Empty, model.Password ?? string.Empty);
                    if (status != "Login Success")
                    {
                        IsADLogin = false;
                        status = dbo.LoginValidation(model);
                    }
                }
                UserViewModel userviewmodel = dbo.GetUserDetail(model.UserName ?? string.Empty) ?? new UserViewModel();

                if (status == "Login Success")
                {
                    if (userviewmodel != null && (userviewmodel.Status ?? string.Empty) == "1")
                    {
                        HttpContext.Session.SetString("Name", userviewmodel.Name ?? string.Empty);
                        HttpContext.Session.SetString("UserGroupID", userviewmodel.UserGroupID ?? string.Empty);
                        HttpContext.Session.SetString("Username", userviewmodel.UserName ?? string.Empty);
                        HttpContext.Session.SetString("enteredusername", userviewmodel.UserName ?? string.Empty);
                        HttpContext.Session.SetInt32("UserID", userviewmodel.UserID);
                        HttpContext.Session.SetString("isfromirisweb", "true");
                        HttpContext.Session.SetString("SessionID", DateTime.Now.ToString("yyyyMMddHHmmssff"));
                        HttpContext.Session.SetInt32("InvalidAttempts", 0);
                        string sessionid = HttpContext.Session.GetString("SessionID") ?? string.Empty;
                        var returnUrl = HttpContext.Session.GetString("ReturnUrl");
                        var baseurl = HttpContext.Session.GetString("BaseURL");
                        JsonLog.Log($"{userviewmodel.UserName ?? string.Empty} Logged in successfully. ", logType: "Info", username: userviewmodel.UserName ?? string.Empty);
                        if ((HttpContext.Session.GetString("isTwoFactorEnabled") ?? string.Empty) == "True")
                        {
                            HttpContext.Session.SetString("After2FactorSuccessMethod", "Index");
                            HttpContext.Session.SetString("After2FactorSuccessController", "iRISDashboard");
                            if (userviewmodel.TwoFactorAuthStatus == 1)
                            {
                                return RedirectToAction("LoginWithTwoFactor", "TwoFactorAuthentication");
                            }
                            else
                            {
                                return RedirectToAction("EnableAuthenticator", "TwoFactorAuthentication", new
                                {
                                    authenticatorIssuer = "MobileAspects - iRISHome",
                                    username = userviewmodel.Name ?? string.Empty
                                });
                            }
                        }
                        else
                        {
                            return RedirectToAction("Index", "iRISDashboard");
                        }
                    }
                    else
                    {
                        status = "Unable to login please contact admin";
                    }
                }

                TempData["LoginStatus"] = status;
                model.LoginStatus = status;
                JsonLog.Log($"Login failed for user {model.UserName ?? string.Empty} .", logType: "Info");
            }
            catch (Exception ex)
            {
                JsonLog.Log(ex.Message);
            }

            return RedirectToAction("Index", "AuthLogin");
        }

        public IActionResult Logout()
        {
            HttpContext.Session.Clear();
            return SignOut(new AuthenticationProperties
            {
                RedirectUri = Url.Action("KeycloakValidation", "AuthLogin")
            }, CookieAuthenticationDefaults.AuthenticationScheme, OpenIdConnectDefaults.AuthenticationScheme);
        }

        public string ValidationActiveDirectoryUserLogin(string username, string password)
        {
            string validationResult = string.Empty;
            string apiUri = globalVariable.ADRequestUri ?? string.Empty;

            try
            {
                using var client = new HttpClient();
                client.DefaultRequestHeaders.Add("Username", username);
                client.DefaultRequestHeaders.Add("Password", password);
                client.BaseAddress = new Uri(globalVariable.ADBaseUri ?? string.Empty);
                client.DefaultRequestHeaders.Accept.Clear();
                client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
                HttpResponseMessage response = client.PostAsync(apiUri, null).Result;
                
                if (response.IsSuccessStatusCode)
                {
                    JsonLog.Log($"AD Response {(int)response.StatusCode} {response.IsSuccessStatusCode}");
                    var stringdata = response.Content.ReadAsStringAsync().Result.ToString();
                    if (string.IsNullOrEmpty(stringdata))
                    {
                        validationResult = "Internal server Error. Please try again later.";
                        JsonLog.Log($"AD Response {(int)response.StatusCode} {response.IsSuccessStatusCode}");
                    }
                    else
                    {
                        var data = JsonConvert.DeserializeObject<User>(stringdata);
                        if (data?.message == "Invalid Credentials")
                        {
                            validationResult = data.message;
                        }
                        else if (data?.message == "User Autehntication Successful" && data != null)
                        {
                            JsonLog.Log($"AD Response {(int)response.StatusCode} {response.IsSuccessStatusCode}");
                            data.UserName = username;
                            if (ADUserLoginUpdate(data))
                                validationResult = string.Empty;
                            validationResult = "Login Success";
                            globalVariable.IsADLogin = true;
                        }
                    }
                }
                else
                {
                    JsonLog.Log($"AD Response {(int)response.StatusCode} {response.IsSuccessStatusCode}");
                    validationResult = "Internal server Error. Please try again later.";
                }
            }
            catch (Exception ex)
            {
                JsonLog.Log(ex.Message);
                JsonLog.Log("AD Method " + ex.Message);
                validationResult = "Internal server Error. Please try again later.";
            }
            return validationResult;
        }

        private bool ADUserLoginUpdate(User user)
        {
            if (user == null) return false;

            bool flag = false;
            try
            {
                DBObjects dbo = new DBObjects();
                UserViewModel adUser = new UserViewModel();
                adUser.UserName = user.UserName;
                adUser.RFID = user.RFID ?? string.Empty;
                adUser.FirstName = user.FirstName ?? string.Empty;
                adUser.MiddleName = user.MiddleName ?? string.Empty;
                adUser.LastName = user.LastName ?? string.Empty;
                adUser.Email = user.Email ?? string.Empty;
                adUser.UserGroupID = "3";
                adUser.Domain = "iRISWeb - AD Login";
                adUser.ComputerName = Environment.MachineName;
                adUser.Designation = user.Designation ?? string.Empty;
                string? status = dbo.AddADUserToDB(adUser);
                if (status != null && (status.Equals("User registered") || status.Equals("User updated")))
                    flag = true;
            }
            catch (Exception ex)
            {
                JsonLog.Log(ex.Message);
            }
            return flag;
        }

        //[Authorize]
        public IActionResult KeycloakValidation()
        {
            try
            {
                JsonLog.Log("KeycloakValidation controller hit");
                if (User.Identity.IsAuthenticated)
                {
                    JsonLog.Log("User IsAuthenticated");
                    var userId = User.FindFirst(ClaimTypes.NameIdentifier)?.Value;
                    var userName = User.FindFirst(ClaimTypes.GivenName)?.Value + " " + User.FindFirst(ClaimTypes.Surname)?.Value;
                    HttpContext.Session.SetString("DisplayName", userName);
                    HttpContext.Session.SetString("IsAdminStatus", "True");
                    return RedirectToAction("Index", "iRISDashboard");
                }
                return RedirectToAction("Index", "AuthLogin");
            }
            catch (Exception ex)
            {
                JsonLog.Log(ex.Message);
                return RedirectToAction("Index", "AuthLogin");
            }
        }
        

        public static DataTable CreateDataTableFromJson(string json)
        {
            if (string.IsNullOrEmpty(json))
                throw new ArgumentNullException(nameof(json));

            var handler = new JwtSecurityTokenHandler();
            var token = handler.ReadJwtToken(json);

            if (!token.Payload.TryGetValue("user_attributes", out object attrObj) || attrObj == null)
                throw new Exception("user_attributes not found in JWT token");

            string attrJson = attrObj.ToString() ?? string.Empty;
            List<AppData>? appList = JsonConvert.DeserializeObject<List<AppData>>(attrJson);
            if (appList == null)
                appList = new List<AppData>();

            DataTable dataTable = new DataTable();
            dataTable.Columns.Add("AppName", typeof(string));
            dataTable.Columns.Add("Description", typeof(string));
            dataTable.Columns.Add("AppLink", typeof(string));
            dataTable.Columns.Add("AppType", typeof(string));
            dataTable.Columns.Add("ImgURL", typeof(string));
            dataTable.Columns.Add("Project", typeof(string));

            foreach (AppData app in appList)
            {
                if (app != null)
                {
                    dataTable.Rows.Add(
                        app.AppName ?? string.Empty,
                        app.Description ?? string.Empty,
                        app.AppLink ?? string.Empty,
                        app.AppType ?? string.Empty,
                        app.ImgURL ?? string.Empty,
                        app.Project ?? string.Empty
                    );
                }
            }
            return dataTable;
        }

        public class AppData
        {
            public string AppName { get; set; } = string.Empty;
            public string Description { get; set; } = string.Empty;
            public string AppLink { get; set; } = string.Empty;
            public string AppType { get; set; } = string.Empty;
            public string ImgURL { get; set; } = string.Empty;
            public string Project { get; set; } = string.Empty;
        }
    }
}
