﻿<!doctype html>
<html lang="en">

<head>

    <meta charset="utf-8" />
    <title>@ViewBag.Title | @ViewData["BrandName"]</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta content="Premium Multipurpose Admin & Dashboard Template" name="description" />
    <meta content="Themesbrand" name="author" />
    <!-- App favicon -->
    <link rel="shortcut icon" href="~/assets/images/maicon.png">

    @RenderSection("styles", required: false)

    <!-- Bootstrap Css -->
    <link href="~/assets/css/bootstrap.min.css" rel="stylesheet" />
    <!-- Icons Css -->
    <link href="~/assets/css/icons.min.css" rel="stylesheet" />
    <!-- App Css-->
    <link href="~/assets/css/app.min.css" rel="stylesheet" />
    
</head>

<body>
    @RenderBody()
    <script src="~/assets/libs/jquery/jquery.min.js"></script>
    <partial name="_vendor_scripts" />
    
    @RenderSection("scripts", required: false)
    
</body>
</html>
