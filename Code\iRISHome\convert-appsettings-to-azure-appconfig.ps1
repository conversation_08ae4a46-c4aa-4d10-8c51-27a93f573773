# PowerShell script to convert appsettings.json to Azure App Configuration
# This script reads appsettings.json and uploads the configuration to Azure App Configuration

param(
    [Parameter(Mandatory=$false)]
    [string]$AppSettingsPath = "iRISHome/appsettings.json",
    
    [Parameter(Mandatory=$false)]
    [string]$ConnectionString = "",
    
    [Parameter(Mandatory=$false)]
    [string]$Endpoint = "",
    
    [Parameter(Mandatory=$false)]
    [switch]$DryRun = $false,
    
    [Parameter(Mandatory=$false)]
    [switch]$IncludeSecrets = $false,
    
    [Parameter(Mandatory=$false)]
    [string]$Label = $null
)

Write-Host "=== Azure App Configuration Converter ===" -ForegroundColor Green

# Check if Azure CLI is installed
try {
    $azVersion = az version --output json 2>$null | ConvertFrom-Json
    Write-Host "✅ Azure CLI found: $($azVersion.'azure-cli')" -ForegroundColor Green
} catch {
    Write-Host "❌ Azure CLI not found. Please install Azure CLI first." -ForegroundColor Red
    Write-Host "Download from: https://docs.microsoft.com/en-us/cli/azure/install-azure-cli" -ForegroundColor Yellow
    exit 1
}

# Check if appsettings.json exists
if (-not (Test-Path $AppSettingsPath)) {
    Write-Host "❌ File not found: $AppSettingsPath" -ForegroundColor Red
    exit 1
}

Write-Host "✅ Found appsettings file: $AppSettingsPath" -ForegroundColor Green

# Read and parse appsettings.json
try {
    $appSettings = Get-Content $AppSettingsPath -Raw | ConvertFrom-Json
    Write-Host "✅ Successfully parsed appsettings.json" -ForegroundColor Green
} catch {
    Write-Host "❌ Failed to parse appsettings.json: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Get connection details
if ([string]::IsNullOrEmpty($ConnectionString) -and [string]::IsNullOrEmpty($Endpoint)) {
    if ($appSettings.AppConfig.ConnectionString) {
        $ConnectionString = $appSettings.AppConfig.ConnectionString
        Write-Host "✅ Using connection string from appsettings.json" -ForegroundColor Green
    } elseif ($appSettings.AppConfig.Endpoint) {
        $Endpoint = $appSettings.AppConfig.Endpoint
        Write-Host "✅ Using endpoint from appsettings.json" -ForegroundColor Green
    } else {
        Write-Host "❌ No Azure App Configuration connection details found." -ForegroundColor Red
        Write-Host "Please provide either -ConnectionString or -Endpoint parameter" -ForegroundColor Yellow
        exit 1
    }
}

# Function to flatten JSON object into key-value pairs
function ConvertTo-FlatKeyValue {
    param(
        [Parameter(Mandatory=$true)]
        $Object,
        
        [Parameter(Mandatory=$false)]
        [string]$Prefix = ""
    )
    
    $result = @{}
    
    foreach ($property in $Object.PSObject.Properties) {
        $key = if ($Prefix) { "$Prefix`:$($property.Name)" } else { $property.Name }
        
        if ($property.Value -is [PSCustomObject]) {
            # Recursive call for nested objects
            $nestedResult = ConvertTo-FlatKeyValue -Object $property.Value -Prefix $key
            foreach ($nestedKey in $nestedResult.Keys) {
                $result[$nestedKey] = $nestedResult[$nestedKey]
            }
        } elseif ($property.Value -is [Array]) {
            # Handle arrays
            for ($i = 0; $i -lt $property.Value.Length; $i++) {
                $arrayKey = "$key`:$i"
                if ($property.Value[$i] -is [PSCustomObject]) {
                    $nestedResult = ConvertTo-FlatKeyValue -Object $property.Value[$i] -Prefix $arrayKey
                    foreach ($nestedKey in $nestedResult.Keys) {
                        $result[$nestedKey] = $nestedResult[$nestedKey]
                    }
                } else {
                    $result[$arrayKey] = $property.Value[$i]
                }
            }
        } else {
            # Simple value
            $result[$key] = $property.Value
        }
    }
    
    return $result
}

# Convert appsettings to flat key-value pairs
$flatConfig = ConvertTo-FlatKeyValue -Object $appSettings

# Filter out sensitive keys unless explicitly included
$sensitivePatterns = @(
    "*Secret*",
    "*Password*", 
    "*Key*",
    "*Token*",
    "*ConnectionString*"
)

$configToUpload = @{}
$skippedSecrets = @()

foreach ($key in $flatConfig.Keys) {
    $isSensitive = $false
    foreach ($pattern in $sensitivePatterns) {
        if ($key -like $pattern) {
            $isSensitive = $true
            break
        }
    }
    
    if ($isSensitive -and -not $IncludeSecrets) {
        $skippedSecrets += $key
    } else {
        $configToUpload[$key] = $flatConfig[$key]
    }
}

Write-Host "`n=== Configuration Summary ===" -ForegroundColor Cyan
Write-Host "Total keys found: $($flatConfig.Count)" -ForegroundColor White
Write-Host "Keys to upload: $($configToUpload.Count)" -ForegroundColor Green
Write-Host "Sensitive keys skipped: $($skippedSecrets.Count)" -ForegroundColor Yellow

if ($skippedSecrets.Count -gt 0) {
    Write-Host "`nSkipped sensitive keys:" -ForegroundColor Yellow
    foreach ($key in $skippedSecrets) {
        Write-Host "  - $key" -ForegroundColor Gray
    }
    Write-Host "Use -IncludeSecrets to upload these as well" -ForegroundColor Yellow
}

Write-Host "`nKeys to upload:" -ForegroundColor Green
foreach ($key in $configToUpload.Keys) {
    $value = $configToUpload[$key]
    $displayValue = if ($value.ToString().Length -gt 50) { 
        $value.ToString().Substring(0, 47) + "..." 
    } else { 
        $value.ToString() 
    }
    Write-Host "  $key = $displayValue" -ForegroundColor Gray
}

if ($DryRun) {
    Write-Host "`n=== DRY RUN MODE - No changes will be made ===" -ForegroundColor Yellow
    exit 0
}

# Confirm before proceeding
Write-Host "`n=== Ready to Upload ===" -ForegroundColor Cyan
$confirm = Read-Host "Do you want to proceed with uploading to Azure App Configuration? (y/N)"
if ($confirm -ne "y" -and $confirm -ne "Y") {
    Write-Host "Operation cancelled." -ForegroundColor Yellow
    exit 0
}

# Upload to Azure App Configuration
Write-Host "`nUploading configuration to Azure App Configuration..." -ForegroundColor Green

$successCount = 0
$errorCount = 0

foreach ($key in $configToUpload.Keys) {
    $value = $configToUpload[$key]
    
    try {
        # Build the az appconfig kv set command
        $azCommand = "az appconfig kv set"
        
        if (-not [string]::IsNullOrEmpty($ConnectionString)) {
            $azCommand += " --connection-string `"$ConnectionString`""
        } else {
            $azCommand += " --endpoint `"$Endpoint`""
        }
        
        $azCommand += " --key `"$key`" --value `"$value`""
        
        if (-not [string]::IsNullOrEmpty($Label)) {
            $azCommand += " --label `"$Label`""
        }
        
        $azCommand += " --yes"
        
        # Execute the command
        $result = Invoke-Expression $azCommand 2>&1
        
        if ($LASTEXITCODE -eq 0) {
            Write-Host "✅ $key" -ForegroundColor Green
            $successCount++
        } else {
            Write-Host "❌ $key - Error: $result" -ForegroundColor Red
            $errorCount++
        }
    } catch {
        Write-Host "❌ $key - Exception: $($_.Exception.Message)" -ForegroundColor Red
        $errorCount++
    }
}

Write-Host "`n=== Upload Complete ===" -ForegroundColor Green
Write-Host "Successfully uploaded: $successCount keys" -ForegroundColor Green
Write-Host "Failed uploads: $errorCount keys" -ForegroundColor Red

if ($errorCount -eq 0) {
    Write-Host "`n🎉 All configuration successfully uploaded to Azure App Configuration!" -ForegroundColor Green
} else {
    Write-Host "`n⚠️  Some uploads failed. Please check the errors above." -ForegroundColor Yellow
}

Write-Host "`nNext steps:" -ForegroundColor Cyan
Write-Host "1. Verify the configuration in Azure Portal" -ForegroundColor White
Write-Host "2. Test your application to ensure it reads from Azure App Configuration" -ForegroundColor White
Write-Host "3. Consider removing sensitive values from local appsettings.json" -ForegroundColor White
