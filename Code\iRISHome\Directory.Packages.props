<Project>
  <PropertyGroup>
    <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
    <NoWarn>$(NoWarn);NU1507</NoWarn>
    <RestorePackagesWithLockFile>true</RestorePackagesWithLockFile>
    <DisableImplicitNuGetFallbackFolder>true</DisableImplicitNuGetFallbackFolder>
  </PropertyGroup>
  <ItemGroup>
    <!-- ASP.NET Core & Extensions -->
    <PackageVersion Include="Microsoft.AspNetCore.Authentication.OpenIdConnect" Version="9.0.6" />
    <PackageVersion Include="Microsoft.Azure.AppConfiguration.AspNetCore" Version="7.1.0" />
    <PackageVersion Include="Microsoft.FeatureManagement" Version="4.1.0" />
    <PackageVersion Include="Microsoft.Identity.Client" Version="4.60.4" />
    <PackageVersion Include="Microsoft.Identity.Web.MicrosoftGraph" Version="2.17.1" />
    <PackageVersion Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.21.2" />
    <!-- Aspire -->
    <PackageVersion Include="Aspire.Hosting.AppHost" Version="9.3.1" />
    <PackageVersion Include="Aspire.Hosting.Sdk" Version="9.0.0-preview.3.24210.17" />
    <PackageVersion Include="Aspire.Hosting" Version="9.3.1" />
    <PackageVersion Include="Microsoft.Extensions.ServiceDiscovery" Version="8.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Http.Resilience" Version="8.0.0" />
    <PackageVersion Include="Microsoft.Extensions.Telemetry" Version="9.6.0" />
    <PackageVersion Include="OpenTelemetry.Extensions.Hosting" Version="1.7.0" />
    <PackageVersion Include="OpenTelemetry.Instrumentation.AspNetCore" Version="1.8.1" />
    <PackageVersion Include="OpenTelemetry.Instrumentation.Http" Version="1.8.1" />
    <!-- Data Access -->
    <PackageVersion Include="Microsoft.Data.Sqlite.Core" Version="9.0.6" />
    <PackageVersion Include="Microsoft.Data.SqlClient" Version="5.2.0" />
    <PackageVersion Include="SQLitePCLRaw.bundle_e_sqlite3" Version="2.1.11" />
    <PackageVersion Include="SQLitePCLRaw.lib.e_sqlite3" Version="2.1.11" />
    <PackageVersion Include="SQLitePCLRaw.lib.e_sqlite3.v110" Version="1.1.14" />
    <PackageVersion Include="System.Data.SqlClient" Version="4.9.0" />
    <!-- Authentication -->
    <PackageVersion Include="GoogleAuthenticator" Version="3.3.0-beta1" />
    <PackageVersion Include="Telerik.Licensing" Version="1.6.7" />
    <!-- UI Components -->
    <PackageVersion Include="Telerik.UI.for.AspNet.Core" Version="2025.2.520" />
    <PackageVersion Include="Telerik.FontIcons" Version="4.4.0" />
    <PackageVersion Include="Telerik.SvgIcons" Version="4.4.0" />
    <!-- Utilities -->
    <PackageVersion Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageVersion Include="Serilog.Enrichers.Process" Version="2.0.2" />
    <PackageVersion Include="Serilog.Enrichers.Thread" Version="3.1.0" />
    <PackageVersion Include="SerilogTimings" Version="3.0.1" />
    <PackageVersion Include="System.Configuration.ConfigurationManager" Version="8.0.0" />
    <PackageVersion Include="System.IdentityModel.Tokens.Jwt" Version="8.0.1" />
  </ItemGroup>
</Project>