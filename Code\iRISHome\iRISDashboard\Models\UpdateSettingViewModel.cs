﻿using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace iRISDashboard.Models
{
        public class UpdateAppSettings
        {
            public int? ID { get; set; }
            public string? AppName { get; set; }
            public string AppType { get; set; }
            public string ImgURL { get; set; }
            public string Project { get; set; }
            public string AppLink { get; set; }
            public string Description { get; set; }
        }
}
