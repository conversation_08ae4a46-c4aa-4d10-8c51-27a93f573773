using Microsoft.AspNetCore.Mvc;
using iRISHome.Services;

namespace iRISHome.Controllers
{
    [Route("api/[controller]")]
    [ApiController] 
    public class ThemeController : ControllerBase
    {
        private readonly ThemeService _themeService;

        public ThemeController(ThemeService themeService)
        {
            _themeService = themeService;
        }

        [HttpPost("set")]
        public IActionResult SetTheme([FromForm] string theme)
        {
            if (string.IsNullOrEmpty(theme))
            {
                return BadRequest("Theme value is required");
            }

            try
            {
                _themeService.SetTheme(theme);
                return Ok();
            }
            catch (Exception)
            {
                return StatusCode(500, "Failed to save theme preference");
            }
        }

        [HttpGet("current")]
        public IActionResult GetCurrentTheme()
        {
            var theme = _themeService.GetCurrentTheme();
            return Ok(new { theme });
        }
    }
}