﻿using iRISHome.Models;
using JSONLogger;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using System.Data;

namespace iRISHome.DbFunctions
{
    public class DBObjects
    {
        private readonly IConfiguration _configuration;
        private SqlConnection conn;

        public DBObjects(IConfiguration configuration)
        {
            _configuration = configuration;
            conn = CreateConnection();
        }

        private SqlConnection CreateConnection()
        {
            string? connectionString = _configuration.GetConnectionString("Default");

            if (string.IsNullOrEmpty(connectionString))
            {
                throw new InvalidOperationException("Connection string is not configured. Please check the ConnectionStrings:Default setting in appsettings.json");
            }

            connectionString = AdvancedEncryptionStandard.MACrypto.Decrypt(connectionString);
            return new SqlConnection(connectionString);
        }

        public List<MenuItemsViewModel> GetMenuItems(string UserGroupID, int UserID)
        {
            var menus = new List<MenuItemsViewModel>();
            try
            {
                if (conn == null) conn = CreateConnection();
                if (conn.State == ConnectionState.Closed) conn.Open();
                using (var cmd = new SqlCommand("uspGetMenuItems", conn))
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@UserGroupID", int.Parse(UserGroupID));
                    cmd.Parameters.AddWithValue("@UserID", UserID);
                    using (var dr = cmd.ExecuteReader())
                    {
                        while (dr.Read())
                        {
                            var menuItem = new MenuItemsViewModel();
                            menuItem.ModuleID = dr["MODULE_ID"] != DBNull.Value ? Convert.ToInt32(dr["MODULE_ID"]) : 0;
                            menuItem.ModuleName = dr["MODULE_NAME"]?.ToString() ?? string.Empty;
                            menuItem.Description = dr["DESCRIPTION"]?.ToString() ?? string.Empty;
                            menuItem.Link = dr["LINK"]?.ToString() ?? string.Empty;
                            menuItem.ParentModuleID = dr["PARENT_MODULE_ID"] != DBNull.Value ? Convert.ToInt32(dr["PARENT_MODULE_ID"]) : 0;
                            menuItem.ModuleIcon = dr["MODULE_ICON"]?.ToString() ?? string.Empty;
                            menuItem.FavouriteStatus = dr["bookmarkstatus"]?.ToString() ?? string.Empty;
                            menuItem.BookmarkIcon = menuItem.FavouriteStatus == "1" ? "bx bxs-star" : "bx bx-star";
                            menuItem.BookmarkTitle = menuItem.FavouriteStatus == "1" ? "Click to remove from favorites" : "Click to add to favorites";
                            menuItem.BookmarkDataId = dr["MODULE_NAME"]?.ToString()?.Replace(" ", "") ?? string.Empty;
                            menus.Add(menuItem);
                        }
                    }
                }
                return menus;
            }
            catch (Exception ex)
            {
                JsonLog.Log(ex.Message);
                return menus;
            }
            finally
            {
                if (conn != null && conn.State == ConnectionState.Open) conn.Close();
            }
        }

        public NotificationViewModel GetNotificationData(int userid)
        {
            var Notifications = new NotificationViewModel();
            try
            {
                if (conn == null) conn = CreateConnection();
                using (var cmd = new SqlCommand("usp007GetNotifications", conn))
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("UserID", userid);
                    if (conn.State == ConnectionState.Closed) conn.Open();
                    using (var dr = cmd.ExecuteReader())
                    {
                        var Bookmark = new List<Bookmarks>();
                        var History = new List<RecentUserHistory>();
                        while (dr.HasRows)
                        {
                            var AlertType = dr.GetName(0);
                            if (AlertType == "AlertCount")
                            {
                                var Notification = new List<Notification>();
                                while (dr.Read())
                                {
                                    var Noti = new Notification();
                                    Noti.NotificationLabel = dr["Label"]?.ToString() ?? string.Empty;
                                    Noti.AlertCount = dr["AlertCount"]?.ToString() ?? string.Empty;
                                    Noti.NotificationDetails = dr["Notification Details"]?.ToString() ?? string.Empty;
                                    Noti.AlertUrl = dr["AlertURL"]?.ToString() ?? string.Empty;
                                    Notification.Add(Noti);
                                }
                                Notifications.Notifications.AddRange(Notification);
                            }
                            if (AlertType == "Bookmark")
                            {
                                while (dr.Read())
                                {
                                    var bookmark = new Bookmarks();
                                    bookmark.Title = dr["Bookmark"]?.ToString() ?? string.Empty;
                                    bookmark.BookmarkUrl = dr["BookmarkURL"]?.ToString() ?? string.Empty;
                                    bookmark.BookmarkType = dr["BookmarkType"]?.ToString() ?? string.Empty;
                                    bookmark.BookmarkDataID = bookmark.Title.Replace(" ", "");
                                    Bookmark.Add(bookmark);
                                }
                                Notifications.Bookmarks.AddRange(Bookmark);
                            }
                            if (AlertType == "History")
                            {
                                while (dr.Read())
                                {
                                    var history = new RecentUserHistory();
                                    history.ModuleName = dr["History"]?.ToString() ?? string.Empty;
                                    history.ModuleURL = dr["HistoryURL"]?.ToString() ?? string.Empty;
                                    History.Add(history);
                                }
                                Notifications.UserHistory.AddRange(History);
                            }
                            dr.NextResult();
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                JsonLog.Log(ex.ToString());
            }
            finally
            {
                if (conn != null && conn.State == ConnectionState.Open) conn.Close();
            }
            return Notifications;
        }

        public void GetStartupSettings()
        {
            try
            {
                StartupSettings.LogType = "json";
                StartupSettings.FileSize = 1.0;
            }
            catch(Exception ex)
            {
                JsonLog.Log(ex.Message);
            }
        }
    }
}
