﻿<Project Sdk="Microsoft.NET.Sdk.Razor">

  <PropertyGroup>
    <AssemblyVersion>1.0.0.0</AssemblyVersion>
    <FileVersion>1.0.0.0</FileVersion>
  </PropertyGroup>
  
  <ItemGroup>
    <FrameworkReference Include="Microsoft.AspNetCore.App" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.Data.Sqlite.Core" />
    <PackageReference Include="Microsoft.Data.SqlClient" />
    <PackageReference Include="Microsoft.Identity.Client" />
    <PackageReference Include="Newtonsoft.Json" />
    <PackageReference Include="Serilog.Enrichers.Process" />
    <PackageReference Include="Serilog.Enrichers.Thread" />
    <PackageReference Include="SerilogTimings" />
    <PackageReference Include="System.Configuration.ConfigurationManager" />
    <PackageReference Include="System.IdentityModel.Tokens.Jwt" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="JSONLogger">
      <HintPath>..\iRISHome\Projects\JSONLogger.dll</HintPath>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <Content Update="Views\iRISDashboard\UpdateAppsettingsInfo.cshtml">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
  </ItemGroup>

</Project>
