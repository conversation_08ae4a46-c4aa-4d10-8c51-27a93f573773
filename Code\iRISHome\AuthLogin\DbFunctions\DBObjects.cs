﻿using AuthLogin.Models;
using JSONLogger;
using Microsoft.Data.Sqlite;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace AuthLogin.DbFunctions
{
    public class DBObjects
    {
        private SqlConnection conn;
        public DBObjects()
        {
            conn = GetConnectionStringFromJsonFile();
        }
        public SqlConnection GetConnectionStringFromJsonFile()
        {
            var configuration = GetConfigurations();
            string? connectionString = configuration.GetSection("ConnectionStrings").GetSection("Default").Value;
            connectionString = AdvancedEncryptionStandard.MACrypto.Decrypt(connectionString);
            conn = new SqlConnection(connectionString);
            return conn;
        }
        public IConfiguration GetConfigurations()
        {
            var builder = new ConfigurationBuilder().SetBasePath(Directory.GetCurrentDirectory()).AddJsonFile("appsettings.json", optional: true, reloadOnChange: true);
            return builder.Build();
        }

        public string connectionString = "Data Source=./wwwroot/assets/Dashboard/assets/Sqlite/iRISDashboard.db;";

        public string LoadQueryFromFile(string queryFile)
        {
            return System.IO.File.ReadAllText(queryFile);
        }

        #region DB Connection Check
        public void checkDbConnection()
        {
            try
            {
                if (conn.State == ConnectionState.Closed)
                {
                    conn.Open();
                    JsonLog.Log("Connected to database successfully", logType: "Info");
                }
            }
            catch (Exception ex)
            {
                JsonLog.Log("Database connection issue:" + ex.Message);
            }
            finally
            {
                if (conn.State == ConnectionState.Open) conn.Close();
            }
        }
        #endregion
        #region ModulesList
        public StartUpSetting GetStartUpSetting()
        {
            StartUpSetting Settings = new StartUpSetting();
            try
            {
                using (var cmd = new SqlCommand("usp000GetStartupSettings", conn))
                {
                    if (conn.State == ConnectionState.Closed) conn.Open();
                    cmd.CommandType = CommandType.StoredProcedure;
                    using (var dr = cmd.ExecuteReader())
                    {
                        do
                        {
                            while (dr.Read())
                            {
                                if ((dr.GetName(0) ?? string.Empty) == "ProductName")
                                {
                                    ProductDetails AppName = new ProductDetails();
                                    AppName.Name = dr["ProductName"].ToString();
                                    Settings.ProductDetails = AppName;
                                }
                                else if ((dr.GetName(0) ?? string.Empty) == "Setting Name")
                                {
                                    SettingViewModel Setting = new SettingViewModel();
                                    Setting.SettingName = dr["Setting Name"].ToString();
                                    Setting.SettingValue = dr["Setting Value"].ToString();
                                    Settings.Settings.Add(Setting);
                                }
                                else if ((dr.GetName(0) ?? string.Empty) == "SessionTimeout")
                                {
                                    Settings.SessionTimeout = dr["SessionTimeout"].ToString();
                                }
                            }
                        }
                        while (dr.NextResult());
                    }
                }
            }
            catch (Exception ex)
            {
                JsonLog.Log(ex.Message);
            }
            return Settings;
        }
        #endregion
        #region Login
        public string LoginValidation(LoginViewModel model)
        {
            object status = "";
            try
            {
                using (var cmd = new SqlCommand("uspLoginValidation", conn))
                {
                    string? password = PasswordUtil.PasswordUtil.Encrypt((model.Password ?? string.Empty).ToString());
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@UserName", model.UserName);
                    cmd.Parameters.AddWithValue("@Password", password);
                    if (conn.State == ConnectionState.Closed) conn.Open();
                    status = cmd.ExecuteScalar();
                    return status.ToString();
                }
            }
            catch (Exception ex)
            {
                JsonLog.Log(ex.Message);
            }
            finally
            {
                if (conn.State == ConnectionState.Open) conn.Close();
            }
            return status.ToString();
        }
        public UserViewModel GetUserDetail(string Username)
        {
            UserViewModel user = new UserViewModel();
            try
            {
                using (var cmd = new SqlCommand("uspGetUserDetail", conn))
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    cmd.Parameters.AddWithValue("@UserName", Username);
                    if (conn.State == ConnectionState.Closed) conn.Open();
                    using (var dr = cmd.ExecuteReader())
                    {
                        while (dr.Read())
                        {
                            user.Name = dr["Name"]?.ToString() ?? string.Empty;
                            user.UserName = dr["Username"]?.ToString() ?? string.Empty;
                            user.Designation = dr["Designation"]?.ToString() ?? string.Empty;
                            user.EmployeeID = dr["Employee ID"]?.ToString() ?? string.Empty;
                            user.Department = dr["Department"]?.ToString() ?? string.Empty;
                            user.DateAdded = dr["DateAdded"] != DBNull.Value ? Convert.ToDateTime(dr["DateAdded"]).ToString("MM-dd-yyyy") : string.Empty;
                            user.UserID = dr["UserID"] != DBNull.Value ? Convert.ToInt32(dr["UserID"]) : 0;
                            user.UserGroupID = dr["UserGroupID"]?.ToString() ?? string.Empty;
                            user.Status = dr["Status"]?.ToString() ?? string.Empty;
                            //user.Email = dr["Email"]?.ToString() ?? string.Empty;
                            user.TwoFactorAuthStatus = dr["2FactorAuthStatus"] != DBNull.Value ? Convert.ToInt32(dr["2FactorAuthStatus"]) : 0;
                        }
                        return user;
                    }
                }
            }
            catch (Exception ex)
            {
                JsonLog.Log(ex.Message);
            }
            finally
            {
                if (conn.State == ConnectionState.Open) conn.Close();
            }
            return user;
        }
        public string AddADUserToDB(UserViewModel userObeject)
        {
            string? result = null;
            try
            {
                using (var cmd = new SqlCommand("usp002AddADUser", conn))
                {
                    cmd.CommandType = CommandType.StoredProcedure;
                    if (conn.State == ConnectionState.Closed) conn.Open();
                    cmd.Parameters.AddWithValue("@rfid", userObeject.RFID);
                    cmd.Parameters.AddWithValue("@firstname", userObeject.FirstName);
                    cmd.Parameters.AddWithValue("@middlename", userObeject.MiddleName);
                    cmd.Parameters.AddWithValue("@lastname", userObeject.LastName);
                    cmd.Parameters.AddWithValue("@username", userObeject.UserName);
                    cmd.Parameters.AddWithValue("@email", userObeject.Email);
                    cmd.Parameters.AddWithValue("@groupid", userObeject.UserGroupID);
                    cmd.Parameters.AddWithValue("@domain", userObeject.Domain);
                    cmd.Parameters.AddWithValue("@computername", userObeject.ComputerName);
                    cmd.Parameters.AddWithValue("@designation", userObeject.Designation);
                    using (var dr = cmd.ExecuteReader())
                    {
                        while (dr.Read())
                        {
                            result = dr["msg"].ToString();
                        }
                    }
                    return result;
                }
            }
            catch (Exception ex)
            {
                JsonLog.Log(ex.Message);
            }
            finally
            {
                if (conn.State == ConnectionState.Open) conn.Close();
            }
            return result;
        }
        public List<LoginFeaturesViewModel> GetLoginSettings()
        {
            List<LoginFeaturesViewModel> features = new List<LoginFeaturesViewModel>();
            string query = LoadQueryFromFile("./wwwroot/assets/Dashboard/assets/Sqlite/GetAuthloginSettings.sql");
            var connection = new SqliteConnectionStringBuilder(connectionString).ToString();
            using (var conn = new SqliteConnection(connection))
            {
                conn.Open();
                using (var cmd = new SqliteCommand(query, conn))
                {
                    using (var dr = cmd.ExecuteReader())
                    {
                        while (dr.Read())
                        {
                            LoginFeaturesViewModel Feature = new LoginFeaturesViewModel();
                            Feature.FeatureName = dr["LoginFeature"].ToString();
                            Feature.Status = Convert.ToBoolean(dr["Status"]);
                            features.Add(Feature);
                        }
                    }
                }
            }
            return features;
        }
        #endregion
    }
}
