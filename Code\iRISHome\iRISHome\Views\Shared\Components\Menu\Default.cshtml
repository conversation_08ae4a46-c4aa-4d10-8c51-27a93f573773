﻿@using iRISHome.Models
@* @model List<MenuItemsViewModel> *@
@inject Microsoft.AspNetCore.Http.IHttpContextAccessor HttpContextAccessor
@using Microsoft.AspNetCore.Http;
@{
    var session = HttpContextAccessor.HttpContext?.Session;
    var appurl = session?.GetString("BaseURL") ?? string.Empty;
    string classname = string.Empty;
    string Title = string.Empty;
}
<script src="~/assets/libs/jquery/jquery.min.js"></script>
@* <div class="vertical-menu">
    <div data-simplebar class="h-100">
        <!--- Sidemenu -->
        <div id="sidebar-menu">
            <!-- Left Menu Start -->
            <ul class="metismenu list-unstyled" id="side-menu">
                <li class="menu-title" key="t-menu">Menu</li>
                @{
                    List<MenuItemsViewModel> GetMenuHierarchy(List<MenuItemsViewModel> modules)

                    {
                        var lookup = modules.ToDictionary(m => m.ModuleID, m => m);

                        foreach (var module in modules.Where(m => m.ParentModuleID != 0))
                        {
                            if (lookup.ContainsKey(module.ParentModuleID))
                            {
                                lookup[module.ParentModuleID].SubModules.Add(module);
                            }
                        }

                        // Return the top-level modules (where ParentModuleId is 0)
                        return modules.Where(m => m.ParentModuleID == 0).ToList();
                    }
                }
                @{
                    var menu = GetMenuHierarchy(Model);
                    for (var i = 0; i < menu.Count; i++)
                    {
                        var module = menu[i];
                        // Check if module has no parent and no children
                        if (!module.SubModules.Any() && module.ParentModuleID == 0)
                        {
                            <li>
                                    <a href="@module.Link" class="waves-effect" style="display: flex; align-items: center; justify-content: space-between;">
                                        <div style="display: flex; align-items: center;">
                                            <i class="@module.ModuleIcon"></i>
                                            <span style="margin-left: 8px;">@module.ModuleName</span>
                                        </div>
                                        <span title="Click to add to favorites"
                                              data-id="Reports"
                                              id="Reports"
                                              bookmarkurl="/ManageReports"
                                              bookmarkstatus="0"
                                              class="bx bx-star Reports text-white fav_btn"
                                              style="font-size: 18px; cursor: pointer;">
                                        </span>
                                    </a>
                            </li>
                        }
                        else
                        {
                            // If the module has sub-modules or is a child module
                            <li>
                                <a href="javascript: void(0);" class="has-arrow waves-effect">
                                    <i class="@module.ModuleIcon"></i>
                                    <span>@module.ModuleName</span>
                                </a>

                                @if (module.SubModules.Any()) // If the module has child modules
                                {
                                    <ul class="sub-menu" aria-expanded="true">
                                        @foreach (var subModule in module.SubModules)
                                        {
                                            // Check if the subModule has more children or is a leaf node
                                            if (subModule.SubModules.Any()) // Sub-module with further nesting
                                            {
                                                <li>
                                                    <a href="javascript: void(0);" class="has-arrow">
                                                        <span>@subModule.ModuleName</span>
                                                    </a>
                                                    <ul class="sub-menu" aria-expanded="true">
                                                        @foreach (var subSubModule in subModule.SubModules)
                                                        {
                                                            if (subSubModule.SubModules.Any())
                                                            {
                                                                <li>
                                                                    <a href="javascript: void(0);" class="has-arrow">
                                                                        <span>@subSubModule.ModuleName</span>
                                                                    </a>
                                                                    <ul class="sub-menu" aria-expanded="true">
                                                                        @foreach (var subSubSubModule in subSubModule.SubModules)
                                                                        {
                                                                            <li>
                                                                                <a href="@subSubSubModule.Link" key="@subSubSubModule.ModuleID" style="display: flex; align-items: center; justify-content: space-between;">
                                                                                    <div style="display: flex; align-items: center;">
                                                                                        <span>@subSubSubModule.ModuleName</span>
                                                                                    </div>
                                                                                    <span title="Click to add to favorites"
                                                                                          data-id="Reports"
                                                                                          id="Reports"
                                                                                          bookmarkurl="/ManageReports"
                                                                                          bookmarkstatus="0"
                                                                                          class="bx bx-star Reports text-white fav_btn"
                                                                                          style="font-size: 18px; cursor: pointer;">
                                                                                    </span>
                                                                                </a>
                                                                            </li>
                                                                        }
                                                                    </ul>
                                                                </li>
                                                            }
                                                            else // Sub-module with no further nesting
                                                            {
                                                                <li>
                                                                    <a href="@subSubModule.Link" key="@subSubModule.ModuleID" style="display: flex; align-items: center; justify-content: space-between;">
                                                                        <div style="display: flex; align-items: center;">
                                                                            @subSubModule.ModuleName
                                                                        </div>
                                                                        <span title="Click to add to favorites"
                                                                              data-id="Reports"
                                                                              id="Reports"
                                                                              bookmarkurl="/ManageReports"
                                                                              bookmarkstatus="0"
                                                                              class="bx bx-star Reports text-white fav_btn"
                                                                              style="font-size: 18px; cursor: pointer;">
                                                                        </span>
                                                                    </a>
                                                                </li>
                                                            }

                                                        }
                                                    </ul>
                                                </li>
                                            }
                                            else // Sub-module with no further nesting
                                            {
                                                <li>
                                                    <a href="@subModule.Link" key="@subModule.ModuleID" style="display: flex; align-items: center; justify-content: space-between;">
                                                        <div style="display: flex; align-items: center;">
                                                            @subModule.ModuleName
                                                        </div>
                                                        <span title="Click to add to favorites"
                                                              data-id="Reports"
                                                              id="Reports"
                                                              bookmarkurl="/ManageReports"
                                                              bookmarkstatus="0"
                                                              class="bx bx-star Reports text-white fav_btn"
                                                              style="font-size: 18px; cursor: pointer;">
                                                        </span>
                                                    </a>
                                                </li>
                                            }
                                        }
                                    </ul>
                                }
                            </li>
                        }
                    }

                }
            </ul>
        </div>
    </div>
</div> *@

<div class="vertical-menu">
    <div data-simplebar="init" class="h-100">
        <div class="simplebar-wrapper" style="margin: 0px;">
            <div class="simplebar-height-auto-observer-wrapper"><div class="simplebar-height-auto-observer"></div></div><div class="simplebar-mask">
                <div class="simplebar-offset" style="right: 0px; bottom: 0px;">
                    <div class="simplebar-content-wrapper" tabindex="0" role="region" aria-label="scrollable content" style="height: 100%; overflow: hidden;">
                        <div class="simplebar-content" style="padding: 0px;">
                            <!--- Sidemenu -->
                            <div id="sidebar-menu">
                                <!-- Left Menu Start -->
                                <ul class="metismenu list-unstyled" id="side-menu">

                                    <li class="mm-active">
                                        <a href="javascript: void(0);" class="has-arrow waves-effect" aria-expanded="true">
                                            <i class="fas fa-user-cog"></i>
                                            <span>Admin</span>
                                        </a>

                                        <ul class="sub-menu mm-collapse mm-show" aria-expanded="true" style="">
                                            <li>
                                                <a href="/iRISDashboard/Admin" key="48" style="display: flex; align-items: center; justify-content: space-between;">
                                                    <div style="display: flex; align-items: center;">
                                                        Manage App Settings
                                                    </div>
                                                    @* <span title="Click to add to favorites" data-id="Reports" id="Reports" bookmarkurl="/ManageReports" bookmarkstatus="0" class="bx bx-star Reports text-white fav_btn" style="font-size: 18px; cursor: pointer;"> *@
                                                    </span>
                                                </a>
                                            </li>
                                        </ul>
                                        <ul class="sub-menu mm-collapse mm-show" aria-expanded="true" style="">
                                            <li>
                                                <a href="@Url.Action("Index","iRISDashboard")" key="48" style="display: flex; align-items: center; justify-content: space-between;">
                                                    <div style="display: flex; align-items: center;">
                                                        Dashboard
                                                    </div>
                                                    @* <span title="Click to add to favorites" data-id="Reports" id="Reports" bookmarkurl="/ManageReports" bookmarkstatus="0" class="bx bx-star Reports text-white fav_btn" style="font-size: 18px; cursor: pointer;"> *@
                                                    </span>
                                                </a>
                                            </li>
                                        </ul>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div><div class="simplebar-placeholder" style="width: 250px; height: 203px;"></div>
        </div><div class="simplebar-track simplebar-horizontal" style="visibility: hidden;"><div class="simplebar-scrollbar" style="width: 0px; display: none;"></div></div><div class="simplebar-track simplebar-vertical" style="visibility: hidden;"><div class="simplebar-scrollbar" style="height: 0px; display: none;"></div></div>
    </div>
</div>

<script>

    //Favorite option

    var ModuleName;

    var Status;

    var TargetID;

    var FavouriteType;

    var FavouriteURL;

    //$('.fav_btn').click(function () {
    $(document).on("click", "span.fav_btn", function () {
        debugger;

        //kendo.ui.progress($("#loader"), true);

        TargetID = $(this).attr('id');

        Status = $(this).attr('bookmarkstatus');

        ModuleName = $(this).attr('data-id');

        FavouriteURL = $(this).attr('bookmarkurl');

        FavouriteType = 'Module';

        $.ajax({

            type: 'post',

            url: '@Url.Action("UpdateFavourites", "Reports")',

            data: { ModuleName: ModuleName, Status: Status, FavouriteType }

        })

            .done(function (response) {

                if (response == "Bookmark removed") {

                    //$("#"+TargetID).css('color', 'white');

                    $("." + TargetID).removeClass('bx bxs-star').addClass('bx bx-star');

                    $("." + TargetID).attr('bookmarkstatus', '0');

                    $("." + TargetID).attr('title', 'Click to add to favorites');

                    //kendo.ui.progress($("#loader"), false);

                    Toastify({

                        text: "Removed from favorites.",

                        duration: 1500,

                        close: true,

                        gravity: "top", // `top` or `bottom`

                        position: "right", // `left`, `center` or `right`

                        stopOnFocus: true, // Prevents dismissing of toast on hover

                        style: {

                            background: "linear-gradient(to right, #00b09b, #96c93d)",

                        }

                    }).showToast();

                    var contentDiv = document.querySelector(".simplebar-content");
                    var newDiv = document.createElement("div");
                    newDiv.className = "mb-2";
                    // Find the anchor element with text "Report name or module name"

                    var anchorToRemove = Array.from(contentDiv.getElementsByTagName("a")).find(function (anchor) {

                        return anchor.textContent.trim() === ModuleName;

                    });

                    // Remove the anchor element if found

                    // if (anchorToRemove) {

                    //     contentDiv.removeChild(anchorToRemove);

                    // }
                    //Remove the span tag
                    // Find the span element with the specified id
                    var existingSpan = Array.from(contentDiv.getElementsByTagName("div")).find(function (div) {
                        return div.querySelector("span." + TargetID);
                        //return span.id === TargetID;//&&
                        //     span.style.marginLeft === "-10px" &&
                        //     span.textContent.trim() === "Favorites is empty";
                    });

                    // Remove the span element if found
                    if (existingSpan) {
                        existingSpan.parentNode.removeChild(existingSpan);
                    }

                    var remainingAnchors = contentDiv.getElementsByTagName("a");

                    if (remainingAnchors.length === 0) {

                        // Add the new anchor tag if the number of anchors is empty

                        var newAnchor = document.createElement("a");

                        newAnchor.href = "#";

                        newAnchor.className = "dropdown-item notify-item";

                        newAnchor.style.marginLeft = "-10px";

                        newAnchor.textContent = "Favorites is empty";
                        contentDiv.appendChild(newAnchor);

                    }

                }

                else if (response == "Bookmark added") {

                    //$("#"+TargetID).css('color', 'yellow');

                    $("." + TargetID).removeClass('bx bx-star').addClass('bx bxs-star');

                    $("." + TargetID).attr('bookmarkstatus', '1');

                    $("." + TargetID).attr('title', 'Click to remove from favorites');

                    //kendo.ui.progress($("#loader"), false);

                    Toastify({

                        text: "Added to favorites.",

                        duration: 1500,

                        close: true,

                        gravity: "top", // `top` or `bottom`

                        position: "right", // `left`, `center` or `right`

                        stopOnFocus: true, // Prevents dismissing of toast on hover

                        style: {

                            background: "linear-gradient(to right, #00b09b, #96c93d)",

                        }

                    }).showToast();

                    // Get the reference to the div with class "simplebar-content"

                    var contentDiv = document.querySelector(".simplebar-content");
                    var newDiv = document.createElement("div");
                    newDiv.className = "mb-2";
                    // Find the anchor element with the specified style and text content

                    var existingAnchor = Array.from(contentDiv.getElementsByTagName("a")).find(function (anchor) {

                        return anchor.className === "dropdown-item notify-item" &&

                            anchor.style.marginLeft === "-10px" &&

                            anchor.textContent.trim() === "Favorites is empty";

                    });

                    // Remove the existing anchor element if found

                    if (existingAnchor) {

                        contentDiv.removeChild(existingAnchor);

                    }


                    // Find the span element with the specified id
                    // var existingSpan = Array.from(contentDiv.getElementsByTagName("span")).find(function (span) {
                    //     return span.className === "fav_btn";//&&
                    //     //     span.style.marginLeft === "-10px" &&
                    //     //     span.textContent.trim() === "Favorites is empty";
                    // });

                    // // Remove the span element if found
                    // if (existingSpan) {
                    //     existingSpan.parentNode.removeChild(existingSpan);
                    // }

                    // Add new anchor tags

                    var anchorElement = document.createElement("a");

                    // Set attributes for the anchor element

                    anchorElement.href = FavouriteURL;

                    anchorElement.className = "dropdown-item notify-item text-truncate";
                    anchorElement.style = "display:inline;"
                    anchorElement.title = ModuleName;

                    // Set the text content for the anchor element

                    anchorElement.textContent = ModuleName;

                    // Get the reference to the div with class "simplebar-content"

                    var contentDiv = document.querySelector(".simplebar-content");

                    // Add new anchor and span tags

                    var spanElement = document.createElement("span");
                    spanElement.title = "Click to remove";
                    spanElement.dataset.id = ModuleName;
                    spanElement.id = TargetID;
                    spanElement.setAttribute("bookmarkurl", FavouriteURL);
                    spanElement.setAttribute("bookmarkstatus", "1");
                    spanElement.className = TargetID + " bx bxs-star text-white fav_btn float-right mr-2";
                    spanElement.style = "font-size:18px; cursor:pointer; color:#fd7106!important;";
                    console.log(spanElement);
                    contentDiv.appendChild(newDiv);
                    newDiv.appendChild(spanElement);

                    // Append the anchor element to the div

                    newDiv.appendChild(anchorElement);


                }

                else {

                    // kendo.ui.progress($("#loader"), false);

                    Toastify({

                        text: "Failed please try again later.",

                        duration: 1500,

                        close: true,

                        gravity: "top", // `top` or `bottom`

                        position: "right", // `left`, `center` or `right`

                        stopOnFocus: true, // Prevents dismissing of toast on hover

                        style: {

                            background: "linear-gradient(to right, #ff5f6d, #ffc371)",

                        }

                    }).showToast();

                }

            })
    })
</script>


