﻿@using iRISDashboard.Models;
@model UpdateAppSettings;

@{
    ViewBag.Title = "Update App Settings";
    ViewBag.pTitle = "Update App Settings";
    ViewBag.pageTitle = "Mobile Aspects";
    ViewBag.BrandName = "iRISHome";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<style>
    .form {
        margin: 20px auto;
        padding: 20px;
        background: #fff;
        border-radius: 5px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    }

    .required {
        color: red;
    }

    #basicpill-ImgURL-input {
        height: 40px; 
    }

    #ImgURLID {
        height: 40px; 
        width: auto; 
    }

</style>

<script src="~/assets/libs/toastify/toastify.js"></script>
<link rel="stylesheet" href="~/assets/libs/toastify/toastify.css">
<script src="~/assets/js/pages/form-validation.init.js"></script>

<div class="row form">
    <div class="col-lg-12">
        <form class="needs-validation custom-validation" novalidate id="NewSettingForm">
            <div class="row mb-3">
                <div class="col-sm-4">
                    <div class="form-group">
                        <label for="AppName"><span class="required">AppName *</span></label>
                        <input type="text" class="form-control" id="AppName" name="AppName" required>
                    </div>
                </div>
                <div class="col-sm-4">
                    <div class="form-group">
                        <label for="AppType"><span class="required">AppType *</span></label>
                        <input type="text" class="form-control" id="basicpill-AppType-input" required>
                    </div>
                </div>
                <div class="col-sm-4">
                    <div class="form-group">
                        <label for="basicpill-Project-input"><span class="required">Project *</span></label>
                        <input type="text" class="form-control" id="basicpill-Project-input" value="@ViewBag.Project" readonly required>
                    </div>
                </div>
            </div>
            <div class="row mb-3">
                <div class="col-6">
                    <label for="basicpill-ImgURL-input" class="mr-2"><span class="required">ImgURL *</span></label>
                    <div class="form-group d-flex align-items-center">
                        <input type="text" class="form-control" id="basicpill-ImgURL-input" value="@ViewBag.ImgURL" readonly required>
                        <div class="ml-2">
                            <img src="@ViewBag.ImgURL" id="ImgURLID" class="img-fluid" />
                        </div>
                    </div>
                </div>
                <div class="col-6">
                    <div class="form-group">
                        <label for="basicpill-AppLink-input"><span class="required">AppLink *</span></label>
                        <input type="text" class="form-control" id="basicpill-AppLink-input" required>
                    </div>
                </div>
            </div>
            <div class="row mb-3">
                <div class="col">
                    <div class="form-group">
                        <label for="basicpill-Description-input"><span class="required">Description *</span></label>
                        <input type="text" class="form-control" id="basicpill-Description-input" required>
                    </div>
                </div>
            </div>
            <div class="form-group d-flex justify-content-end text-right mt-2">
                <button type="button" id="clearDataBtn" class="btn btn-secondary waves-effect me-2"
                        onclick="clearForm()">
                    <span>Clear</span>
                </button>
                <button type="submit" id="submit" class="btn btn-primary" style="margin-left: 6px;">
                    Submit
                </button>
                <button type="button" id="cancel" class="btn btn-primary" style="margin-left: 6px;">
                    Cancel
                </button>
            </div>
        </form>
    </div>
</div>

<script>
    $(document).ready(function() {
        $('#basicpill-ImgURL-input').val('@ViewBag.ImgURL');
        $('#basicpill-Project-input').val('@ViewBag.Project');
        $('#ImgURLID').attr('src', '@ViewBag.ImgURL');
    });

    function clearForm() {
        document.querySelector('.NewSettingForm').reset();
    }

    $("#cancel").click(() => {
        window.history.back();
    });

    $('#NewSettingForm').on('submit', function (e) {
        e.preventDefault();
        var settingToEdit = {
            AppName: $('#AppName').val(),
            AppType: $('#basicpill-AppType-input').val(),
            ImgURL: $('#basicpill-ImgURL-input').val(),
            Project: $('#basicpill-Project-input').val(),
            AppLink: $('#basicpill-AppLink-input').val(),
            Description: $('#basicpill-Description-input').val()
        };

        $.ajax({
            url: '@Url.Action("AddNewSettingToDB", "iRISDashboard")',
            type: 'POST',
            contentType: 'application/json',
            data: JSON.stringify(settingToEdit),
            success: function (response) {
                var model = {
                    Status: "New Setting Added Successfully.",
                    ProjectType: settingToEdit.Project,
                };
                var queryString = $.param(model);
                window.location.href = '@Url.Action("GetProjectSettings", "iRISDashboard")?' + queryString;
            },
            error: function (xhr, status, error) {
                // Handle error
            }
        });
    });
</script>
@section scripts {
    <script src="~/assets/js/app.js"></script>
}