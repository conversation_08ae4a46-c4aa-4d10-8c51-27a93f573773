﻿@using iRISHome.Services
@inject ThemeService ThemeService

@{
    var currentTheme = ThemeService.GetCurrentTheme();
}

<!-- Right Sidebar -->
<div class="right-bar" role="complementary">
    <div data-simplebar class="h-100">
        <div class="rightbar-title d-flex align-items-center px-3 py-4">
            <h5 class="m-0 me-2" id="settings-panel-title">Settings</h5>
            <a href="javascript:void(0);" class="right-bar-toggle ms-auto" aria-label="Close settings panel">
                <i class="mdi mdi-close noti-icon" aria-hidden="true"></i>
            </a>
        </div>

        <!-- Settings -->
        <hr class="mt-0" />
        <h6 class="text-center mb-0">Choose Theme</h6>

        <div class="p-4">
            <div id="theme-form" aria-labelledby="settings-panel-title">
                <div class="form-check form-switch mb-3">
                    <input class="form-check