﻿@inject Microsoft.AspNetCore.Http.IHttpContextAccessor HttpContextAccessor
@using Microsoft.AspNetCore.Http;
@{
    var session = HttpContextAccessor.HttpContext?.Session;
    var Version = session?.GetString("AppVersion");
}
<footer class="footer">
    <div class="container-fluid">
        <div class="row">
            <div class="col-sm-6">
                <script>document.write(new Date().getFullYear())</script> © Mobile Aspects.Version @(Version ?? "")
            </div>
           @*  <div class="col-sm-6">
                <div class="text-sm-end d-none d-sm-block">
                    v2.1.2.3
                </div>
            </div> *@
        </div>
    </div>
</footer>