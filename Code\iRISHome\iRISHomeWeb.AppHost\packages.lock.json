{"version": 2, "dependencies": {"net9.0": {"Aspire.Dashboard.Sdk.win-x64": {"type": "Direct", "requested": "[9.3.1, )", "resolved": "9.3.1", "contentHash": "0BAjVEMMBOkGaxVK3AioTiJKN2nyB+0hjvf8k2ek9LUIMowE2WvQfGUZhdZJaqmfqUCMdmFuhqNzkgubclIcXQ=="}, "Aspire.Hosting": {"type": "Direct", "requested": "[9.3.1, )", "resolved": "9.3.1", "contentHash": "9yVRi8TFV2VQD/7UFT2RezaPKa/91JisKIADAyrkaqJzNFBRQbh/z7xi4MQ6Bzugav/atOdAx4K/NCBdC6LZTg==", "dependencies": {"AspNetCore.HealthChecks.Uris": "9.0.0", "Google.Protobuf": "3.30.2", "Grpc.AspNetCore": "2.71.0", "Grpc.Net.ClientFactory": "2.71.0", "Grpc.Tools": "2.72.0", "Humanizer.Core": "2.14.1", "JsonPatch.Net": "3.3.0", "KubernetesClient": "16.0.7", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.2", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.2", "Microsoft.Extensions.Diagnostics.HealthChecks": "8.0.15", "Microsoft.Extensions.Hosting": "8.0.1", "Microsoft.Extensions.Hosting.Abstractions": "8.0.1", "Microsoft.Extensions.Http": "8.0.1", "Microsoft.Extensions.Logging.Abstractions": "8.0.3", "Microsoft.Extensions.Options": "8.0.2", "Microsoft.Extensions.Primitives": "8.0.0", "Newtonsoft.Json": "13.0.3", "Polly.Core": "8.5.2", "StreamJsonRpc": "2.21.69", "System.IO.Hashing": "9.0.4"}}, "Aspire.Hosting.AppHost": {"type": "Direct", "requested": "[9.3.1, )", "resolved": "9.3.1", "contentHash": "U7lENUUkk97FpGsu6BLi2OA9RNaHKriyLw/t6Q/YOZrsch3PoEH09Hs7UCHfZIErKT40BczHjOvGP0uebNU8Tw==", "dependencies": {"AspNetCore.HealthChecks.Uris": "9.0.0", "Aspire.Hosting": "9.3.1", "Google.Protobuf": "3.30.2", "Grpc.AspNetCore": "2.71.0", "Grpc.Net.ClientFactory": "2.71.0", "Grpc.Tools": "2.72.0", "Humanizer.Core": "2.14.1", "JsonPatch.Net": "3.3.0", "KubernetesClient": "16.0.7", "Microsoft.Extensions.Configuration.Abstractions": "9.0.4", "Microsoft.Extensions.Configuration.Binder": "9.0.4", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.Diagnostics.HealthChecks": "9.0.4", "Microsoft.Extensions.Hosting": "9.0.4", "Microsoft.Extensions.Hosting.Abstractions": "9.0.4", "Microsoft.Extensions.Http": "9.0.4", "Microsoft.Extensions.Logging.Abstractions": "9.0.4", "Microsoft.Extensions.Options": "9.0.4", "Microsoft.Extensions.Primitives": "9.0.4", "Newtonsoft.Json": "13.0.3", "Polly.Core": "8.5.2", "StreamJsonRpc": "2.21.69", "System.IO.Hashing": "9.0.4"}}, "Aspire.Hosting.Orchestration.win-x64": {"type": "Direct", "requested": "[9.3.1, )", "resolved": "9.3.1", "contentHash": "8tA+G0AHRF2DCg0WVwvXTwDGZYXetRlG9E+5UIfdB10lj6MqOSEVr6/1h9lIAQuTwy8HWtI7o8GyDotKg5AWeQ=="}, "AspNetCore.HealthChecks.Uris": {"type": "Transitive", "resolved": "9.0.0", "contentHash": "XYdNlA437KeF8p9qOpZFyNqAN+c0FXt/JjTvzH/Qans0q0O3pPE8KPnn39ucQQjR/Roum1vLTP3kXiUs8VHyuA==", "dependencies": {"Microsoft.Extensions.Diagnostics.HealthChecks": "8.0.11", "Microsoft.Extensions.Http": "8.0.0"}}, "Azure.Core": {"type": "Transitive", "resolved": "1.35.0", "contentHash": "hENcx03Jyuqv05F4RBEPbxz29UrM3Nbhnr6Wl6NQpoU9BCIbL3XLentrxDCTrH54NLS11Exxi/o8MYgT/cnKFA==", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "1.1.1", "System.Diagnostics.DiagnosticSource": "6.0.1", "System.Memory.Data": "1.0.2", "System.Numerics.Vectors": "4.5.0", "System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "4.7.2", "System.Threading.Tasks.Extensions": "4.5.4"}}, "Azure.Identity": {"type": "Transitive", "resolved": "1.10.3", "contentHash": "l1Xm2MWOF2Mzcwuarlw8kWQXLZk3UeB55aQXVyjj23aBfDwOZ3gu5GP2kJ6KlmZeZv2TCzw7x4L3V36iNr3gww==", "dependencies": {"Azure.Core": "1.35.0", "Microsoft.Identity.Client": "4.56.0", "Microsoft.Identity.Client.Extensions.Msal": "4.56.0", "System.Memory": "4.5.4", "System.Security.Cryptography.ProtectedData": "4.7.0", "System.Text.Json": "4.7.2", "System.Threading.Tasks.Extensions": "4.5.4"}}, "Fractions": {"type": "Transitive", "resolved": "7.3.0", "contentHash": "2bETFWLBc8b7Ut2SVi+bxhGVwiSpknHYGBh2PADyGWONLkTxT7bKyDRhF8ao+XUv90tq8Fl7GTPxSI5bacIRJw=="}, "Google.Protobuf": {"type": "Transitive", "resolved": "3.30.2", "contentHash": "Y2aOVLIt75yeeEWigg9V9YnjsEm53sADtLGq0gLhwaXpk3iu8tYSoauolyhenagA2sWno2TQ2WujI0HQd6s1Vw=="}, "Grpc.AspNetCore": {"type": "Transitive", "resolved": "2.71.0", "contentHash": "B4wAbNtAuHNiHAMxLFWL74wUElzNOOboFnypalqpX76piCOGz/w5FpilbVVYGboI4Qgl4ZmZsvDZ1zLwHNsjnw==", "dependencies": {"Google.Protobuf": "3.30.2", "Grpc.AspNetCore.Server.ClientFactory": "2.71.0", "Grpc.Tools": "2.71.0"}}, "Grpc.AspNetCore.Server": {"type": "Transitive", "resolved": "2.71.0", "contentHash": "kv+9YVB6MqDYWIcstXvWrT7Xc1si/sfINzzSxvQfjC3aei+92gXDUXCH/Q+TEvi4QSICRqu92BYcrXUBW7cuOw==", "dependencies": {"Grpc.Net.Common": "2.71.0"}}, "Grpc.AspNetCore.Server.ClientFactory": {"type": "Transitive", "resolved": "2.71.0", "contentHash": "AHvMxoC+esO1e/nOYBjxvn0WDHAfglcVBjtkBy6ohgnV+PzkF8UdkPHE02xnyPFaSokWGZKnWzjgd00x6EZpyQ==", "dependencies": {"Grpc.AspNetCore.Server": "2.71.0", "Grpc.Net.ClientFactory": "2.71.0"}}, "Grpc.Core.Api": {"type": "Transitive", "resolved": "2.71.0", "contentHash": "QquqUC37yxsDzd1QaDRsH2+uuznWPTS8CVE2Yzwl3CvU4geTNkolQXoVN812M2IwT6zpv3jsZRc9ExJFNFslTg=="}, "Grpc.Net.Client": {"type": "Transitive", "resolved": "2.71.0", "contentHash": "U1vr20r5ngoT9nlb7wejF28EKN+taMhJsV9XtK9MkiepTZwnKxxiarriiMfCHuDAfPUm9XUjFMn/RIuJ4YY61w==", "dependencies": {"Grpc.Net.Common": "2.71.0", "Microsoft.Extensions.Logging.Abstractions": "6.0.0"}}, "Grpc.Net.ClientFactory": {"type": "Transitive", "resolved": "2.71.0", "contentHash": "8oPLwQLPo86fmcf9ghjCDyNsSWhtHc3CXa/AqwF8Su/pG7qAoeWWtbymsZhoNvCV9Zjzb6BDcIPKXLYt+O175g==", "dependencies": {"Grpc.Net.Client": "2.71.0", "Microsoft.Extensions.Http": "6.0.0"}}, "Grpc.Net.Common": {"type": "Transitive", "resolved": "2.71.0", "contentHash": "v0c8R97TwRYwNXlC8GyRXwYTCNufpDfUtj9la+wUrZFzVWkFJuNAltU+c0yI3zu0jl54k7en6u2WKgZgd57r2Q==", "dependencies": {"Grpc.Core.Api": "2.71.0"}}, "Grpc.Tools": {"type": "Transitive", "resolved": "2.72.0", "contentHash": "BCiuQ03EYjLHCo9hqZmY5barsz5vvcz/+/ICt5wCbukaePHZmMPDGelKlkxWx3q+f5xOMNHa9zXQ2N6rQZ4B+w=="}, "Humanizer.Core": {"type": "Transitive", "resolved": "2.14.1", "contentHash": "lQKvtaTDOXnoVJ20ibTuSIOf2i0uO0MPbDhd1jm238I+U/2ZnRENj0cktKZhtchBMtCUSRQ5v4xBCUbKNmyVMw=="}, "Json.More.Net": {"type": "Transitive", "resolved": "2.1.0", "contentHash": "qtwsyAsL55y2vB2/sK4Pjg3ZyVzD5KKSpV3lOAMHlnjFfsjQ/86eHJfQT9aV1YysVXzF4+xyHOZbh7Iu3YQ7Lg=="}, "JsonPatch.Net": {"type": "Transitive", "resolved": "3.3.0", "contentHash": "GIcMMDtzfzVfIpQgey8w7dhzcw6jG5nD4DDAdQCTmHfblkCvN7mI8K03to8YyUhKMl4PTR6D6nLSvWmyOGFNTg==", "dependencies": {"JsonPointer.Net": "5.2.0"}}, "JsonPointer.Net": {"type": "Transitive", "resolved": "5.2.0", "contentHash": "qe1F7Tr/p4mgwLPU9P60MbYkp+xnL2uCPnWXGgzfR/AZCunAZIC0RZ32dLGJJEhSuLEfm0YF/1R3u5C7mEVq+w==", "dependencies": {"Humanizer.Core": "2.14.1", "Json.More.Net": "2.1.0"}}, "KubernetesClient": {"type": "Transitive", "resolved": "16.0.7", "contentHash": "hH+YN18bpIRO/rq2CiMGDpLpc/KjSMlAn4EelFB4PgiswbSie4jANLAOou1Q39Kx7en2jO1Qp73y3SkjxGJIMg==", "dependencies": {"Fractions": "7.3.0", "YamlDotNet": "16.3.0"}}, "MessagePack": {"type": "Transitive", "resolved": "2.5.192", "contentHash": "Jtle5MaFeIFkdXtxQeL9Tu2Y3HsAQGoSntOzrn6Br/jrl6c8QmG22GEioT5HBtZJR0zw0s46OnKU8ei2M3QifA==", "dependencies": {"MessagePack.Annotations": "2.5.192", "Microsoft.NET.StringTools": "17.6.3"}}, "MessagePack.Annotations": {"type": "Transitive", "resolved": "2.5.192", "contentHash": "jaJuwcgovWIZ8Zysdyf3b7b34/BrADw4v82GaEZymUhDd3ScMPrYd/cttekeDteJJPXseJxp04yTIcxiVUjTWg=="}, "Microsoft.Bcl.AsyncInterfaces": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "3WA9q9yVqJp222P3x1wYIGDAkpjAku0TMUaaQV22g6L67AI0LdOIrVS7Ht2vJfLHGSPVuqN94vIr15qn+HEkHw=="}, "Microsoft.Bcl.TimeProvider": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "f5Kr5JepAbiGo7uDmhgvMqhntwxqXNn6/IpTBSSI4cuHhgnJGrLxFRhMjVpRkLPp6zJXO0/G0l3j9p9zSJxa+w=="}, "Microsoft.Data.SqlClient.SNI.runtime": {"type": "Transitive", "resolved": "5.2.0", "contentHash": "po1jhvFd+8pbfvJR/puh+fkHi0GRanAdvayh/0e47yaM6CXWZ6opUjCMFuYlAnD2LcbyvQE7fPJKvogmaUcN+w=="}, "Microsoft.Extensions.AmbientMetadata.Application": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "KW4QZv6zVfytBV15Dflu2dwcR9IKiMeS5kwp+vuWfM+bDi8wAd8KdJ8xfMaX3xiIN/Futw/Kpzf01TA8ALRbXw==", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0"}}, "Microsoft.Extensions.Compliance.Abstractions": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "FZ+Y66GF0VwvPgQSmMMNNUxEtjy3Nh9r2iTvZW+UXUtHPmdtXg85NtVYYihhR8guNNQ0LZgfioxiRRjbMECfvw==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.ObjectPool": "8.0.0"}}, "Microsoft.Extensions.Configuration": {"type": "Transitive", "resolved": "9.0.4", "contentHash": "KIVBrMbItnCJDd1RF4KEaE8jZwDJcDUJW5zXpbwQ05HNYTK1GveHxHK0B3SjgDJuR48GRACXAO+BLhL8h34S7g==", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.4", "Microsoft.Extensions.Primitives": "9.0.4"}}, "Microsoft.Extensions.Configuration.Abstractions": {"type": "Transitive", "resolved": "9.0.4", "contentHash": "0LN/DiIKvBrkqp7gkF3qhGIeZk6/B63PthAHjQsxymJfIBcz0kbf4/p/t4lMgggVxZ+flRi5xvTwlpPOoZk8fg==", "dependencies": {"Microsoft.Extensions.Primitives": "9.0.4"}}, "Microsoft.Extensions.Configuration.Binder": {"type": "Transitive", "resolved": "9.0.4", "contentHash": "cdrjcl9RIcwt3ECbnpP0Gt1+pkjdW90mq5yFYy8D9qRj2NqFFcv3yDp141iEamsd9E218sGxK8WHaIOcrqgDJg==", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.4"}}, "Microsoft.Extensions.Configuration.CommandLine": {"type": "Transitive", "resolved": "9.0.4", "contentHash": "TbM2HElARG7z1gxwakdppmOkm1SykPqDcu3EF97daEwSb/+TXnRrFfJtF+5FWWxcsNhbRrmLfS2WszYcab7u1A==", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.4", "Microsoft.Extensions.Configuration.Abstractions": "9.0.4"}}, "Microsoft.Extensions.Configuration.EnvironmentVariables": {"type": "Transitive", "resolved": "9.0.4", "contentHash": "2IGiG3FtVnD83IA6HYGuNei8dOw455C09yEhGl8bjcY6aGZgoC6yhYvDnozw8wlTowfoG9bxVrdTsr2ACZOYHg==", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.4", "Microsoft.Extensions.Configuration.Abstractions": "9.0.4"}}, "Microsoft.Extensions.Configuration.FileExtensions": {"type": "Transitive", "resolved": "9.0.4", "contentHash": "UY864WQ3AS2Fkc8fYLombWnjrXwYt+BEHHps0hY4sxlgqaVW06AxbpgRZjfYf8PyRbplJqruzZDB/nSLT+7RLQ==", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.4", "Microsoft.Extensions.Configuration.Abstractions": "9.0.4", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.4", "Microsoft.Extensions.FileProviders.Physical": "9.0.4", "Microsoft.Extensions.Primitives": "9.0.4"}}, "Microsoft.Extensions.Configuration.Json": {"type": "Transitive", "resolved": "9.0.4", "contentHash": "vVXI70CgT/dmXV3MM+n/BR2rLXEoAyoK0hQT+8MrbCMuJBiLRxnTtSrksNiASWCwOtxo/Tyy7CO8AGthbsYxnw==", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.4", "Microsoft.Extensions.Configuration.Abstractions": "9.0.4", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.4", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.4"}}, "Microsoft.Extensions.Configuration.UserSecrets": {"type": "Transitive", "resolved": "9.0.4", "contentHash": "zuvyC72gJkJyodyGowCuz3EQ1QvzNXJtKusuRzmjoHr17aeB3X0aSiKFB++HMHnQIWWlPOBf9YHTQfEqzbgl1g==", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.4", "Microsoft.Extensions.Configuration.Json": "9.0.4", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.4", "Microsoft.Extensions.FileProviders.Physical": "9.0.4"}}, "Microsoft.Extensions.DependencyInjection": {"type": "Transitive", "resolved": "9.0.4", "contentHash": "f2MTUaS2EQ3lX4325ytPAISZqgBfXmY0WvgD80ji6Z20AoDNiCESxsqo6mFRwHJD/jfVKRw9FsW6+86gNre3ug==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4"}}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"type": "Transitive", "resolved": "9.0.4", "contentHash": "UI0TQPVkS78bFdjkTodmkH0Fe8lXv9LnhGFKgKrsgUJ5a5FVdFRcgjIkBVLbGgdRhxWirxH/8IXUtEyYJx6GQg=="}, "Microsoft.Extensions.DependencyInjection.AutoActivation": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "NuHa4iADMRvcfaHCGV2ZDPwx5cn5GsA/bSkOnwBFkkdpgIsNounqYtyfuorJMwtxHjqGeWEhBLzPHvd+Oemjow==", "dependencies": {"Microsoft.Extensions.Hosting.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}}, "Microsoft.Extensions.DiagnosticAdapter": {"type": "Transitive", "resolved": "3.1.32", "contentHash": "oDv3wt+Q5cmaSfOQ3Cdu6dF6sn/x5gzWdNpOq4ajBwCMWYBr6CchncDvB9pF83ORlbDuX32MsVLOPGPxW4Lx4g==", "dependencies": {"System.Diagnostics.DiagnosticSource": "4.7.1"}}, "Microsoft.Extensions.Diagnostics": {"type": "Transitive", "resolved": "9.0.4", "contentHash": "1bCSQrGv9+bpF5MGKF6THbnRFUZqQDrWPA39NDeVW9djeHBmow8kX4SX6/8KkeKI8gmUDG7jsG/bVuNAcY/ATQ==", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.4", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.4", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.4"}}, "Microsoft.Extensions.Diagnostics.Abstractions": {"type": "Transitive", "resolved": "9.0.4", "contentHash": "IAucBcHYtiCmMyFag+Vrp5m+cjGRlDttJk9Vx7Dqpq+Ama4BzVUOk0JARQakgFFr7ZTBSgLKlHmtY5MiItB7Cg==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.Options": "9.0.4"}}, "Microsoft.Extensions.Diagnostics.ExceptionSummarization": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "3gTLqY8tsiflTXweUdLy4pFj15x3kuGSglhem6UM3FZaui+Bcee7PT/tkV3WMcBRwQ2tsBEAS8XuaSEcbvGLPQ==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}}, "Microsoft.Extensions.Diagnostics.HealthChecks": {"type": "Transitive", "resolved": "9.0.4", "contentHash": "jW9lhWQzOOL5sBUCNtAiS6B7tGeLlxJVDjwNuQAQl6dDt9PAAxt3+T2F2jtcvi7KoujgzAdkKQKtGoRaAGlD9w==", "dependencies": {"Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions": "9.0.4", "Microsoft.Extensions.Hosting.Abstractions": "9.0.4", "Microsoft.Extensions.Logging.Abstractions": "9.0.4", "Microsoft.Extensions.Options": "9.0.4"}}, "Microsoft.Extensions.Diagnostics.HealthChecks.Abstractions": {"type": "Transitive", "resolved": "9.0.4", "contentHash": "XM6WwNbDkVuGhDN89eKxA2Og2eMDXB0PVI7PEzl2R0MbFjYUlfTh7D7vBPEWUVCf2zPDAFiwcMlnVzi6Umq5mg=="}, "Microsoft.Extensions.Features": {"type": "Transitive", "resolved": "8.0.4", "contentHash": "ym56/tUtFHFHucqiyQq42Ha6n1CT3CkrSZmbb3Szfkl/KNEaNqotKXFQMWHlFk2WMvU4fepqUsW8VK5WEjMChA=="}, "Microsoft.Extensions.FileProviders.Abstractions": {"type": "Transitive", "resolved": "9.0.4", "contentHash": "gQN2o/KnBfVk6Bd71E2YsvO5lsqrqHmaepDGk+FB/C4aiQY9B0XKKNKfl5/TqcNOs9OEithm4opiMHAErMFyEw==", "dependencies": {"Microsoft.Extensions.Primitives": "9.0.4"}}, "Microsoft.Extensions.FileProviders.Physical": {"type": "Transitive", "resolved": "9.0.4", "contentHash": "qkQ9V7KFZdTWNThT7ke7E/Jad38s46atSs3QUYZB8f3thBTrcrousdY4Y/tyCtcH5YjsPSiByjuN+L8W/ThMQg==", "dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "9.0.4", "Microsoft.Extensions.FileSystemGlobbing": "9.0.4", "Microsoft.Extensions.Primitives": "9.0.4"}}, "Microsoft.Extensions.FileSystemGlobbing": {"type": "Transitive", "resolved": "9.0.4", "contentHash": "05Lh2ItSk4mzTdDWATW9nEcSybwprN8Tz42Fs5B+jwdXUpauktdAQUI1Am4sUQi2C63E5hvQp8gXvfwfg9mQGQ=="}, "Microsoft.Extensions.Hosting": {"type": "Transitive", "resolved": "9.0.4", "contentHash": "1rZwLE+tTUIyZRUzmlk/DQj+v+Eqox+rjb+X7Fi+cYTbQfIZPYwpf1pVybsV3oje8+Pe4GaNukpBVUlPYeQdeQ==", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.4", "Microsoft.Extensions.Configuration.Abstractions": "9.0.4", "Microsoft.Extensions.Configuration.Binder": "9.0.4", "Microsoft.Extensions.Configuration.CommandLine": "9.0.4", "Microsoft.Extensions.Configuration.EnvironmentVariables": "9.0.4", "Microsoft.Extensions.Configuration.FileExtensions": "9.0.4", "Microsoft.Extensions.Configuration.Json": "9.0.4", "Microsoft.Extensions.Configuration.UserSecrets": "9.0.4", "Microsoft.Extensions.DependencyInjection": "9.0.4", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.Diagnostics": "9.0.4", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.4", "Microsoft.Extensions.FileProviders.Physical": "9.0.4", "Microsoft.Extensions.Hosting.Abstractions": "9.0.4", "Microsoft.Extensions.Logging": "9.0.4", "Microsoft.Extensions.Logging.Abstractions": "9.0.4", "Microsoft.Extensions.Logging.Configuration": "9.0.4", "Microsoft.Extensions.Logging.Console": "9.0.4", "Microsoft.Extensions.Logging.Debug": "9.0.4", "Microsoft.Extensions.Logging.EventLog": "9.0.4", "Microsoft.Extensions.Logging.EventSource": "9.0.4", "Microsoft.Extensions.Options": "9.0.4"}}, "Microsoft.Extensions.Hosting.Abstractions": {"type": "Transitive", "resolved": "9.0.4", "contentHash": "bXkwRPMo4x19YKH6/V9XotU7KYQJlihXhcWO1RDclAY3yfY3XNg4QtSEBvng4kK/DnboE0O/nwSl+6Jiv9P+FA==", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.4", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.Diagnostics.Abstractions": "9.0.4", "Microsoft.Extensions.FileProviders.Abstractions": "9.0.4", "Microsoft.Extensions.Logging.Abstractions": "9.0.4"}}, "Microsoft.Extensions.Http": {"type": "Transitive", "resolved": "9.0.4", "contentHash": "ezelU6HJgmq4862YoWuEbHGSV+JnfnonTSbNSJVh6n6wDehyiJn4hBtcK7rGbf2KO3QeSvK5y8E7uzn1oaRH5w==", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.4", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.Diagnostics": "9.0.4", "Microsoft.Extensions.Logging": "9.0.4", "Microsoft.Extensions.Logging.Abstractions": "9.0.4", "Microsoft.Extensions.Options": "9.0.4"}}, "Microsoft.Extensions.Http.Diagnostics": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "OtaXg8tmoz47bf9M+9J9b9H4e1Wdr7mabSjQ+74eUiBAoIoddA59XtDIli5PcsVAjybzreTFEgOM+aw8/0U6FA==", "dependencies": {"Microsoft.Extensions.DependencyInjection.AutoActivation": "8.0.0", "Microsoft.Extensions.DiagnosticAdapter": "3.1.32", "Microsoft.Extensions.Http": "8.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0", "Microsoft.Extensions.Telemetry": "8.0.0", "Microsoft.IO.RecyclableMemoryStream": "2.3.2", "System.Text.Json": "8.0.0"}}, "Microsoft.Extensions.Logging": {"type": "Transitive", "resolved": "9.0.4", "contentHash": "xW6QPYsqhbuWBO9/1oA43g/XPKbohJx+7G8FLQgQXIriYvY7s+gxr2wjQJfRoPO900dvvv2vVH7wZovG+M1m6w==", "dependencies": {"Microsoft.Extensions.DependencyInjection": "9.0.4", "Microsoft.Extensions.Logging.Abstractions": "9.0.4", "Microsoft.Extensions.Options": "9.0.4"}}, "Microsoft.Extensions.Logging.Abstractions": {"type": "Transitive", "resolved": "9.0.4", "contentHash": "0MXlimU4Dud6t+iNi5NEz3dO2w1HXdhoOLaYFuLPCjAsvlPQGwOT6V2KZRMLEhCAm/stSZt1AUv0XmDdkjvtbw==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4"}}, "Microsoft.Extensions.Logging.Configuration": {"type": "Transitive", "resolved": "9.0.4", "contentHash": "/kF+rSnoo3/nIwGzWsR4RgBnoTOdZ3lzz2qFRyp/GgaNid4j6hOAQrs/O+QHXhlcAdZxjg37MvtIE+pAvIgi9g==", "dependencies": {"Microsoft.Extensions.Configuration": "9.0.4", "Microsoft.Extensions.Configuration.Abstractions": "9.0.4", "Microsoft.Extensions.Configuration.Binder": "9.0.4", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.Logging": "9.0.4", "Microsoft.Extensions.Logging.Abstractions": "9.0.4", "Microsoft.Extensions.Options": "9.0.4", "Microsoft.Extensions.Options.ConfigurationExtensions": "9.0.4"}}, "Microsoft.Extensions.Logging.Console": {"type": "Transitive", "resolved": "9.0.4", "contentHash": "cI0lQe0js65INCTCtAgnlVJWKgzgoRHVAW1B1zwCbmcliO4IZoTf92f1SYbLeLk7FzMJ/GlCvjLvJegJ6kltmQ==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.Logging": "9.0.4", "Microsoft.Extensions.Logging.Abstractions": "9.0.4", "Microsoft.Extensions.Logging.Configuration": "9.0.4", "Microsoft.Extensions.Options": "9.0.4"}}, "Microsoft.Extensions.Logging.Debug": {"type": "Transitive", "resolved": "9.0.4", "contentHash": "D1jy+jy+huUUxnkZ0H480RZK8vqKn8NsQxYpMpPL/ALPPh1WATVLcr/uXI3RUBB45wMW5265O+hk9x3jnnXFuA==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.Logging": "9.0.4", "Microsoft.Extensions.Logging.Abstractions": "9.0.4"}}, "Microsoft.Extensions.Logging.EventLog": {"type": "Transitive", "resolved": "9.0.4", "contentHash": "bApxdklf7QTsONOLR5ow6SdDFXR5ncHvumSEg2+QnCvxvkzc2z5kNn7yQCyupRLRN4jKbnlTkVX8x9qLlwL6Qg==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.Logging": "9.0.4", "Microsoft.Extensions.Logging.Abstractions": "9.0.4", "Microsoft.Extensions.Options": "9.0.4", "System.Diagnostics.EventLog": "9.0.4"}}, "Microsoft.Extensions.Logging.EventSource": {"type": "Transitive", "resolved": "9.0.4", "contentHash": "R600zTxVJNw2IeAEOvdOJGNA1lHr1m3vo460hSF5G1DjwP0FNpyeH4lpLDMuf34diKwB1LTt5hBw1iF1/iuwsQ==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.Logging": "9.0.4", "Microsoft.Extensions.Logging.Abstractions": "9.0.4", "Microsoft.Extensions.Options": "9.0.4", "Microsoft.Extensions.Primitives": "9.0.4"}}, "Microsoft.Extensions.ObjectPool": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "4pm+XgxSukskwjzDDfSjG4KNUIOdFF2VaqZZDtTzoyQMOVSnlV6ZM8a9aVu5dg9LVZTB//utzSc8fOi0b0Mb2Q=="}, "Microsoft.Extensions.Options": {"type": "Transitive", "resolved": "9.0.4", "contentHash": "fiFI2+58kicqVZyt/6obqoFwHiab7LC4FkQ3mmiBJ28Yy4fAvy2+v9MRnSvvlOO8chTOjKsdafFl/K9veCPo5g==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.Primitives": "9.0.4"}}, "Microsoft.Extensions.Options.ConfigurationExtensions": {"type": "Transitive", "resolved": "9.0.4", "contentHash": "aridVhAT3Ep+vsirR1pzjaOw0Jwiob6dc73VFQn2XmDfBA2X98M8YKO1GarvsXRX7gX1Aj+hj2ijMzrMHDOm0A==", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "9.0.4", "Microsoft.Extensions.Configuration.Binder": "9.0.4", "Microsoft.Extensions.DependencyInjection.Abstractions": "9.0.4", "Microsoft.Extensions.Options": "9.0.4", "Microsoft.Extensions.Primitives": "9.0.4"}}, "Microsoft.Extensions.Primitives": {"type": "Transitive", "resolved": "9.0.4", "contentHash": "SPFyMjyku1nqTFFJ928JAMd0QnRe4xjE7KeKnZMWXf3xk+6e0WiOZAluYtLdbJUXtsl2cCRSi8cBquJ408k8RA=="}, "Microsoft.Extensions.Resilience": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "QqpjuGYLQPd3vueN6dP5QAFlZRm+rE7GBSWu2h9EwJHt9A/YnO1iM3eJAUNajQ/OL8oqVn4VequbybTirY6vWw==", "dependencies": {"Microsoft.Extensions.Diagnostics": "8.0.0", "Microsoft.Extensions.Diagnostics.ExceptionSummarization": "8.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0", "Microsoft.Extensions.Telemetry.Abstractions": "8.0.0", "Polly.Core": "8.0.0", "Polly.Extensions": "8.0.0", "Polly.RateLimiting": "8.0.0"}}, "Microsoft.Extensions.ServiceDiscovery.Abstractions": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "db0gjxG8qLtEkSYBSOafxVr5v1Lf7O4dbWKhlj0uMB6dK6Y8KLQz3YBtxXDpmtww1mIzrHlzdWA+jN1dKjerkA==", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.1", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.1", "Microsoft.Extensions.Features": "8.0.4", "Microsoft.Extensions.Logging.Abstractions": "8.0.1", "Microsoft.Extensions.Options": "8.0.2", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Telemetry.Abstractions": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "lsFZSSPUO3KVHuiVUHB+HM4OF6dZywXlgab8S5Wh9I2GeXJPXAxWU7SX0wPiqdTkmH3J9z3bUu2lOwrPTXQsjQ==", "dependencies": {"Microsoft.Extensions.Compliance.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.ObjectPool": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "System.Diagnostics.DiagnosticSource": "8.0.0"}}, "Microsoft.Identity.Client.Extensions.Msal": {"type": "Transitive", "resolved": "4.56.0", "contentHash": "H12YAzEGK55vZ+QpxUzozhW8ZZtgPDuWvgA0JbdIR9UhMUplj29JhIgE2imuH8W2Nw9D8JKygR1uxRFtpSNcrg==", "dependencies": {"Microsoft.Identity.Client": "4.56.0", "System.IO.FileSystem.AccessControl": "5.0.0", "System.Security.Cryptography.ProtectedData": "4.5.0"}}, "Microsoft.IdentityModel.Abstractions": {"type": "Transitive", "resolved": "8.0.1", "contentHash": "OtlIWcyX01olfdevPKZdIPfBEvbcioDyBiE/Z2lHsopsMD7twcKtlN9kMevHmI5IIPhFpfwCIiR6qHQz1WHUIw=="}, "Microsoft.IdentityModel.JsonWebTokens": {"type": "Transitive", "resolved": "8.0.1", "contentHash": "s6++gF9x0rQApQzOBbSyp4jUaAlwm+DroKfL8gdOHxs83k8SJfUXhuc46rDB3rNXBQ1MVRxqKUrqFhO/M0E97g==", "dependencies": {"Microsoft.IdentityModel.Tokens": "8.0.1"}}, "Microsoft.IdentityModel.Logging": {"type": "Transitive", "resolved": "8.0.1", "contentHash": "UCPF2exZqBXe7v/6sGNiM6zCQOUXXQ9+v5VTb9gPB8ZSUPnX53BxlN78v2jsbIvK9Dq4GovQxo23x8JgWvm/Qg==", "dependencies": {"Microsoft.IdentityModel.Abstractions": "8.0.1"}}, "Microsoft.IdentityModel.Protocols": {"type": "Transitive", "resolved": "8.0.1", "contentHash": "uA2vpKqU3I2mBBEaeJAWPTjT9v1TZrGWKdgK6G5qJd03CLx83kdiqO9cmiK8/n1erkHzFBwU/RphP83aAe3i3g==", "dependencies": {"Microsoft.IdentityModel.Tokens": "8.0.1"}}, "Microsoft.IdentityModel.Protocols.OpenIdConnect": {"type": "Transitive", "resolved": "8.0.1", "contentHash": "AQDbfpL+yzuuGhO/mQhKNsp44pm5Jv8/BI4KiFXR7beVGZoSH35zMV3PrmcfvSTsyI6qrcR898NzUauD6SRigg==", "dependencies": {"Microsoft.IdentityModel.Protocols": "8.0.1", "System.IdentityModel.Tokens.Jwt": "8.0.1"}}, "Microsoft.IdentityModel.Tokens": {"type": "Transitive", "resolved": "8.0.1", "contentHash": "kDimB6Dkd3nkW2oZPDkMkVHfQt3IDqO5gL0oa8WVy3OP4uE8Ij+8TXnqg9TOd9ufjsY3IDiGz7pCUbnfL18tjg==", "dependencies": {"Microsoft.IdentityModel.Logging": "8.0.1"}}, "Microsoft.IO.RecyclableMemoryStream": {"type": "Transitive", "resolved": "2.3.2", "contentHash": "Oh1qXXFdJFcHozvb4H6XYLf2W0meZFuG0A+TfapFPj9z5fd4vxiARGEhAaLj/6XWQaMYIv4SH/9Q6H78Hw0E2Q=="}, "Microsoft.NET.StringTools": {"type": "Transitive", "resolved": "17.6.3", "contentHash": "N0ZIanl1QCgvUumEL1laasU0a7sOE5ZwLZVTn0pAePnfhq8P7SvTjF8Axq+CnavuQkmdQpGNXQ1efZtu5kDFbA=="}, "Microsoft.NETCore.Platforms": {"type": "Transitive", "resolved": "5.0.0", "contentHash": "VyPlqzH2wavqquTcYpkIIAQ6WdenuKoFN0BdYBbCWsclXacSOHNQn66Gt4z5NBqEYW0FAPm5rlvki9ZiCij5xQ=="}, "Microsoft.SqlServer.Server": {"type": "Transitive", "resolved": "1.0.0", "contentHash": "N4KeF3cpcm1PUHym1RmakkzfkEv3GRMyofVv40uXsQhCQeglr2OHNcUk2WOG51AKpGO8ynGpo9M/kFXSzghwug=="}, "Microsoft.VisualStudio.Threading.Only": {"type": "Transitive", "resolved": "17.13.61", "contentHash": "vl5a2URJYCO5m+aZZtNlAXAMz28e2pUotRuoHD7RnCWOCeoyd8hWp5ZBaLNYq4iEj2oeJx5ZxiSboAjVmB20Qg==", "dependencies": {"Microsoft.VisualStudio.Validation": "17.8.8"}}, "Microsoft.VisualStudio.Validation": {"type": "Transitive", "resolved": "17.8.8", "contentHash": "rWXThIpyQd4YIXghNkiv2+VLvzS+MCMKVRDR0GAMlflsdo+YcAN2g2r5U1Ah98OFjQMRexTFtXQQ2LkajxZi3g=="}, "Nerdbank.Streams": {"type": "Transitive", "resolved": "2.11.90", "contentHash": "7jrOfU6b/PVBccqzNLfw9u84WWzkSpvWLb2mZxvwdQkOx/V9FXWkmnp/rjOnBFDOhrO/ev4+gQ5QS13FkgNSBA==", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "8.0.0", "Microsoft.VisualStudio.Threading.Only": "17.13.61", "Microsoft.VisualStudio.Validation": "17.8.8", "System.IO.Pipelines": "8.0.0", "System.Runtime.CompilerServices.Unsafe": "6.1.0"}}, "OpenTelemetry": {"type": "Transitive", "resolved": "1.7.0", "contentHash": "HNmOJg++4FtEJGCIn1dCJcDkHRLNuLKU047owrGXUOfz/C/c5oZHOPKrowKVOy2jOOh/F9+HDshzeOk5NnQEZg==", "dependencies": {"Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Configuration": "8.0.0", "OpenTelemetry.Api.ProviderBuilderExtensions": "1.7.0"}}, "OpenTelemetry.Api": {"type": "Transitive", "resolved": "1.8.0", "contentHash": "+daN4OqIXne3QLlFHEzb6ybAETgKs7Hg5jINYT5P8p8A/cEtuP6CRDYdDpOe6AlW8oAaV/nJJ5tLyPUVKKQu0w==", "dependencies": {"System.Diagnostics.DiagnosticSource": "8.0.0"}}, "OpenTelemetry.Api.ProviderBuilderExtensions": {"type": "Transitive", "resolved": "1.8.0", "contentHash": "BLo2IwO+sJZMsedvKyMtDJL1Pk7gto2B5lf0rkGKigihUUYDaWwh7HTYtftTamXn0UthCB9B+VUaF7h9AohXMg==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "OpenTelemetry.Api": "1.8.0"}}, "Polly.Core": {"type": "Transitive", "resolved": "8.5.2", "contentHash": "1MJKdxv4zwDmiWvYvVN24DsrWUfgQ4F83voH8bhbtLMdPuGy8CfTUzsgQhvyrl1a7hrM6f/ydwLVdVUI0xooUw=="}, "Polly.Extensions": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "XGasiRYj4ecCLBrIH/caPRkrUb891KbNYSLB+JFy/wh7wVt8Su7dNqhiP7FjcJXTa+0MA96UR7l0uFmhSUSaYQ==", "dependencies": {"Microsoft.Extensions.Logging.Abstractions": "6.0.0", "Microsoft.Extensions.Options": "6.0.0", "Polly.Core": "8.0.0", "System.Diagnostics.DiagnosticSource": "7.0.0"}}, "Polly.RateLimiting": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "x4GgLxNcSLILTyjLJxUItbfr4HyPq5DhMh8FpY8CZDCf4wrGXZtHzaD6NjNB93oVEZxntYv4rpx0SDMAdj3Nqw==", "dependencies": {"Polly.Core": "8.0.0", "System.Threading.RateLimiting": "7.0.0"}}, "runtime.native.System.Data.SqlClient.sni": {"type": "Transitive", "resolved": "4.4.0", "contentHash": "A8v6PGmk+UGbfWo5Ixup0lPM4swuSwOiayJExZwKIOjTlFFQIsu3QnDXECosBEyrWSPryxBVrdqtJyhK3BaupQ==", "dependencies": {"runtime.win-arm64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x64.runtime.native.System.Data.SqlClient.sni": "4.4.0", "runtime.win-x86.runtime.native.System.Data.SqlClient.sni": "4.4.0"}}, "runtime.win-arm64.runtime.native.System.Data.SqlClient.sni": {"type": "Transitive", "resolved": "4.4.0", "contentHash": "LbrynESTp3bm5O/+jGL8v0Qg5SJlTV08lpIpFesXjF6uGNMWqFnUQbYBJwZTeua6E/Y7FIM1C54Ey1btLWupdg=="}, "runtime.win-x64.runtime.native.System.Data.SqlClient.sni": {"type": "Transitive", "resolved": "4.4.0", "contentHash": "38ugOfkYJqJoX9g6EYRlZB5U2ZJH51UP8ptxZgdpS07FgOEToV+lS11ouNK2PM12Pr6X/PpT5jK82G3DwH/SxQ=="}, "runtime.win-x86.runtime.native.System.Data.SqlClient.sni": {"type": "Transitive", "resolved": "4.4.0", "contentHash": "YhEdSQUsTx+C8m8Bw7ar5/VesXvCFMItyZF7G1AUY+OM0VPZUOeAVpJ4Wl6fydBGUYZxojTDR3I6Bj/+BPkJNA=="}, "Serilog": {"type": "Transitive", "resolved": "2.10.0", "contentHash": "+QX0hmf37a0/OZLxM3wL7V6/ADvC1XihXN4Kq/p6d8lCPfgkRdiuhbWlMaFjR9Av0dy5F0+MBeDmDdRZN/YwQA=="}, "SQLitePCLRaw.core": {"type": "Transitive", "resolved": "2.1.10", "contentHash": "Ii8JCbC7oiVclaE/mbDEK000EFIJ+ShRPwAvvV89GOZhQ+ZLtlnSWl6ksCNMKu/VGXA4Nfi2B7LhN/QFN9oBcw==", "dependencies": {"System.Memory": "4.5.3"}}, "StreamJsonRpc": {"type": "Transitive", "resolved": "2.21.69", "contentHash": "WbTpn/PIo+HpFYnsOCiOOe0kHUE2N1eiVRi7MO70DFBTMG3pAOfrgHtwUpOJ37dfDETq/9P9WNIbHom4ABZfrA==", "dependencies": {"MessagePack": "2.5.192", "Microsoft.Bcl.AsyncInterfaces": "8.0.0", "Microsoft.VisualStudio.Threading.Only": "17.13.61", "Microsoft.VisualStudio.Validation": "17.8.8", "Nerdbank.Streams": "2.11.90", "Newtonsoft.Json": "13.0.3", "System.IO.Pipelines": "8.0.0"}}, "System.Diagnostics.DiagnosticSource": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "c9xLpVz6PL9lp/djOWtk5KPDZq3cSYpmXoJQY524EOtuFl5z9ZtsotpsyrDW40U1DRnQSYvcPKEUV0X//u6gkQ=="}, "System.Diagnostics.EventLog": {"type": "Transitive", "resolved": "9.0.4", "contentHash": "getRQEXD8idlpb1KW56XuxImMy0FKp2WJPDf3Qr0kI/QKxxJSftqfDFVo0DZ3HCJRLU73qHSruv5q2l5O47jQQ=="}, "System.IO.FileSystem.AccessControl": {"type": "Transitive", "resolved": "5.0.0", "contentHash": "SxHB3nuNrpptVk+vZ/F+7OHEpoHUIKKMl02bUmYHQr1r+glbZQxs7pRtsf4ENO29TVm2TH3AEeep2fJcy92oYw==", "dependencies": {"System.Security.AccessControl": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "System.IO.Hashing": {"type": "Transitive", "resolved": "9.0.4", "contentHash": "WogPvgAFqQORFD8Iyha6RZ+/1QB3dsWRWxbwi8/HHVgiGQ8z0oMWpwe8Kk3Ti+Roe+P6a3sBg+WwBfEsyziZKg=="}, "System.IO.Pipelines": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "FHNOatmUq0sqJOkTx+UF/9YK1f180cnW5FVqnQMvYUN0elp6wFzbtPSiqbo1/ru8ICp43JM1i7kKkk6GsNGHlA=="}, "System.Memory": {"type": "Transitive", "resolved": "4.5.4", "contentHash": "1MbJTHS1lZ4bS4FmsJjnuGJOu88ZzTT2rLvrhW7Ygic+pC0NWA+3hgAen0HRdsocuQXCkUTdFn9yHJJhsijDXw=="}, "System.Memory.Data": {"type": "Transitive", "resolved": "1.0.2", "contentHash": "JGkzeqgBsiZwKJZ1IxPNsDFZDhUvuEdX8L8BDC8N3KOj+6zMcNU28CNN59TpZE/VJYy9cP+5M+sbxtWJx3/xtw==", "dependencies": {"System.Text.Encodings.Web": "4.7.2", "System.Text.Json": "4.6.0"}}, "System.Numerics.Vectors": {"type": "Transitive", "resolved": "4.5.0", "contentHash": "QQTlPTl06J/iiDbJCiepZ4H//BVraReU4O4EoRw1U02H5TLUIT7xn3GnDp9AXPSlJUDyFs4uWjWafNX6WrAojQ=="}, "System.Runtime.Caching": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "4TmlmvGp4kzZomm7J2HJn6IIx0UUrQyhBDyb5O1XiunZlQImXW+B8b7W/sTPcXhSf9rp5NR5aDtQllwbB5elOQ==", "dependencies": {"System.Configuration.ConfigurationManager": "8.0.0"}}, "System.Runtime.CompilerServices.Unsafe": {"type": "Transitive", "resolved": "6.1.0", "contentHash": "5o/HZxx6RVqYlhKSq8/zronDkALJZUT2Vz0hx43f0gwe8mwlM0y2nYlqdBwLMzr262Bwvpikeb/yEwkAa5PADg=="}, "System.Security.AccessControl": {"type": "Transitive", "resolved": "5.0.0", "contentHash": "dagJ1mHZO3Ani8GH0PHpPEe/oYO+rVdbQjvjJkBRNQkX4t0r1iaeGn8+/ybkSLEan3/slM0t59SVdHzuHf2jmw==", "dependencies": {"Microsoft.NETCore.Platforms": "5.0.0", "System.Security.Principal.Windows": "5.0.0"}}, "System.Security.Cryptography.ProtectedData": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "+TUFINV2q2ifyXauQXRwy4CiBhqvDEDZeVJU7qfxya4aRYOKzVBpN+4acx25VcPB9ywUN6C0n8drWl110PhZEg=="}, "System.Security.Principal.Windows": {"type": "Transitive", "resolved": "5.0.0", "contentHash": "t0MGLukB5WAVU9bO3MGzvlGnyJPgUlcwerXn1kzBRjwLKixT96XV0Uza41W49gVd8zEMFu9vQEFlv0IOrytICA=="}, "System.Text.Encodings.Web": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "yev/k9GHAEGx2Rg3/tU6MQh4HGBXJs70y7j1LaM1i/ER9po+6nnQ6RRqTJn1E7Xu0fbIFK80Nh5EoODxrbxwBQ=="}, "System.Text.Json": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "OdrZO2WjkiEG6ajEFRABTRCi/wuXQPxeV6g8xvUJqdxMvvuCCEk86zPla8UiIQJz3durtUEbNyY/3lIhS0yZvQ==", "dependencies": {"System.Text.Encodings.Web": "8.0.0"}}, "System.Threading.RateLimiting": {"type": "Transitive", "resolved": "7.0.0", "contentHash": "1vzNiIzf11QzIcqTLf9d9+DcLt2xVT9XtP4pB/ahHngVH1VKEQ+S6yhpFlipb7JxM8VMUK9IBNlsDwWj0vUOTQ=="}, "System.Threading.Tasks.Extensions": {"type": "Transitive", "resolved": "4.5.4", "contentHash": "zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg=="}, "YamlDotNet": {"type": "Transitive", "resolved": "16.3.0", "contentHash": "SgMOdxbz8X65z8hraIs6hOEdnkH6hESTAIUa7viEngHOYaH+6q5XJmwr1+yb9vJpNQ19hCQY69xbFsLtXpobQA=="}, "authlogin": {"type": "Project", "dependencies": {"Microsoft.AspNetCore.Authentication.OpenIdConnect": "[9.0.6, )", "Microsoft.Data.SqlClient": "[5.2.0, )", "Microsoft.Data.Sqlite.Core": "[9.0.6, )", "Microsoft.Identity.Client": "[4.60.4, )", "Newtonsoft.Json": "[13.0.3, )", "System.Data.SqlClient": "[4.9.0, )", "System.IdentityModel.Tokens.Jwt": "[8.0.1, )", "iRISHomeWeb.ServiceDefaults": "[1.0.0, )"}}, "irisdashboard": {"type": "Project", "dependencies": {"Microsoft.Data.SqlClient": "[5.2.0, )", "Microsoft.Data.Sqlite.Core": "[9.0.6, )", "Microsoft.Identity.Client": "[4.60.4, )", "Newtonsoft.Json": "[13.0.3, )", "Serilog.Enrichers.Process": "[2.0.2, )", "Serilog.Enrichers.Thread": "[3.1.0, )", "SerilogTimings": "[3.0.1, )", "System.Configuration.ConfigurationManager": "[8.0.0, )", "System.IdentityModel.Tokens.Jwt": "[8.0.1, )", "iRISHomeWeb.ServiceDefaults": "[1.0.0, )"}}, "irishomeweb.servicedefaults": {"type": "Project", "dependencies": {"Microsoft.Extensions.Http.Resilience": "[8.0.0, )", "Microsoft.Extensions.ServiceDiscovery": "[8.0.0, )", "OpenTelemetry.Extensions.Hosting": "[1.7.0, )", "OpenTelemetry.Instrumentation.AspNetCore": "[1.8.1, )", "OpenTelemetry.Instrumentation.Http": "[1.8.1, )"}}, "Microsoft.AspNetCore.Authentication.OpenIdConnect": {"type": "CentralTransitive", "requested": "[9.0.6, )", "resolved": "9.0.6", "contentHash": "cRsREllwLWFEl83me6iC5LO6sHy/X0tw1X2bXmfGBulrmM5BvoVuUkJegfpEV1R2Q89gdv8ckS5ZaPxdocUVPw==", "dependencies": {"Microsoft.IdentityModel.Protocols.OpenIdConnect": "8.0.1"}}, "Microsoft.Data.SqlClient": {"type": "CentralTransitive", "requested": "[5.2.0, )", "resolved": "5.2.0", "contentHash": "3alfyqRN3ELRtdvU1dGtLBRNQqprr3TJ0WrUJfMISPwg1nPUN2P3Lelah68IKWuV27Ceb7ig95hWNHFTSXfxMg==", "dependencies": {"Azure.Identity": "1.10.3", "Microsoft.Data.SqlClient.SNI.runtime": "5.2.0", "Microsoft.Identity.Client": "4.56.0", "Microsoft.IdentityModel.JsonWebTokens": "6.35.0", "Microsoft.IdentityModel.Protocols.OpenIdConnect": "6.35.0", "Microsoft.SqlServer.Server": "1.0.0", "System.Configuration.ConfigurationManager": "8.0.0", "System.Runtime.Caching": "8.0.0"}}, "Microsoft.Data.Sqlite.Core": {"type": "CentralTransitive", "requested": "[9.0.6, )", "resolved": "9.0.6", "contentHash": "3auiudiViGzj1TidUdjuDqtP3+f6PBk4xdw6r9sBaTtkYoGc3AZn0cP8LgYZaLRnJBqY5bXRLB+qhjoB+iATzA==", "dependencies": {"SQLitePCLRaw.core": "2.1.10"}}, "Microsoft.Extensions.Http.Resilience": {"type": "CentralTransitive", "requested": "[8.0.0, )", "resolved": "8.0.0", "contentHash": "NyZ8XPwgPfyXRdSq1wYc16Zf71patMLPHtT+nBkMRP7Jdcxf+Q3XaE6MEk3mAQBfUsV6OxyL4vvnZTZ+S3wdIA==", "dependencies": {"Microsoft.Extensions.Http.Diagnostics": "8.0.0", "Microsoft.Extensions.ObjectPool": "8.0.0", "Microsoft.Extensions.Resilience": "8.0.0"}}, "Microsoft.Extensions.ServiceDiscovery": {"type": "CentralTransitive", "requested": "[8.0.0, )", "resolved": "8.0.0", "contentHash": "VXevCiq3yEfRorMyIf2XMZkwhnEbyo3fNoN3s7a/u/4BT8zlATacPxfTgH/fRUznxNjyHzplDT/rKdgdnQsjhg==", "dependencies": {"Microsoft.Extensions.Http": "8.0.0", "Microsoft.Extensions.ServiceDiscovery.Abstractions": "8.0.0"}}, "Microsoft.Extensions.Telemetry": {"type": "CentralTransitive", "requested": "[9.6.0, )", "resolved": "8.0.0", "contentHash": "JsIErSvWidQKIt17kF/Th5ag374D9duZKaDODeb3Sq6X5IpE7YHieykisy89XdKf+k8ocE+aqxcvInlbiRVXKw==", "dependencies": {"Microsoft.Bcl.TimeProvider": "8.0.0", "Microsoft.Extensions.AmbientMetadata.Application": "8.0.0", "Microsoft.Extensions.Compliance.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.AutoActivation": "8.0.0", "Microsoft.Extensions.Logging.Configuration": "8.0.0", "Microsoft.Extensions.ObjectPool": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Telemetry.Abstractions": "8.0.0"}}, "Microsoft.Identity.Client": {"type": "CentralTransitive", "requested": "[4.60.4, )", "resolved": "4.60.4", "contentHash": "7gLb5KF/tJMIoO891C8Ij7kaj5EoU1gTazdYPUZgkEhIZQ6ii74Md8mJXMDvX9fSb3EUQ4lyex5pI0jqpCJivw==", "dependencies": {"Microsoft.IdentityModel.Abstractions": "6.35.0", "System.Diagnostics.DiagnosticSource": "6.0.1"}}, "Newtonsoft.Json": {"type": "CentralTransitive", "requested": "[13.0.3, )", "resolved": "13.0.3", "contentHash": "HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ=="}, "OpenTelemetry.Extensions.Hosting": {"type": "CentralTransitive", "requested": "[1.7.0, )", "resolved": "1.7.0", "contentHash": "MbB7CWWqb7xHK0jTF9Gtvw/eLWdaKqzkE1XAwLe05xyskHuwJWAbZFax4nGLA71YkMWQNO5iPIBlirvYXOLMlg==", "dependencies": {"Microsoft.Extensions.Diagnostics.Abstractions": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.0", "OpenTelemetry": "1.7.0"}}, "OpenTelemetry.Instrumentation.AspNetCore": {"type": "CentralTransitive", "requested": "[1.8.1, )", "resolved": "1.8.1", "contentHash": "dRb1LEXSH95LGEubk96kYyBmGuny9/qycH9KqL8FXcOv446Xi53EW56TVE4wTMv4HPfn+rL3B9pPQ5RX7zD4Yw==", "dependencies": {"OpenTelemetry.Api.ProviderBuilderExtensions": "1.8.0"}}, "OpenTelemetry.Instrumentation.Http": {"type": "CentralTransitive", "requested": "[1.8.1, )", "resolved": "1.8.1", "contentHash": "l1KaO1U+v11X/kfZ8tcONc5l1qoP6nPk6yPrXBJNH0Wb6NEBTdEgI1dtJBbqOnjOrI2XS09le0ZGooh9ZVkZ3Q==", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "OpenTelemetry.Api.ProviderBuilderExtensions": "1.8.0"}}, "Serilog.Enrichers.Process": {"type": "CentralTransitive", "requested": "[2.0.2, )", "resolved": "2.0.2", "contentHash": "T9EjKKLsL6qC/3eOLUAKEPBLEqPDmt5BLXaQdPMaxJzuex+MeXA8DuAiPboUaftp3kbnCN4ZgZpDvs+Fa7OHuw==", "dependencies": {"Serilog": "2.3.0"}}, "Serilog.Enrichers.Thread": {"type": "CentralTransitive", "requested": "[3.1.0, )", "resolved": "3.1.0", "contentHash": "85lWsGRJpRxvKT6j/H67no55SUBsBIvp556TKuBTGhjtoPeq+L7j/sDWbgAtvT0p7u7/phJyX6j35PQ4Vtqw0g==", "dependencies": {"Serilog": "2.3.0"}}, "SerilogTimings": {"type": "CentralTransitive", "requested": "[3.0.1, )", "resolved": "3.0.1", "contentHash": "Zs28eTgszAMwpIrbBnWHBI50yuxL50p/dmAUWmy75+axdZYK/Sjm5/5m1N/CisR8acJUhTVcjPZrsB1P5iv0Uw==", "dependencies": {"Serilog": "2.10.0"}}, "System.Configuration.ConfigurationManager": {"type": "CentralTransitive", "requested": "[8.0.0, )", "resolved": "8.0.0", "contentHash": "JlYi9XVvIREURRUlGMr1F6vOFLk7YSY4p1vHo4kX3tQ0AGrjqlRWHDi66ImHhy6qwXBG3BJ6Y1QlYQ+Qz6Xgww==", "dependencies": {"System.Diagnostics.EventLog": "8.0.0", "System.Security.Cryptography.ProtectedData": "8.0.0"}}, "System.Data.SqlClient": {"type": "CentralTransitive", "requested": "[4.9.0, )", "resolved": "4.9.0", "contentHash": "j4KJO+vC62NyUtNHz854njEqXbT8OmAa5jb1nrGfYWBOcggyYUQE0w/snXeaCjdvkSKWuUD+hfvlbN8pTrJTXg==", "dependencies": {"runtime.native.System.Data.SqlClient.sni": "4.4.0"}}, "System.IdentityModel.Tokens.Jwt": {"type": "CentralTransitive", "requested": "[8.0.1, )", "resolved": "8.0.1", "contentHash": "GJw3bYkWpOgvN3tJo5X4lYUeIFA2HD293FPUhKmp7qxS+g5ywAb34Dnd3cDAFLkcMohy5XTpoaZ4uAHuw0uSPQ==", "dependencies": {"Microsoft.IdentityModel.JsonWebTokens": "8.0.1", "Microsoft.IdentityModel.Tokens": "8.0.1"}}}}}