{"version": 3, "sources": ["custom/fonts/_fonts.scss", "custom/structure/_topbar.scss", "app.css", "custom/structure/_page-head.scss", "custom/structure/_footer.scss", "custom/structure/_right-sidebar.scss", "../../../node_modules/bootstrap/scss/mixins/_breakpoints.scss", "custom/structure/_vertical.scss", "custom/structure/_horizontal-nav.scss", "custom/structure/_layouts.scss", "custom/components/_waves.scss", "custom/components/_avatar.scss", "custom/components/_accordion.scss", "custom/components/_helper.scss", "custom/components/_preloader.scss", "custom/components/_forms.scss", "custom/components/_widgets.scss", "custom/components/_demos.scss", "custom/components/_print.scss", "custom/plugins/_custom-scrollbar.scss", "custom/plugins/_calendar.scss", "custom/plugins/_calendar-full.scss", "custom/plugins/_dragula.scss", "custom/plugins/_session-timeout.scss", "custom/plugins/_range-slider.scss", "custom/plugins/_sweatalert2.scss", "custom/plugins/_rating.scss", "custom/plugins/_toastr.scss", "custom/plugins/_parsley.scss", "custom/plugins/_select2.scss", "custom/plugins/_switch.scss", "custom/plugins/_colorpicker.scss", "custom/plugins/_timepicker.scss", "custom/plugins/_datepicker.scss", "custom/plugins/_bootstrap-touchspin.scss", "custom/plugins/_form-editors.scss", "custom/plugins/_form-upload.scss", "custom/plugins/_form-wizard.scss", "custom/plugins/_datatable.scss", "custom/plugins/_responsive-table.scss", "custom/plugins/_table-editable.scss", "custom/plugins/_apexcharts.scss", "custom/plugins/_echarts.scss", "custom/plugins/_flot.scss", "custom/plugins/_sparkline-chart.scss", "custom/plugins/_google-map.scss", "custom/plugins/_vector-maps.scss", "custom/plugins/_leaflet-maps.scss", "custom/pages/_authentication.scss", "custom/pages/_ecommerce.scss", "custom/pages/_email.scss", "custom/pages/_file-manager.scss", "custom/pages/_chat.scss", "custom/pages/_projects.scss", "custom/pages/_contacts.scss", "custom/pages/_crypto.scss", "custom/pages/_coming-soon.scss", "custom/pages/_timeline.scss", "custom/pages/_extras-pages.scss", "custom/pages/_jobs.scss"], "names": [], "mappings": "AAIQ,8FCAR,aACI,SAAA,MACA,IAAA,EACA,MAAA,EACA,KAAA,EACA,QAAA,KACA,iBAAA,oBACA,mBAAA,EAAA,OAAA,OAAA,mBAAA,WAAA,EAAA,OAAA,OAAA,mBAGJ,eACI,QAAA,YAAA,QAAA,YAAA,QAAA,KACA,cAAA,QACA,iBAAA,QAAA,gBAAA,cACA,kBAAA,OAAA,eAAA,OAAA,YAAA,OACA,OAAA,EAAA,KACA,OAAA,KACA,QAAA,EAAA,gBAAA,EAAA,EAGI,2CACI,iBAAA,sBAKZ,kBACI,QAAA,EAAA,OACA,WAAA,OACA,MAAA,MAGJ,MACI,YAAA,KAEA,eACI,QAAA,KAIR,YACI,QAAA,KAKJ,YACI,QAAA,gBAAA,EAEA,0BACI,OAAA,KACA,OAAA,KACA,aAAA,KACA,cAAA,KACA,iBAAA,2BACA,mBAAA,KAAA,WAAA,KACA,cAAA,KAEJ,iBACI,SAAA,SACA,QAAA,GACA,UAAA,KACA,YAAA,KACA,KAAA,KACA,IAAA,EACA,MAAA,0BAOJ,kBACI,SAAA,SACA,QAAA,IAAA,EACA,oBACI,MAAA,qBAKZ,yBACI,kBACI,MAAA,KAKA,mBACI,QAAA,KAGJ,mBACI,QAAA,cAKZ,cACI,QAAA,kBAAA,iBAAA,KAAA,iBAGJ,aACI,OAAA,KACA,mBAAA,eAAA,WAAA,eACA,MAAA,4BACA,OAAA,EACA,cAAA,EAEA,mBACI,MAAA,4BAIR,qBACI,OAAA,KACA,MAAA,KACA,iBAAA,sBACA,QAAA,IAIA,aACI,UAAA,KACA,MAAA,4BAGJ,kBACI,SAAA,SACA,IAAA,KACA,MAAA,IAKJ,2BACI,QAAA,OAAA,KAEA,iCACI,iBAAA,sBAMZ,oBACI,QAAA,MACA,cAAA,IACA,YAAA,KACA,WAAA,OACA,QAAA,KAAA,EAAA,IACA,QAAA,MACA,OAAA,IAAA,MAAA,YACA,MAAA,0BAEA,wBACI,OAAA,KAGJ,yBACI,QAAA,MACA,SAAA,OACA,cAAA,SACA,YAAA,OAGJ,0BACI,aAAA,uBAOA,sEACI,QAAA,QAMR,oCACI,iBAAA,yBAII,kEACI,iBAAA,sBAIR,kEACI,WAAA,qBAIR,oCACI,MAAA,iCAEA,0CACI,MAAA,iCAIR,4CACI,iBAAA,sBAIA,oCACI,MAAA,iCAIR,kCACI,QAAA,KAGJ,mCACI,QAAA,MAKA,iDACI,iBAAA,qCACA,MAAA,KChCZ,iFDkCQ,wCAEI,MAAA,qBAMR,0CACI,WAAA,QAGJ,mCACI,QAAA,KAGJ,oCACI,QAAA,MAIR,yBAEQ,yBACI,SAAA,OAEA,wCACI,KAAA,eACA,MAAA,gBAMhB,yBACI,kBACI,QAAA,MAKJ,+CACI,MAAA,KAEJ,2CACI,WAAA,KACA,QAAA,kBAAA,gBAAA,KAAA,gBAIR,yBAEQ,2CACI,WAAA,MEzRZ,gBACI,eAAA,KAEA,4BACI,iBAAA,YACA,QAAA,EAGJ,mBACI,eAAA,UACA,YAAA,IACA,UAAA,eCXR,QACI,OAAA,EACA,QAAA,KAAA,iBACA,SAAA,SACA,MAAA,EACA,MAAA,uBACA,KAAA,MACA,OAAA,KACA,iBAAA,oBAEA,4BAVJ,QAWQ,KAAA,GAMJ,2BACI,KAAA,KAEA,4BAHJ,2BAIQ,KAAA,GAMR,qCACI,KAAA,YC5BR,WACI,iBAAA,uBACA,mBAAA,EAAA,EAAA,KAAA,EAAA,eAAA,CAAA,EAAA,IAAA,EAAA,EAAA,gBAAA,WAAA,EAAA,EAAA,KAAA,EAAA,eAAA,CAAA,EAAA,IAAA,EAAA,EAAA,gBACA,QAAA,MACA,SAAA,MACA,mBAAA,IAAA,IAAA,SAAA,WAAA,IAAA,IAAA,SACA,MAAA,MACA,QAAA,KACA,MAAA,gBACA,MAAA,OACA,IAAA,EACA,OAAA,EAEA,6BACI,iBAAA,eACA,OAAA,KACA,MAAA,KACA,YAAA,KACA,QAAA,MACA,MAAA,mBACA,WAAA,OACA,cAAA,IAEA,mCACI,iBAAA,eAMZ,kBACI,iBAAA,mBACA,SAAA,SACA,KAAA,EACA,MAAA,EACA,IAAA,EACA,OAAA,EACA,QAAA,KACA,QAAA,KACA,mBAAA,IAAA,IAAA,SAAA,WAAA,IAAA,IAAA,SAIA,8BACI,MAAA,EAEJ,qCACI,QAAA,MCyBJ,4BDpBA,WACI,SAAA,KACA,4BACI,OAAA,gBEvDZ,WACI,OAAA,EAEA,cACI,QAAA,MACA,MAAA,KAGJ,wBACI,QAAA,KAEA,sCACI,QAAA,KAGJ,gCACI,QAAA,MAIR,0BACI,SAAA,SACA,OAAA,EACA,SAAA,OACA,mCAAA,KAAA,2BAAA,KACA,4BAAA,KAAA,oBAAA,KACA,4BAAA,MAAA,CAAA,WAAA,oBAAA,MAAA,CAAA,WAKR,eACI,MAAA,MACA,QAAA,KACA,WAAA,KACA,OAAA,EACA,WAAA,EACA,SAAA,MACA,IAAA,KACA,mBAAA,EAAA,OAAA,OAAA,mBAAA,WAAA,EAAA,OAAA,OAAA,mBAGJ,cACI,YAAA,MACA,SAAA,OAEA,uBACI,QAAA,EAAA,KAAA,KAAA,KACA,WAAA,KAKR,cACI,QAAA,KAAA,EAAA,KAAA,EAIQ,0CACI,kBAAA,gBAAA,UAAA,gBAMR,+BACI,QAAA,SACA,YAAA,wBACA,QAAA,MACA,MAAA,MACA,mBAAA,kBAAA,IAAA,WAAA,kBAAA,IAAA,WAAA,UAAA,IAAA,WAAA,UAAA,GAAA,CAAA,kBAAA,IACA,UAAA,KAMA,sBACI,QAAA,MACA,QAAA,QAAA,OACA,MAAA,QACA,SAAA,SACA,UAAA,KACA,mBAAA,IAAA,IAAA,WAAA,IAAA,IAEA,wBACI,QAAA,aACA,UAAA,QACA,eAAA,OACA,UAAA,QACA,YAAA,WACA,eAAA,OACA,MAAA,QACA,mBAAA,IAAA,IAAA,WAAA,IAAA,IAGJ,4BACI,MAAA,QAEA,8BACI,MAAA,QAKZ,2BACI,WAAA,IAGJ,gCACI,QAAA,EAII,qCACI,QAAA,MAAA,OAAA,MAAA,OACA,UAAA,KACA,MAAA,QACA,2CACI,MAAA,QAIR,+CACI,QAAA,EAGI,oDACI,QAAA,MAAA,OAAA,MAAA,OACA,UAAA,KAahC,YACI,QAAA,KAAA,eACA,eAAA,MACA,eAAA,KACA,OAAA,QACA,UAAA,KACA,eAAA,UACA,MAAA,QACA,YAAA,IAGJ,WACI,MAAA,kBACA,aACI,MAAA,kBACA,eACI,MAAA,kBAGR,mBACI,MAAA,kBAEA,qBACI,MAAA,kBAGR,aACI,MAAA,kBAIR,yBACI,eACI,QAAA,KAGJ,cACI,YAAA,YAIA,mCACI,QAAA,OAQR,iCACI,YAAA,KAGJ,qCACI,MAAA,eAIA,sCACI,QAAA,KAGJ,sCACI,QAAA,MAKR,kCACI,SAAA,SACA,MAAA,eACA,QAAA,ELsSR,6DKpSQ,kDAEI,SAAA,kBAGJ,uDACI,QAAA,eAGJ,oDACI,OAAA,YLoSZ,uDACA,6DK/RY,4DAGI,QAAA,eAGJ,8DACI,OAAA,kBAIA,iEACI,QAAA,KAKJ,sDACI,SAAA,SACA,YAAA,OAEA,wDACI,QAAA,KAAA,KACA,WAAA,KACA,mBAAA,KAAA,WAAA,KAEA,+DAAA,8DAAA,8DAGI,MAAA,QAGJ,0DACI,UAAA,QACA,YAAA,IAGJ,6DACI,QAAA,KACA,aAAA,KAKJ,8DACI,SAAA,SACA,MAAA,mBACA,MAAA,QACA,iBAAA,QACA,mBAAA,KAAA,WAAA,KAEA,gEACI,MAAA,QAGJ,mEACI,QAAA,OAIR,+DACI,QAAA,MACA,KAAA,KACA,SAAA,SACA,MAAA,MACA,OAAA,eACA,mBAAA,IAAA,IAAA,KAAA,EAAA,kBAAA,WAAA,IAAA,IAAA,KAAA,EAAA,kBAEA,kEACI,mBAAA,IAAA,IAAA,KAAA,EAAA,kBAAA,WAAA,IAAA,IAAA,KAAA,EAAA,kBAGJ,iEACI,mBAAA,KAAA,WAAA,KACA,QAAA,IAAA,KACA,SAAA,SACA,MAAA,MACA,QAAA,EACA,MAAA,QAEA,uEACI,MAAA,QAOpB,sDACI,QAAA,IAAA,EACA,QAAA,KACA,QAAA,KACA,iBAAA,KAIQ,kEACI,QAAA,MACA,KAAA,MACA,OAAA,eACA,WAAA,MACA,SAAA,SACA,MAAA,MAKJ,2EACI,SAAA,SACA,MAAA,KACA,IAAA,KACA,kBAAA,eAAA,UAAA,eAMR,kEACI,MAAA,QAYxB,uCACI,WAAA,QAOQ,8CACI,MAAA,QAEA,gDACI,MAAA,QAGJ,oDACI,MAAA,KAEA,sDACI,MAAA,KAQJ,6DACI,MAAA,QAEA,mEACI,MAAA,KAS5B,0CACI,WAAA,OAWoB,qFACI,WAAA,QACA,MAAA,KACA,uFACI,MAAA,KAKJ,wFACI,MAAA,QACA,8FACI,MAAA,KAOpB,6EACI,iBAAA,QAQA,+FACI,MAAA,eACA,iGACI,MAAA,eAW5B,mCACI,MAAA,eACA,qCACI,MAAA,eACA,uCACI,MAAA,eAGR,qCACI,MAAA,eAEJ,2CACI,MAAA,eAEA,6CACI,MAAA,eAKZ,oCACI,MAAA,QAMJ,2CACI,YAAA,YAOJ,gDACI,MAAA,MAEJ,6CACI,MAAA,MACA,WAAA,OLqMR,oDKnMQ,8DAEI,QAAA,eAGR,4CACI,YAAA,MAEJ,sCACI,KAAA,MACA,4BAFJ,sCAGQ,KAAA,GAMA,6DACI,iBAAA,QAGA,sDACI,QAAA,MAKA,mEACI,aAAA,OAGJ,kFACI,aAAA,OAOhB,8DACI,YAAA,KAGA,6EACI,WAAA,KAIY,uFACI,QAAA,aAOxB,wDACI,KAAA,KAQR,0CACI,iBAAA,QAEJ,6CACI,iBAAA,QACA,wDACI,QAAA,KAEJ,yDACI,QAAA,MAIR,sCACI,MAAA,eACA,wCACI,MAAA,eACA,0CACI,MAAA,eAGR,8CAAA,wCACI,MAAA,eAOI,0DACI,MAAA,qBAGJ,iDACI,MAAA,qBACA,mDACI,MAAA,qBAGA,4EACE,WAAA,qBAIN,uDACI,MAAA,KAEA,yDACI,MAAA,KAOJ,gEACI,MAAA,qBACA,sEACI,MAAA,KAcR,wFACI,iBAAA,QACA,MAAA,KACA,0FACI,MAAA,KASJ,kGACI,MAAA,kBAOI,uGACI,MAAA,QAGR,yGACI,MAAA,kBACA,2GACI,MAAA,kBACA,6GACI,MAAA,kBCzpB5C,QACI,WAAA,oBACA,QAAA,EAAA,gBACA,mBAAA,EAAA,OAAA,OAAA,mBAAA,WAAA,EAAA,OAAA,OAAA,mBACA,WAAA,KACA,SAAA,MACA,KAAA,EACA,MAAA,EACA,QAAA,IAEA,qBACI,OAAA,EACA,QAAA,EAKA,8BACI,UAAA,KACA,SAAA,SACA,QAAA,KAAA,OACA,MAAA,0BACA,gCACI,UAAA,KAEJ,oCAAA,oCACI,MAAA,iCACA,iBAAA,YAIR,mCACI,MAAA,0BACA,0CAAA,yCACI,MAAA,iCAKJ,+CACI,MAAA,iCAMF,uCACM,MAAA,iCACA,iBAAA,YFWhB,0BEDI,8CNqxBN,4CMnxBU,UAAA,KFDR,yBEWgB,sDACI,aAAA,EAMhB,uBACI,QAAA,MAAA,OACA,UAAA,MAMI,oDACI,KAAA,EACA,MAAA,KAGR,iCACI,WAAA,EACA,cAAA,EAAA,EAAA,wBAAA,wBAGI,oDACI,MAAA,KACA,kBAAA,gBAAA,iBAAA,UAAA,gBAAA,iBACA,SAAA,SAKJ,0DACI,SAAA,SACA,IAAA,YACA,KAAA,KACA,QAAA,KAMR,uCACI,QAAA,MAKZ,sEACI,QAAA,MAIR,eACI,QAAA,MAIR,YACI,QAAA,aAEA,kBACI,aAAA,QACA,aAAA,MACA,aAAA,EAAA,EAAA,IAAA,IACA,QAAA,GACA,OAAA,KACA,QAAA,aACA,MAAA,IACA,IAAA,IACA,YAAA,KACA,kBAAA,eAAA,iBAAA,UAAA,eAAA,iBACA,yBAAA,IAAA,iBAAA,IACA,mBAAA,IAAA,IAAA,SAAA,WAAA,IAAA,IAAA,SACA,MAAA,KFzEJ,6BEoFoB,kEACI,MAAA,KACA,KAAA,MFtFxB,4BEkGI,6BACI,QAAA,wBACA,0CACI,QAAA,wBAIR,8BACI,QAAA,uBAIR,QACI,WAAA,MACA,WAAA,KACA,QAAA,EAEI,8BACI,QAAA,OAAA,OAKJ,iCACI,iBAAA,YACA,OAAA,KACA,mBAAA,KAAA,WAAA,KACA,aAAA,KACA,uDACI,MAAA,KAEA,4DACI,OAAA,EAKZ,iCACI,SAAA,SACA,iBAAA,YAEA,wCAAA,wCAEI,MAAA,QAMR,2BACI,MAAA,KACA,SAAA,UFlKZ,yBE6KQ,6EACI,QAAA,wBAGJ,8EACI,QAAA,uBAIR,wDACI,iBAAA,QAGI,8EACI,MAAA,qBAEA,oFAAA,oFACI,MAAA,qBAMJ,uFACQ,MAAA,gCAYxB,+DACI,iBAAA,QACA,mBAAA,KAAA,WAAA,KAGJ,6DACI,QAAA,KAGJ,8DACI,QAAA,MAKA,4EACI,iBAAA,qCACA,MAAA,KNusBZ,4GMrsBQ,mEAEI,MAAA,qBAGR,+DACI,MAAA,iCAEA,qEACI,MAAA,iCAMA,6FACI,iBAAA,qBAIR,6FACI,WAAA,qBAKJ,+DACI,MAAA,iCF/PR,yBEoQI,0DACI,iBAAA,QAGI,gFACI,MAAA,qBAEA,sFAAA,sFACI,MAAA,qBAMF,yFACM,MAAA,gCC9U5B,6BACI,iBAAA,wBACA,6CACI,iBAAA,kBACA,UAAA,OACA,OAAA,EAAA,KACA,mBAAA,EAAA,OAAA,OAAA,mBAAA,WAAA,EAAA,OAAA,OAAA,mBAGJ,0CACI,UAAA,OACA,OAAA,EAAA,KAGJ,qCACI,OAAA,EAAA,KACA,UAAA,qBAIA,uDACI,UAAA,oBASR,qEAAA,kEAAA,6DACI,UAAA,KAEJ,sEAAA,oEACI,UAAA,OAOJ,yBACI,+CAAA,iDACI,SAAA,UAKJ,yBACI,uEAAA,kEACI,SAAA,UCrDhB;;;;;;AAOC,cACG,SAAA,SACA,OAAA,QACA,QAAA,aACA,SAAA,OACA,oBAAA,KACA,iBAAA,KACA,gBAAA,KACA,YAAA,KACA,4BAAA,YAEF,4BACE,SAAA,SACA,cAAA,IACA,MAAA,MACA,OAAA,MACA,WAAA,MACA,YAAA,MACA,QAAA,EACA,WAAA,eAIA,WAAA,mHACA,mBAAA,IAAA,IAAA,SAGA,WAAA,IAAA,IAAA,SACA,4BAAA,iBAAA,CAAA,QAGA,4BAAA,OAAA,CAAA,kBAAA,oBAAA,OAAA,CAAA,kBAAA,oBAAA,SAAA,CAAA,QAAA,oBAAA,SAAA,CAAA,OAAA,CAAA,kBACA,kBAAA,SAAA,eAIA,UAAA,SAAA,eACA,eAAA,KAEF,wCACE,WAAA,qBAIA,WAAA,2IAEF,0CACE,WAAA,eAEF,sDACE,WAAA,qBAEF,oBACE,mBAAA,eAGA,WAAA,eAEF,cR2iCF,cQziCI,kBAAA,cAIA,UAAA,cACA,mBAAA,oDAEF,cR2iCF,oBAFA,oBACA,sBQtiCI,YAAA,OACA,eAAA,OACA,OAAA,QACA,OAAA,KACA,QAAA,EACA,MAAA,QACA,iBAAA,cACA,UAAA,IACA,YAAA,IACA,WAAA,OACA,gBAAA,KACA,QAAA,EAEF,cACE,QAAA,MAAA,MACA,cAAA,KAEF,oBACE,OAAA,EACA,QAAA,MAAA,MAEF,qBACE,cAAA,KACA,eAAA,OAEF,kCACE,QAAA,EAEF,yCACE,SAAA,SACA,IAAA,EACA,KAAA,EACA,QAAA,EAEF,cACE,WAAA,OACA,MAAA,MACA,OAAA,MACA,YAAA,MACA,cAAA,IAEF,aACE,mBAAA,KACA,mBAAA,EAAA,IAAA,MAAA,IAAA,gBACA,WAAA,EAAA,IAAA,MAAA,IAAA,gBACA,mBAAA,IAAA,IAGA,WAAA,IAAA,IAEF,oBACE,mBAAA,EAAA,IAAA,KAAA,IAAA,eACA,WAAA,EAAA,IAAA,KAAA,IAAA,eAEF,aACE,QAAA,MAIA,wCACI,iBAAA,qBAKJ,0CACI,iBAAA,oBAIJ,0CACI,iBAAA,oBAIJ,uCACI,iBAAA,oBAIJ,0CACI,iBAAA,oBAIJ,yCACI,iBAAA,qBCjKR,WACE,OAAA,KACA,MAAA,KAGF,WACE,OAAA,KACA,MAAA,KAGF,WACE,OAAA,OACA,MAAA,OAGF,WACE,OAAA,KACA,MAAA,KAGF,WACE,OAAA,OACA,MAAA,OAGF,cACE,kBAAA,OAAA,eAAA,OAAA,YAAA,OACA,iBAAA,QACA,MAAA,KACA,QAAA,YAAA,QAAA,YAAA,QAAA,KACA,YAAA,IACA,OAAA,KACA,iBAAA,OAAA,cAAA,OAAA,gBAAA,OACA,MAAA,KAIF,cACE,aAAA,KACA,QAAA,YAAA,QAAA,YAAA,QAAA,KACA,cAAA,KAAA,UAAA,KACA,iCACE,YAAA,MACA,OAAA,IAAA,MAAA,uBACA,cAAA,IACA,mBAAA,IAAA,IAAA,WAAA,IAAA,IACA,uCACE,SAAA,SACA,kBAAA,iBAAA,UAAA,iBC9CF,kCACI,QAAA,YAAA,QAAA,YAAA,QAAA,KACA,cAAA,IACA,iBAAA,sBACA,QAAA,KAAA,KACA,MAAA,qBACA,YAAA,IACA,kBAAA,OAAA,eAAA,OAAA,YAAA,OACA,iBAAA,QAAA,cAAA,QAAA,gBAAA,cAGQ,qEACI,QAAA,SAKZ,mDACI,QAAA,aACA,UAAA,KACA,OAAA,KACA,MAAA,KACA,YAAA,KACA,iBAAA,uBACA,WAAA,OACA,cAAA,IASI,uDACI,QAAA,SAOhB,6BACI,MAAA,6BC7CR,cACI,UAAA,eAGJ,cACI,UAAA,eAGJ,cACI,UAAA,eAGJ,cACI,UAAA,eAGJ,cACI,UAAA,eAGJ,cACI,UAAA,eAGJ,cACI,UAAA,eAGJ,cACI,UAAA,eAGJ,cACI,UAAA,eAGJ,cACI,UAAA,eAGJ,cACI,UAAA,eAGJ,cACI,UAAA,eAIJ,kBACI,OAAA,KACA,MAAA,KACA,YAAA,iBACA,QAAA,MACA,OAAA,IAAA,MAAA,QACA,cAAA,IACA,MAAA,QACA,WAAA,OACA,mBAAA,IAAA,IAAA,WAAA,IAAA,IAEA,wBACI,MAAA,0BACA,iBAAA,QAKR,MACI,UAAA,KAGJ,MACI,UAAA,KAGJ,MACI,UAAA,MAGJ,MACI,UAAA,MAGJ,MACI,UAAA,MAOA,8BACI,UAAA,KACA,QAAA,QAAA,QAIR,eACI,WAAA,MClGJ,WACI,SAAA,MACA,IAAA,EACA,KAAA,EACA,MAAA,EACA,OAAA,EACA,iBAAA,uBACA,QAAA,KAGJ,QACI,MAAA,KACA,OAAA,KACA,SAAA,SACA,KAAA,IACA,IAAA,IACA,OAAA,MAAA,EAAA,EAAA,MAGJ,eACI,OAAA,EAAA,KACA,MAAA,KACA,OAAA,KACA,SAAA,SACA,kBAAA,cAAA,KAAA,SAAA,OAAA,KAAA,UAAA,cAAA,KAAA,SAAA,OAAA,KAGJ,WACI,MAAA,KACA,OAAA,KACA,SAAA,SACA,KAAA,EACA,IAAA,EACA,kBAAA,UAAA,GAAA,SAAA,YAAA,KAAA,UAAA,UAAA,GAAA,SAAA,YAAA,KACA,kBACI,QAAA,GACA,QAAA,MACA,MAAA,IACA,OAAA,IACA,iBAAA,QACA,cAAA,KACA,kBAAA,iBAAA,GAAA,SAAA,YAAA,KAAA,UAAA,iBAAA,GAAA,SAAA,YAAA,KAGJ,wBACI,wBAAA,MAAA,gBAAA,MACA,+BACI,wBAAA,MAAA,gBAAA,MAGR,wBACI,wBAAA,IAAA,gBAAA,IACA,+BACI,wBAAA,IAAA,gBAAA,IAGR,wBACI,wBAAA,KAAA,gBAAA,KACA,+BACI,wBAAA,KAAA,gBAAA,KAGR,wBACI,wBAAA,KAAA,gBAAA,KACA,+BACI,wBAAA,KAAA,gBAAA,KAGR,wBACI,wBAAA,KAAA,gBAAA,KACA,+BACI,wBAAA,KAAA,gBAAA,KAGR,wBACI,wBAAA,KAAA,gBAAA,KACA,+BACI,wBAAA,KAAA,gBAAA,KAKZ,iCACI,KACI,kBAAA,eAAA,UAAA,gBAFR,yBACI,KACI,kBAAA,eAAA,UAAA,gBAIR,6BACI,KAAA,IACI,kBAAA,eAAA,UAAA,gBAFR,qBACI,KAAA,IACI,kBAAA,eAAA,UAAA,gBAIR,oCACI,IACI,kBAAA,UAAA,UAAA,UAEJ,GAAA,KACI,kBAAA,SAAA,UAAA,UALR,4BACI,IACI,kBAAA,UAAA,UAAA,UAEJ,GAAA,KACI,kBAAA,SAAA,UAAA,UC/FN,wCAAA,yCAAA,sCAAA,sCACE,WAAA,KADF,+BAAA,gCAAA,6BAAA,6BACE,WAAA,KADF,mCAAA,oCAAA,iCAAA,iCACE,WAAA,KADF,oCAAA,qCAAA,kCAAA,kCACE,WAAA,KbmiDJ,0BACA,2BariDE,wBbmiDF,wBaliDI,WAAA,KAMJ,YACE,SAAA,SACA,WAAA,KAQF,kBACE,aAAA,EACA,QAAA,aACA,cAAA,MAEA,oCACE,MAAA,MACA,YAAA,EACA,aAAA,OAGF,oCACE,QAAA,MAUF,yCACE,aAAA,IACA,iBAAA,uBAEA,gDACE,eAAA,KAAA,OAAA,KAGF,iDACE,iBAAA,iCAEA,gEACE,iBAAA,KAIF,uDACE,SAAA,SACA,QAAA,SACA,YAAA,wBACA,IAAA,eACA,KAAA,IAEA,UAAA,KACA,MAAA,qBASN,sCACE,iBAAA,uBACA,SAAA,SAEA,6CACE,eAAA,KAAA,OAAA,KAGF,8CACE,iBAAA,iCAEA,6DACE,iBAAA,KAIF,oDACE,SAAA,SACA,QAAA,GACA,IAAA,cACA,KAAA,IACA,MAAA,IACA,OAAA,IACA,cAAA,IAcF,8CACE,iBAAA,QACA,aAAA,QAOF,8CACE,aAAA,QACA,iBAAA,QAEA,oDACE,iBAAA,QAdJ,gDACE,iBAAA,QACA,aAAA,QAOF,gDACE,aAAA,QACA,iBAAA,QAEA,sDACE,iBAAA,QAdJ,8CACE,iBAAA,QACA,aAAA,QAOF,8CACE,aAAA,QACA,iBAAA,QAEA,oDACE,iBAAA,QAdJ,2CACE,iBAAA,QACA,aAAA,QAOF,2CACE,aAAA,QACA,iBAAA,QAEA,iDACE,iBAAA,QAdJ,8CACE,iBAAA,QACA,aAAA,QAOF,8CACE,aAAA,QACA,iBAAA,QAEA,oDACE,iBAAA,QAdJ,6CACE,iBAAA,QACA,aAAA,QAOF,6CACE,aAAA,QACA,iBAAA,QAEA,mDACE,iBAAA,QAdJ,2CACE,iBAAA,QACA,aAAA,QAOF,2CACE,aAAA,QACA,iBAAA,QAEA,iDACE,iBAAA,QAdJ,4CACE,iBAAA,QACA,aAAA,QAOF,4CACE,aAAA,QACA,iBAAA,QAEA,kDACE,iBAAA,QAdJ,2CACE,iBAAA,QACA,aAAA,QAOF,2CACE,aAAA,QACA,iBAAA,QAEA,iDACE,iBAAA,QAOV,YbimDA,kBACA,kBa/lDE,OAAA,QACA,cAAA,EAKF,gBACE,aAAA,OACA,WAAA,KACA,YAAA,KAEA,kCACE,MAAA,KACA,OAAA,KACA,KAAA,OACA,SAAA,SAGF,kCACE,eAAA,OAKJ,gBACE,aAAA,QACA,WAAA,KACA,YAAA,KAEA,kCACE,MAAA,KACA,OAAA,KACA,KAAA,QACA,SAAA,SAIJ,kBACE,cAAA,EC9KE,gCACI,SAAA,OACA,SAAA,SACA,sCAAA,uCACI,QAAA,GACA,SAAA,SACA,MAAA,IACA,OAAA,KACA,iBAAA,qBACA,KAAA,KACA,kBAAA,cAAA,UAAA,cACA,IAAA,KACA,mBAAA,IAAA,IAAA,WAAA,IAAA,IAGJ,uCACI,KAAA,MACA,MAAA,KACA,mBAAA,IAAA,IAAA,WAAA,IAAA,IAMA,6CACI,KAAA,KCxBhB,gBACI,UAAA,OAEJ,WACE,MAAA,+BAKF,kBACI,SAAA,SACA,IAAA,KACA,MAAA,KACA,OAAA,KACA,KAAA,KACA,QAAA,EACA,QAAA,MAIF,sBACE,aAAA,YAKJ,mBACI,WAAA,OACA,MAAA,QAEA,qBACE,QAAA,MACA,UAAA,KACA,cAAA,KACA,MAAA,6BACA,mBAAA,IAAA,IAAA,WAAA,IAAA,IAGF,6BACE,WAAA,KAGE,qCACE,MAAA,QACA,kBAAA,WAAA,UAAA,WAUN,gCACI,iBAAA,mBACA,WAAA,KACA,UAAA,MACA,YAAA,IACA,QAAA,KAAA,KAOR,YACE,iBAAA,uBACA,OAAA,IAAA,MAAA,uBACA,cAAA,wBACA,QAAA,KACA,SAAA,OACA,cAAA,SACA,YAAA,OAEA,kBACE,OAAA,QAIJ,kBACE,QAAA,MAIF,kBACE,QAAA,KACA,sCACE,aAAA,kBAKF,wBACI,WAAA,KACA,+BACI,MAAA,KACA,OAAA,KACA,YAAA,eACA,UAAA,eACA,cAAA,cACA,iBAAA,+BACA,MAAA,kBACA,OAAA,IAAA,cCxGV,ahBo4DE,QADA,eADA,gBADA,WgBh4DE,eAKI,QAAA,eAEJ,WhBg4DF,cAEA,cADA,WAEA,KgB93DM,QAAA,EACA,OAAA,EAGJ,MACI,OAAA,GCvBR,iBACE,SAAA,SACA,mBAAA,SAAA,sBAAA,OAAA,mBAAA,OAAA,eAAA,OACA,cAAA,KAAA,UAAA,KACA,iBAAA,MAAA,cAAA,MAAA,gBAAA,WACA,mBAAA,MAAA,cAAA,WACA,kBAAA,MAAA,eAAA,MAAA,YAAA,WAGF,mBACE,SAAA,OACA,MAAA,QACA,OAAA,QACA,UAAA,QACA,WAAA,QAGF,gBACE,UAAA,QACA,SAAA,SACA,SAAA,OACA,QAAA,EACA,OAAA,EACA,KAAA,EACA,IAAA,EACA,OAAA,EACA,MAAA,EACA,MAAA,eACA,OAAA,eACA,QAAA,EAGF,kBACE,UAAA,kBACA,mBAAA,kBAAA,WAAA,kBACA,OAAA,eACA,SAAA,SACA,IAAA,EACA,KAAA,YACA,OAAA,EACA,MAAA,YACA,QAAA,EACA,OAAA,EACA,2BAAA,MAGF,2BACE,UAAA,QACA,mBAAA,qBAAA,WAAA,qBACA,SAAA,SACA,QAAA,MACA,OAAA,KACA,MAAA,KACA,WAAA,QACA,SAAA,KACA,UAAA,KACA,WAAA,KACA,gBAAA,KACA,QAAA,YAGF,8CjBo6DA,6CiBl6DE,QAAA,KjBu6DF,yBiBp6DA,0BAEE,QAAA,IACA,QAAA,MAGF,uBACE,WAAA,KACA,UAAA,KACA,MAAA,KACA,eAAA,KAGF,wCACE,mBAAA,kBAAA,WAAA,kBACA,OAAA,KACA,MAAA,KACA,UAAA,IACA,SAAA,SACA,MAAA,KACA,WAAA,IACA,SAAA,OACA,QAAA,GACA,QAAA,EACA,OAAA,EACA,eAAA,KACA,iBAAA,QAAA,kBAAA,QAAA,UAAA,QACA,kBAAA,EAAA,YAAA,EACA,wBAAA,EAAA,WAAA,EAGF,gCACE,mBAAA,QAAA,WAAA,QACA,QAAA,MACA,QAAA,EACA,SAAA,SACA,IAAA,EACA,KAAA,EACA,OAAA,MACA,MAAA,MACA,WAAA,IACA,UAAA,IACA,SAAA,OACA,eAAA,KACA,QAAA,GAGF,iBACE,QAAA,EACA,SAAA,SACA,MAAA,EACA,OAAA,EACA,eAAA,KACA,SAAA,OAGF,uDACE,eAAA,KACA,iBAAA,KAAA,gBAAA,KAAA,YAAA,KACA,oBAAA,KAGF,qDACE,eAAA,IAGF,qBACE,SAAA,SACA,MAAA,IACA,MAAA,IACA,WAAA,KAGF,4BACE,SAAA,SACA,QAAA,GACA,WAAA,QACA,cAAA,IACA,KAAA,EACA,MAAA,EACA,QAAA,EACA,mBAAA,QAAA,IAAA,OAAA,WAAA,QAAA,IAAA,OAGF,8CAEE,QAAA,GACA,mBAAA,QAAA,GAAA,OAAA,WAAA,QAAA,GAAA,OAGF,oCACE,IAAA,EACA,MAAA,KAGF,gEACE,IAAA,IACA,OAAA,IAGF,sCACE,KAAA,EACA,OAAA,KAGF,kEACE,OAAA,KACA,KAAA,IACA,MAAA,IAGF,2DACE,MAAA,KACA,KAAA,EACA,IAAA,IACA,OAAA,IACA,WAAA,EACA,UAAA,KACA,MAAA,KAIF,mEACE,MAAA,KACA,KAAA,EAGF,yBACE,UAAA,IACA,SAAA,MACA,QAAA,EACA,WAAA,OACA,OAAA,MACA,MAAA,MACA,WAAA,OACA,WAAA,OAGF,0BACE,SAAA,MACA,KAAA,EACA,WAAA,OACA,WAAA,OACA,gBAAA,KAGF,eACE,OAAA,KCjNF,oBACI,QAAA,aACA,aAAA,IAKJ,2DACI,aAAA,IAGA,YAAA,EAKJ,0BlB+nEA,qCkB7nEI,iBAAA,iCAGJ,qClB+nEA,wBACA,4BACA,6BAGA,qCADA,qCADA,qCkB3nEI,aAAA,iCAGJ,0CACI,WAAA,OACA,MAAA,KACA,OAAA,KACA,YAAA,KACA,cAAA,IACA,iBAAA,iCACA,MAAA,qBACA,aAAA,uBAGJ,iCACI,MAAA,+BAGJ,0CACI,MAAA,mCAEA,2EACI,YAAA,IAIR,mCACI,iBAAA,iCACA,aAAA,iCAKI,qEACI,aAAA,uBAMR,4CAAA,4CACI,aAAA,uBAIR,oEACI,iBAAA,iCAGJ,iEACI,oBAAA,iCAGJ,qEACI,oBAAA,iCAGJ,0BACI,MAAA,yBACA,iBAAA,iCACA,aAAA,uBAGJ,sCACI,aAAA,uBAGJ,iClBwnEA,kCkBtnEI,iBAAA,iCAGJ,mEACI,oBAAA,iCAGJ,iCACI,aAAA,iClB2nEJ,iCkBxnEA,sCAEI,iBAAA,iCAGJ,sEACI,iBAAA,uBAGJ,2BACI,iBAAA,iCACA,MAAA,mCAGJ,2BACI,iBAAA,kBACA,MAAA,eAEA,iCACI,iBAAA,kBACA,MAAA,eAKJ,2CACI,MAAA,mCAQQ,+KACI,MAAA,oCAWR,kJACI,MAAA,mCC1JhB,OnBywEA,OmBvwEE,OAAA,uBAAA,MAAA,uBAOE,mBACE,UAAA,KACA,YAAA,KACA,eAAA,UAGF,4BnBswEF,2BmBpwEI,yBnBmwEJ,0BmBhwEM,MAAA,KACA,QAAA,MACA,WAAA,OACA,MAAA,KACA,OAAA,KAAA,EAGF,oBACE,MAAA,KAGF,iCACE,QAAA,MAIJ,qBACE,eAAA,WAKJ,gCACE,MAAA,qBAGF,2BACE,MAAA,qBAIJ,sBACE,QAAA,KnB8vEF,gBmB3vEA,gBAEE,SAAA,SACA,QAAA,IAAA,cAME,8CACE,iBAAA,kBACA,aAAA,kBAEA,mBAAA,KAAA,WAAA,KAEA,qDAAA,oDAEE,MAAA,KACA,iBAAA,kBACA,aAAA,kBAKN,mCACE,iBAAA,4BACA,aAAA,4BAMF,uDnBivEF,6CmB/uEI,iBAAA,4BACA,aAAA,4BACA,MAAA,eAKF,4BADF,YAEI,mBAAA,SAAA,sBAAA,OAAA,mBAAA,OAAA,eAAA,OACA,IAAA,MAKF,wBACE,WAAA,QACA,MAAA,QACA,YAAA,KACA,QAAA,KAAA,EACA,eAAA,UACA,YAAA,IAMF,yBnB+uEF,yBACA,iCACA,2BACA,yBACA,qBACA,mBACA,gBACA,gBACA,mBmB7uEI,aAAA,QAGF,yBACE,WAAA,QAIJ,WACE,WAAA,uBACA,aAAA,uBACA,MAAA,QACA,eAAA,WACA,mBAAA,KAAA,WAAA,KACA,QAAA,IAAA,eACA,OAAA,enBivEF,iBACA,mBmB/uEA,eAGE,iBAAA,QACA,MAAA,KACA,YAAA,KAGF,UACE,cAAA,IACA,OAAA,KACA,OAAA,KACA,UAAA,SACA,OAAA,IAAA,IACA,QAAA,IAAA,IACA,WAAA,OACA,MAAA,KAEA,kBAIE,iBAAA,oCAHA,iCACE,MAAA,iCAGF,kCACE,MAAA,iCAKN,UnB6uEA,cmB3uEE,iBAAA,QAGF,sBACE,MAAA,KAIA,iCACE,WAAA,KACA,QAAA,IAAA,KACA,YAAA,EACA,aAAA,EAMF,2EACE,MAAA,QAOF,6BACE,UAAA,cAGF,2CACE,YAAA,MAIJ,kCACE,aAAA,uBAGF,4BACE,iBAAA,kBACA,MAAA,qBAGF,0BACE,QAAA,IACA,UAAA,KACA,YAAA,IAGF,gCACE,iBAAA,+BAGF,uBACE,QAAA,IAAA,KAGF,sCACE,WAAA,kBAGF,+BACI,WAAA,uBACA,OAAA,IAAA,MAAA,uBC5OJ,UAEE,OAAA,IAAA,MAAA,uBAGF,YACI,OAAA,IAAA,OAAA,iCACA,iBAAA,6BCTA,+BACI,QAAA,KAGJ,0CACI,MAAA,QACA,YAAA,IAGJ,qCACI,iBAAA,KACA,MAAA,QACA,mBAAA,KAAA,WAAA,KCbR,KACE,YAAA,0BAGF,aACE,OAAA,QAEA,sBtBg/EF,uBACA,yBAFA,qBsB3+EI,WAAA,kBACA,UAAA,KtBi/EJ,8BACA,gCsB5+EI,4BACE,iBAAA,QAIJ,uBACE,WAAA,sBACA,aAAA,sBAGF,4BACE,UAAA,KACA,MAAA,0BtB4+EJ,sBsBz+EE,sBAEE,MAAA,0BACA,WAAA,sBACA,UAAA,KAGF,yBACE,OAAA,IAAA,MAAA,QACA,MAAA,KACA,OAAA,KACA,IAAA,KACA,iBAAA,iCACA,OAAA,QAEA,gCACE,OAAA,QAIJ,2BACE,iBAAA,0BCrDA,8BACI,UAAA,KACA,YAAA,IAIR,aACI,UAAA,KAIA,2BACI,aAAA,QACA,MAAA,QAGA,sDACI,iBAAA,QAGJ,8CACI,aAAA,oBAGR,0BACI,aAAA,QACA,MAAA,QAKJ,oBACI,mBAAA,KAAA,WAAA,KAKJ,2CACI,WAAA,QACA,sEACI,WAAA,QACA,2FAAA,gGAEI,WAAA,oBAKZ,gDACI,WAAA,QAIR,cACI,aAAA,QAAA,YAAA,QAAA,YAEJ,aACI,WAAA,uBAGJ,sBAAA,aACK,MAAA,yBAGL,YvBohFA,aACA,gBuBlhFI,aAAA,uBACA,MAAA,qBACA,kBvBqhFJ,mBACA,sBuBrhFQ,mBAAA,KAAA,WAAA,KACA,aAAA,uBAIR,oDACI,MAAA,0BC5EJ,QACE,aAAA,uBAGF,0BAAA,0BACE,UAAA,KAGF,cACE,iBAAA,mBAGF,0BACE,IAAA,EAIA,kBACE,QAAA,aACA,eAAA,OAEA,wBACE,YAAA,ICjBF,qBACI,mBAAA,EAAA,OAAA,OAAA,mBAAA,WAAA,EAAA,OAAA,OAAA,mBACA,QAAA,EACA,2BACI,mBAAA,EAAA,OAAA,OAAA,mBAAA,WAAA,EAAA,OAAA,OAAA,mBACA,QAAA,GAKJ,6CAAA,0CACE,UAAA,IACA,OAAA,IAAA,KAQN,eACI,OAAA,IAAA,MAAA,kBACA,iBAAA,8BAFJ,iBACI,OAAA,IAAA,MAAA,kBACA,iBAAA,+BAFJ,eACI,OAAA,IAAA,MAAA,kBACA,iBAAA,8BAFJ,YACI,OAAA,IAAA,MAAA,kBACA,iBAAA,8BAFJ,eACI,OAAA,IAAA,MAAA,kBACA,iBAAA,8BAFJ,cACI,OAAA,IAAA,MAAA,kBACA,iBAAA,+BAFJ,YACI,OAAA,IAAA,MAAA,kBACA,iBAAA,8BAFJ,aACI,OAAA,IAAA,MAAA,kBACA,iBAAA,+BAFJ,YACI,OAAA,IAAA,MAAA,kBACA,iBAAA,4BAOR,aACI,iBAAA,qBACA,OAAA,IAAA,MAAA,QAGJ,gBACI,QAAA,KACA,iBAAA,sBACA,cAAA,EACA,OAAA,IAAA,MAAA,uBC3CJ,OACE,MAAA,QAGF,eACE,aAAA,QAGF,qBACE,QAAA,KACA,OAAA,EACA,QAAA,EACA,4BACE,QAAA,MAEF,wBACE,UAAA,KACA,WAAA,KACA,MAAA,QACA,WAAA,ICnBA,8CACI,iBAAA,uBACA,OAAA,IAAA,MAAA,mCACA,OAAA,KAEA,oDACI,QAAA,EAGJ,2EACI,YAAA,KACA,aAAA,OACA,MAAA,0BAIJ,wEACI,OAAA,KACA,MAAA,KACA,MAAA,IAEA,0EACI,aAAA,mBAAA,YAAA,YAAA,YACA,aAAA,IAAA,IAAA,EAAA,IAIR,8EACI,MAAA,qBAMR,uCACI,WAAA,IAQI,gFACI,aAAA,YAAA,YAAA,mBAAA,sBACA,aAAA,EAAA,IAAA,IAAA,cAOZ,sDACI,QAAA,KACA,iBAAA,uBAEA,6EACI,OAAA,IAAA,MAAA,mCACA,iBAAA,uBACA,MAAA,0BACA,QAAA,EAIR,iFACI,iBAAA,QAGJ,yEACI,iBAAA,gBACA,MAAA,yBAEA,+EACI,iBAAA,QACA,MAAA,KAKJ,4FACI,aAAA,KACA,MAAA,yBAKZ,yBACI,QAAA,IAAA,KAGJ,oFACI,aAAA,OAGJ,kBACI,OAAA,IAAA,MAAA,uBACA,iBAAA,uBACA,mBAAA,EAAA,OAAA,OAAA,mBAAA,WAAA,EAAA,OAAA,OAAA,mBAIA,sBACI,OAAA,IAAA,MAAA,uBAKJ,gDACI,WAAA,KACA,iBAAA,uBACA,OAAA,IAAA,MAAA,6CACA,QAAA,IAAA,OAEA,6EACI,QAAA,IAAA,OAGJ,uEACI,OAAA,EACA,MAAA,yBACA,OAAA,EACA,WAAA,IAEA,kGACI,MAAA,0BADJ,yFACI,MAAA,0BADJ,6FACI,MAAA,0BADJ,8FACI,MAAA,0BADJ,oFACI,MAAA,0BAIR,2EACI,iBAAA,sBACA,OAAA,IAAA,MAAA,uBACA,cAAA,IACA,QAAA,EAAA,IAGJ,6EACI,QAAA,EAOJ,kFACI,aAAA,mCAIR,oDACI,YAAA,IAIA,6FACI,aAAA,KAOZ,mCACI,MAAA,KACA,MAAA,KACA,aAAA,KAEA,uCACI,MAAA,KACA,OAAA,KACA,cAAA,IAIR,uCACI,WAAA,IAGJ,kC3BgsFA,uCACA,qC2B9rFI,QAAA,aACA,UAAA,KACA,aAAA,IACA,MAAA,0BAEA,sC3BgsFJ,2CACA,yC2BhsFQ,aAAA,IAGI,uD3BisFZ,4DACA,0D2BjsFgB,QAAA,QACA,YAAA,sBAQZ,wE3B8rFJ,6EACA,2E2B5rFQ,MAAA,qBAIR,iCACI,SAAA,OAKJ,UACI,aAAA,IACA,OAAA,KACA,MAAA,KAIJ,+DACI,iBAAA,sBC5NJ,cACE,QAAA,KACA,oBACE,UAAA,IACA,YAAA,EACA,MAAA,KACA,OAAA,KACA,iBAAA,QACA,iBAAA,KACA,cAAA,KACA,QAAA,UACA,OAAA,QACA,QAAA,aACA,WAAA,OACA,SAAA,SACA,YAAA,IACA,mBAAA,IAAA,IAAA,YAAA,WAAA,IAAA,IAAA,YACA,2BACE,MAAA,QACA,QAAA,qBACA,QAAA,MACA,YAAA,QACA,YAAA,IACA,UAAA,KACA,YAAA,KACA,SAAA,SACA,MAAA,IACA,OAAA,IACA,IAAA,KACA,WAAA,OACA,UAAA,WACA,SAAA,OACA,mBAAA,IAAA,IAAA,YAAA,WAAA,IAAA,IAAA,YAGF,0BACE,QAAA,GACA,SAAA,SACA,KAAA,IACA,iBAAA,QACA,mBAAA,KAAA,WAAA,KACA,cAAA,KACA,OAAA,KACA,MAAA,KACA,IAAA,IACA,mBAAA,IAAA,IAAA,YAAA,WAAA,IAAA,IAAA,YAIJ,4BACE,iBAAA,QAIJ,4BACE,iBAAA,QACA,mCACE,MAAA,KACA,QAAA,oBACA,MAAA,KACA,KAAA,IAGF,kCACE,KAAA,KACA,iBAAA,QAIJ,yBACE,iBAAA,QAEF,gCAAA,wC5B45FA,2C4B15FE,MAAA,KAGF,iCACE,iBAAA,QAGF,oCACE,iBAAA,QAGF,oCACE,iBAAA,QAGF,oCACE,iBAAA,QAGF,iCACE,iBAAA,QAGF,oCACE,iBAAA,QAGF,mCACE,iBAAA,QAGF,iCACE,iBAAA,QACA,wCACE,MAAA,QAIJ,eACE,aAAA,IACA,mCAAA,yCACE,cAAA,ICnHJ,cACE,iBAAA,uBACA,qBACE,QAAA,OAAA,MACE,UAAA,UACA,cAAA,MACA,YAAA,IACA,MAAA,QAEA,uCACE,iBAAA,QAGF,+BACE,iBAAA,QACA,YAAA,IACA,aAAA,EAKR,sBACE,aAAA,IAAA,MAAA,QAGF,UACE,iBAAA,uBACA,aAAA,6CACA,MAAA,yBACA,gBACE,QAAA,EAOF,oBACE,UAAA,IAIA,kDACE,wBAAA,YACA,2BAAA,YACA,uBAAA,cACA,0BAAA,cAIJ,qCACE,OAAA,IAAA,MAAA,mCACA,YAAA,EACA,uBAAA,EACA,0BAAA,EACA,wBAAA,wBACA,2BAAA,wBCvDE,wCACE,MAAA,yBACA,8CACE,iBAAA,YACA,aAAA,YACA,cAAA,IACA,MAAA,QACA,gBAAA,KAGJ,4CACE,MAAA,KACA,OAAA,KACA,OAAA,EACA,MAAA,qBACA,OAAA,IAAA,MAAA,uBACA,iBAAA,uBAMN,iDACE,oBAAA,mBAEF,4DACE,iBAAA,mBAKJ,uBACE,IAAA,kEAGF,0BACE,IAAA,eACA,OAAA,kEAGF,6BACE,KAAA,YACA,MAAA,eAME,2DACE,KAAA,IACA,MAAA,KAGF,2DACE,KAAA,IACA,MAAA,KCxDN,YACE,OAAA,IAAA,MAAA,uBACA,QAAA,IACA,QAAA,cAKI,wBACE,YAAA,IAGA,yC/BknGR,2CAA4C,iD+BlnGpC,+BAAA,8CAAA,qCAAA,iCAAA,0CAAA,gDAAA,uCAAA,8BAAA,uCAAA,6CAAA,oCAIE,iBAAA,kBACA,iBAAA,KACA,mBAAA,KAAA,WAAA,KACA,MAAA,e/BmnGV,qCACA,mC+BjnGQ,oCAAA,kCAII,WAAA,gB/BinGZ,iCACA,iC+B/mGQ,4BAAA,4BAII,MAAA,mBACA,QAAA,GAGJ,8BAAA,uCAAA,6CAAA,oCACI,iBAAA,sBAQV,6BAAA,6BACE,QAAA,IAKF,gDACE,MAAA,eACA,QAAA,aAOJ,sBACE,OAAA,IAAA,MAAA,uBACA,mBAAA,KAAA,WAAA,KACA,iBAAA,uBAEA,wCACE,MAAA,MAIJ,YACE,MAAA,0BACE,qCAAA,wBAAA,wBAAA,8BACE,WAAA,gBAQF,wBACE,iBAAA,uBACA,cAAA,IAEA,+BAAA,qCACE,iBAAA,qBACA,MAAA,QAGF,oCAAA,0CAAA,8BACE,iBAAA,QACA,MAAA,KAIF,8BAAA,oCACE,MAAA,mBACA,QAAA,GAOF,wCACE,YAAA,IAGF,wCAAA,8CACE,iBAAA,uBC5GF,2DAAA,wEAEE,wBAAA,EACA,2BAAA,EAQF,0DAAA,uEAEE,uBAAA,EACA,0BAAA,ECfJ,kBACI,OAAA,IAAA,MAAA,iCAGJ,2BACI,iBAAA,iCAGA,8BACI,MAAA,eAIA,sDACI,mBAAA,qBAAA,WAAA,qBAIR,oBACI,MAAA,QAEA,gEACI,iBAAA,QAKJ,0BACI,MAAA,kBACA,iBAAA,QAIR,kCjCmsGR,wCACA,iCACA,uCiCjsGY,KAAA,kBAGJ,6BACI,iBAAA,QAGJ,6BACI,iBAAA,iCAGJ,uBACI,iBAAA,iCAEA,iCACI,iBAAA,gCACA,yDACI,MAAA,eAKZ,gCACI,iBAAA,iCAEI,8DACI,iBAAA,gCAEI,4EACI,KAAA,eAOpB,yBACI,iBAAA,iCAGJ,yBACI,iBAAA,iCAGJ,yBACI,iBAAA,iCACA,MAAA,+BAIZ,kDACI,aAAA,iCAGJ,gBACI,iBAAA,iCAGJ,4BACI,MAAA,eAGJ,0DACI,iBAAA,gCAIA,+BACI,KAAA,+BC1GR,UACE,WAAA,MACA,OAAA,IAAA,OAAA,uBACA,WAAA,uBACA,cAAA,IAEA,sBACE,UAAA,KACA,MAAA,KACA,OAAA,KAAA,ECTF,2BACE,UAAA,KACA,WAAA,MAQA,kBACE,QAAA,YAAA,QAAA,YAAA,QAAA,KACA,cAAA,KAAA,UAAA,KACA,aAAA,EACA,cAAA,EACA,WAAA,KAEA,6BAPF,kBAQI,mBAAA,SAAA,sBAAA,OAAA,mBAAA,OAAA,eAAA,QAGF,oBAAA,qBACE,wBAAA,EAAA,WAAA,EACA,iBAAA,EAAA,kBAAA,EAAA,UAAA,EAGF,qBACE,MAAA,KAEA,uBACE,QAAA,MACA,QAAA,MAAA,KACA,MAAA,0BACA,YAAA,IAEA,iBAAA,oBAIJ,gCACE,SAAA,SACA,KAAA,OAMJ,uBACE,QAAA,aACA,MAAA,KACA,OAAA,KACA,YAAA,KACA,OAAA,IAAA,MAAA,QACA,MAAA,QACA,WAAA,OACA,cAAA,IACA,aAAA,MAIA,0BAAA,iCAAA,gCACE,iBAAA,oBACA,MAAA,yBACA,kCAAA,yCAAA,wCACE,iBAAA,QACA,MAAA,KAOR,iBACE,iBAAA,YACA,QAAA,KACA,WAAA,EACA,cAAA,EACA,WAAA,MACA,wBACE,SAAA,SACA,KAAA,OAEF,uBACE,MAAA,KACA,OAAA,KACA,QAAA,KAAA,EAAA,EACA,SAAA,OAMJ,iBACE,SAAA,SACA,QAAA,MACA,WAAA,MACA,MAAA,KACA,oBACI,QAAA,MACA,WAAA,MACA,aAAA,EACE,uBACE,QAAA,aACA,OAAA,EAAA,KAIN,mBAAA,0BAAA,yBACE,iBAAA,QACA,cAAA,IACA,QAAA,IAAA,KACA,MAAA,KAIA,6BAAA,oCAAA,mCACE,QAAA,IACA,iBAAA,QACA,MAAA,KACA,OAAA,YAMR,wBACE,QAAA,YAAA,QAAA,YAAA,QAAA,KACA,cAAA,KAAA,UAAA,KAIE,kCACE,mBAAA,SAAA,sBAAA,OAAA,mBAAA,OAAA,eAAA,OAEA,qCACI,MAAA,eAMR,iCAAA,iCAAA,+BACE,MAAA,KAIE,0BADJ,+BAEM,MAAA,KAIJ,iCACE,QAAA,KACA,0BAFF,iCAGI,MAAA,IACA,QAAA,KAAA,MAGJ,uCACE,QAAA,EC9JN,6CACE,WAAA,MAEA,yBAHF,6CAII,WAAA,QAIF,mDACE,YAAA,KACA,aAAA,EAOJ,iBpC67GF,gBoC57GI,gBAAA,mBACA,kDpC+7GJ,iDoC97GM,SAAA,SAEA,8DpCg8GN,6DoC/7GQ,aAAA,KAEA,qEpCi8GR,oEoCh8GU,IAAA,IACA,KAAA,IACA,OAAA,KACA,MAAA,KACA,WAAA,KACA,QAAA,MACA,SAAA,SACA,MAAA,KACA,OAAA,IAAA,MAAA,KACA,cAAA,KACA,mBAAA,YAAA,WAAA,YACA,WAAA,OACA,YAAA,YACA,YAAA,KACA,QAAA,IACA,iBAAA,QC1CR,+BACE,QAAA,MAEF,oCACE,OAAA,eAGA,0CACE,iBAAA,QACA,MAAA,QACA,OAAA,IAAA,MAAA,QACA,sDACI,iBAAA,QACA,aAAA,QACA,MAAA,KACA,mBAAA,EAAA,EAAA,EAAA,IAAA,oBAAA,WAAA,EAAA,EAAA,EAAA,IAAA,oBAGN,wCACE,MAAA,MACA,uDACE,MAAA,EACA,kBAAA,eAAA,UAAA,eACA,IAAA,eAKJ,2BACE,UAAA,KACA,YAAA,IAIJ,gCACE,aAAA,KACA,MAAA,+BAEA,sCACE,iBAAA,kBAGF,sCACE,QAAA,aACA,aAAA,IACA,SAAA,SACA,8CACE,cAAA,IAAA,YACA,mBAAA,IAAA,YACA,iBAAA,KACA,cAAA,IACA,OAAA,IAAA,MAAA,QACA,QAAA,GACA,QAAA,aACA,OAAA,KACA,KAAA,EACA,YAAA,MACA,SAAA,SACA,WAAA,IAAA,YACA,MAAA,KACA,QAAA,YAEF,6CACE,MAAA,QACA,QAAA,aACA,UAAA,KACA,OAAA,KACA,KAAA,EACA,YAAA,MACA,aAAA,IACA,YAAA,IACA,SAAA,SACA,IAAA,KACA,MAAA,KAGJ,qDACE,OAAA,QACA,QAAA,EACA,QAAA,EACA,QAAA,YAEA,oEACE,QAAA,IAIF,yEACE,eAAA,KACA,QAAA,EAIF,0EACE,QAAA,QACA,YAAA,sBACA,YAAA,IAIF,4EACE,iBAAA,QACA,OAAA,YAIF,2EACE,iBAAA,QACA,aAAA,QAEF,0EACE,MAAA,KAMJ,uDACE,IAAA,eACA,iBAAA,QACA,6DACE,MAAA,KrCo+GR,uCADA,qDqC99GE,qDAGI,WAAA,QACA,aAAA,QACA,MAAA,KrCi+GN,6CADA,2DqC99GM,2DACI,MAAA,KrCm+GV,uDqC/9GI,uDrCg+GJ,uDAFA,uDqC19GM,MAAA,qBAKJ,yBAEI,kEACE,IAAA,iBCtJN,mBAAA,oBACE,OAAA,uDACA,QAAA,OAAA,MACA,OAAA,IAAA,MAAA,mCACA,iBAAA,uBACA,MAAA,yBACA,cAAA,wBACA,yBAAA,0BACE,QAAA,EACA,aAAA,iCCZN,aACI,WAAA,eAEA,kBACI,YAAA,oCACA,KAAA,mBAGJ,gCACI,OAAA,EAAA,KvCuoHR,yBuCnoHA,0BAEI,YAAA,oCAGJ,0BACI,YAAA,IAGJ,qBACI,eAAA,KACA,OAAA,uBAGJ,wBACI,MAAA,kBACA,YAAA,oCACA,UAAA,eAGJ,sBACI,KAAA,evCsoHJ,uBuCjoHI,uBACI,YAAA,oCACA,KAAA,0BAIR,qBACI,OAAA,uBAIA,kDACI,OAAA,uBAIR,oBACI,iBAAA,iCACA,OAAA,IAAA,MAAA,iCAEA,8CACI,iBAAA,iCACA,cAAA,IAAA,MAAA,iCAIR,qBACI,OAAA,uBAIA,8BACI,OAAA,uBAIR,sBACI,KAAA,0BAGJ,uBACI,OAAA,uBC9EJ,UACI,OAAA,MCHJ,oBACE,OAAA,MAGF,SACE,QAAA,IAAA,KACA,iBAAA,kBACA,QAAA,IACA,MAAA,QACA,mBAAA,EAAA,OAAA,OAAA,mBAAA,WAAA,EAAA,OAAA,OAAA,mBACA,cAAA,IAGF,aACE,MAAA,QCbF,YACE,mBAAA,YAAA,WAAA,YACA,MAAA,eACA,OAAA,eACA,iBAAA,kBACA,mBAAA,EAAA,KAAA,KAAA,iBAAA,WAAA,EAAA,KAAA,KAAA,iBACA,QAAA,IAAA,eACA,cAAA,IACA,aAAA,kBAGF,UACE,MAAA,kBACA,UAAA,eACA,YAAA,eACA,YAAA,oCACA,YAAA,cCfF,OAAA,gBACE,OAAA,gBACA,WAAA,QACA,cAAA,IAGF,eACE,QAAA,MACA,WAAA,OACA,MAAA,KACA,UAAA,KACA,YAAA,KACA,WAAA,QACA,cAAA,IACA,QAAA,KAAA,KAGF,qBACE,KAAA,IACA,YAAA,MACA,MAAA,EACA,OAAA,EACA,SAAA,SACA,2BACE,OAAA,MACA,YAAA,KAAA,MAAA,YACA,aAAA,KAAA,MAAA,YACA,WAAA,KAAA,MAAA,QAEF,2BACE,IAAA,MACA,YAAA,KAAA,MAAA,YACA,aAAA,KAAA,MAAA,YACA,cAAA,KAAA,MAAA,QClCJ,kBACI,OAAA,KACA,WAAA,QACA,MAAA,QACA,YAAA,0BACA,UAAA,SACA,QAAA,IAAA,ICNJ,aACI,OAAA,MACA,+BACI,QAAA,GCDR,UACI,SAAA,SACA,IAAA,KACA,MAAA,KAMA,2BACI,QAAA,wBAEJ,4BACI,QAAA,uBAIR,cACI,iBAAA,uBAQI,qEACI,QAAA,SAOZ,cACI,iBAAA,qBACA,QAAA,YAAA,QAAA,YAAA,QAAA,KAEA,0BAJJ,cAKQ,OAAA,OAIJ,sBACI,QAAA,GACA,SAAA,SACA,MAAA,MACA,OAAA,MACA,cAAA,IAGJ,0BACI,WAAA,mCACA,gBAAA,MACA,kBAAA,UACA,oBAAA,OAIR,wBACI,QAAA,YAAA,QAAA,YAAA,QAAA,KAEA,0BAHJ,wBAIQ,WAAA,OAQQ,wDACI,iBAAA,qBAGA,+DAAA,8DACI,iBAAA,QC3EpB,0BACI,cAAA,KACA,aAAA,KAEJ,yBACI,UAAA,KACA,SAAA,SACA,KAAA,KACA,IAAA,EACA,YAAA,KAMA,mBACI,QAAA,MACA,QAAA,IAAA,EACA,MAAA,qBAOJ,sCACI,YAAA,IAEJ,sCACI,MAAA,KACA,OAAA,KACA,UAAA,KACA,QAAA,EACA,YAAA,KACA,WAAA,OACA,cAAA,IAKZ,gBACI,SAAA,SACA,MAAA,EACA,IAAA,EAOI,mCACI,OAAA,IAAA,EAEA,0CACI,iBAAA,sBAOZ,iBACI,QAAA,aACA,WAAA,OACA,MAAA,qBACA,qCACI,OAAA,IAEJ,wBAAA,uBACI,MAAA,QACA,4CAAA,2CACI,aAAA,kBASZ,sBACI,YAAA,GAGJ,yBACI,SAAA,SACA,UAAA,MACA,MAAA,sBACA,YAAA,GACA,MAAA,EACA,OAAA,EASA,oCACI,cAAA,KACA,WAAA,OACA,iBAAA,uBACA,mBAAA,EAAA,OAAA,OAAA,mBAAA,WAAA,EAAA,OAAA,OAAA,mBAEA,2CACI,iBAAA,QAEJ,oDACI,UAAA,KCjHhB,eACE,MAAA,MACA,MAAA,KACA,QAAA,KACA,cAAA,IAGF,gBACE,YAAA,MAIA,4BACE,MAAA,yBACA,YAAA,IAEF,iBACE,UAAA,KAIJ,yBACE,eACE,MAAA,KACA,MAAA,KAEF,gBACE,OAAA,GAMF,aACE,QAAA,MACA,MAAA,0BACA,YAAA,KACA,QAAA,IAAA,IACA,oBACE,MAAA,QACA,YAAA,IAKN,cACE,QAAA,MACA,aAAA,EAEA,iBACE,SAAA,SACA,QAAA,MACA,OAAA,KACA,YAAA,KACA,OAAA,QACA,4BAAA,IAAA,oBAAA,IAEA,mBACE,MAAA,0BAGF,uBACE,WAAA,sBACA,4BAAA,KAAA,oBAAA,KAGF,2BACE,MAAA,KACA,SAAA,SAGF,6BACE,MAAA,MhDm8HN,oDACA,kCgDl8HM,0CAGE,QAAA,MACA,MAAA,KAGF,kCACE,OAAA,IAAA,MAAA,YACA,cAAA,MACA,OAAA,KAAA,KAAA,EACA,OAAA,EACA,MAAA,EACA,YAAA,EACA,UAAA,EAGF,oDACE,OAAA,KAAA,KAAA,EAAA,KAGF,0CACE,WAAA,KACA,YAAA,IAGF,oCACE,SAAA,SACA,IAAA,EACA,KAAA,MACA,MAAA,EACA,cAAA,SACA,SAAA,OACA,YAAA,OACA,cAAA,EAIJ,6BACE,SAAA,SACA,IAAA,EACA,KAAA,MACA,MAAA,EACA,OAAA,EhD67HN,mCgD37HM,sCAEE,SAAA,SACA,IAAA,EAGF,sCACE,KAAA,EACA,MAAA,MACA,cAAA,SACA,SAAA,OACA,YAAA,OAGF,mCACE,MAAA,EACA,MAAA,MACA,aAAA,KAIJ,wBAAA,8BAEE,mBAAA,MAAA,IAAA,EAAA,EAAA,QAAA,WAAA,MAAA,IAAA,EAAA,EAAA,QAIJ,wBACE,iBAAA,sBACA,YAAA,IACA,MAAA,qBACE,0BACE,MAAA,qBACA,YAAA,IAMN,qCACE,OAAA,QACA,OAAA,KACA,MAAA,KACA,SAAA,SACA,QAAA,aACA,iBAAA,uBACA,mBAAA,MAAA,EAAA,EAAA,EAAA,IAAA,mCAAA,WAAA,MAAA,EAAA,EAAA,EAAA,IAAA,mCACA,cAAA,IAEA,2CACE,QAAA,EACA,OAAA,QAEF,yDACE,QAAA,EAGF,2CACE,SAAA,SACA,OAAA,KACA,MAAA,KACA,KAAA,EACA,OAAA,QACA,QAAA,EACA,cAAA,EACA,4BAAA,KAAA,oBAAA,KACA,IAAA,EACA,kDACE,QAAA,SACA,YAAA,wBACA,IAAA,EACA,OAAA,KACA,MAAA,qBACA,MAAA,KACA,SAAA,SACA,WAAA,MACA,KAAA,IACA,UAAA,KAMR,4BACE,6BACI,MAAA,OCvMF,0BAFJ,qBAGQ,UAAA,MACA,UAAA,OAGJ,0BAPJ,qBAQQ,UAAA,MACA,UAAA,OAKR,iBACI,QAAA,IAAA,EAEI,sBACI,QAAA,MACA,QAAA,IAAA,KACA,MAAA,qBACA,YAAA,IAIA,6BACI,MAAA,QAIR,uBACI,aAAA,KAEI,4BACI,QAAA,IAAA,KACA,MAAA,0BACA,UAAA,KACA,YAAA,ICtChB,yBADJ,kBAEQ,UAAA,OAGJ,0BALJ,kBAMQ,UAAA,OAIA,6CACI,iBAAA,uBAGJ,qDACI,WAAA,MAOJ,kCACI,QAAA,GACA,SAAA,SACA,MAAA,IACA,OAAA,IACA,iBAAA,QACA,cAAA,IACA,MAAA,EAIR,yBACI,QAAA,IACA,mBAAA,KAAA,WAAA,KACA,UAAA,KAKJ,+BACI,OAAA,EAIR,WACI,OAAA,EAGQ,uBACI,iBAAA,uBACA,aAAA,YACA,mBAAA,EAAA,OAAA,OAAA,mBAAA,WAAA,EAAA,OAAA,OAAA,mBAGR,gBACI,QAAA,MACA,QAAA,KAAA,KACA,MAAA,0BACA,mBAAA,IAAA,IAAA,WAAA,IAAA,IACA,WAAA,IAAA,MAAA,uBACA,cAAA,IACA,sBACI,iBAAA,uBACA,aAAA,YACA,mBAAA,EAAA,OAAA,OAAA,mBAAA,WAAA,EAAA,OAAA,OAAA,mBAQR,kCACI,MAAA,qBACA,OAAA,KACA,MAAA,KACA,YAAA,KACA,mBAAA,KAAA,WAAA,KACA,QAAA,EACA,UAAA,KACA,iBAAA,gBACA,cAAA,IACA,OAAA,KAGJ,wCACI,mBAAA,EAAA,OAAA,OAAA,mBAAA,WAAA,EAAA,OAAA,OAAA,mBACA,OAAA,IAAA,MAAA,uBAMR,sBACI,MAAA,KAGJ,mCACI,SAAA,SACA,WAAA,OACA,cAAA,KAEA,0CACI,iBAAA,uBACA,SAAA,SACA,QAAA,EACA,QAAA,IAAA,KAGJ,0CACI,QAAA,GACA,SAAA,SACA,MAAA,KACA,OAAA,IACA,KAAA,EACA,MAAA,EACA,iBAAA,uBACA,IAAA,KAEJ,0CACI,UAAA,KAGR,sCACI,cAAA,KACA,QAAA,aACA,SAAA,SAEA,kDACI,QAAA,KAAA,KACA,iBAAA,oBACA,cAAA,IAAA,IAAA,IAAA,EACA,SAAA,OAEA,qEACI,YAAA,IACA,MAAA,QACA,cAAA,IAIR,gDACI,MAAA,MACA,iEACI,UAAA,KACA,QAAA,IACA,MAAA,0BACA,4BAJJ,iEAKQ,QAAA,MAIR,+DACI,mBAAA,EAAA,OAAA,OAAA,mBAAA,WAAA,EAAA,OAAA,OAAA,mBACA,OAAA,IAAA,MAAA,uBAIR,iDACI,UAAA,KAKJ,6CACI,MAAA,MACA,yDACI,iBAAA,gBACA,WAAA,MACA,cAAA,IAAA,IAAA,EAAA,IAEJ,uDACI,MAAA,KAKI,iFACI,MAAA,EACA,KAAA,KASZ,wDACI,QAAA,SACA,YAAA,wBACA,SAAA,SACA,MAAA,QACA,MAAA,EACA,OAAA,EACA,UAAA,KAEA,4BATJ,wDAUQ,QAAA,MAOpB,oBACI,WAAA,IAAA,MAAA,uBAGJ,YACI,cAAA,KACA,iBAAA,0BACA,aAAA,0BACA,cAAA,MAGJ,kBACI,SAAA,SACA,MAAA,KACA,IAAA,IACA,kBAAA,iBAAA,UAAA,iBAEI,uBACI,UAAA,KACA,YAAA,KACA,QAAA,EAAA,IACA,QAAA,aAMR,4BADJ,WAEQ,UAAA,MCzOR,oBACE,gBAAA,SACA,eAAA,EAAA,KAEA,uBACE,iBAAA,uBCNF,iBACE,MAAA,qBAMJ,kBACE,WAAA,MCTF,yBACI,gBACI,SAAA,SAEA,sBACI,QAAA,SACA,YAAA,wBACA,UAAA,KACA,SAAA,SACA,MAAA,KACA,OAAA,KACA,YAAA,KACA,cAAA,IACA,WAAA,OACA,MAAA,EACA,IAAA,IACA,kBAAA,iBAAA,UAAA,iBACA,iBAAA,QACA,MAAA,KACA,QAAA,EACA,MAAA,OAMZ,6BACI,OAAA,IAAA,MAAA,uBACA,WAAA,EAOA,gCACI,WAAA,MAEA,4CACI,OAAA,KAAA,EAUZ,SACI,SAAA,SACA,YAAA,KACA,eAAA,KAEA,kBACI,iBAAA,iCAIR,aACI,MAAA,0BACA,cAAA,IAMJ,YACI,QAAA,EAAA,KACA,MAAA,KACA,QAAA,IACA,cAAA,EACA,mBAAA,IAAA,IAAA,YAAA,WAAA,IAAA,IAAA,YACA,cAAA,IAAA,MAAA,qBAEA,4BARJ,YASQ,iBAAA,qBAGJ,yBACI,YAAA,KACA,mBAAA,IAAA,IAAA,WAAA,IAAA,IAEA,oCACI,QAAA,KAEA,4BAHJ,oCAIQ,QAAA,OAIR,qCACI,QAAA,MAEA,4BAHJ,qCAIQ,QAAA,MAQJ,4CACI,MAAA,qBACA,YAAA,KACA,QAAA,IAAA,KACA,YAAA,IACA,mBAAA,IAAA,IAAA,WAAA,IAAA,IAEA,4BAPJ,4CAQQ,MAAA,6BAGJ,mDAAA,kDAEI,MAAA,qBAEA,4BAJJ,mDAAA,kDAKQ,MAAA,SAIR,4BApBJ,4CAqBQ,YAAA,gBAMhB,uBACI,iBAAA,oBACA,mBAAA,EAAA,OAAA,OAAA,mBAAA,WAAA,EAAA,OAAA,OAAA,mBAEA,oCACI,YAAA,KAEA,+CACI,QAAA,wBAGJ,gDACI,QAAA,uBAMA,uDACI,YAAA,KACA,MAAA,4BAEA,8DAAA,6DAEI,MAAA,QAQxB,YACI,SAAA,SACA,OAAA,KACA,MAAA,KACA,MAAA,EACA,OAAA,EACA,KAAA,EACA,IAAA,EACA,QAAA,GACA,iBAAA,KAGJ,cACI,YAAA,MACA,eAAA,MAEA,0BACI,iBAAA,sCACA,gBAAA,MACA,oBAAA,IAGJ,4BAVJ,cAWQ,YAAA,MACA,eAAA,MAGJ,0BACI,UAAA,KAEA,4BAHJ,0BAIQ,UAAA,MAIR,6BACI,UAAA,KACA,aAAA,MACA,YAAA,MAEA,4BALJ,6BAMQ,QAAA,OAGJ,yCACI,aAAA,KACA,YAAA,KACA,OAAA,IAAA,MAAA,uBACA,cAAA,IACA,QAAA,IACA,iBAAA,uBAEA,4BARJ,yCASQ,QAAA,aACA,MAAA,IACA,cAAA,MAGJ,8CACI,iBAAA,gBACA,UAAA,KACA,QAAA,IACA,WAAA,IAKZ,gCACI,SAAA,QAEA,8CACI,SAAA,QAGJ,gDACI,SAAA,SACA,WAAA,MACA,MAAA,qBACA,OAAA,KACA,UAAA,KACA,YAAA,IAQZ,gBACI,SAAA,SACA,OAAA,KAOA,mBACI,WAAA,KACA,MAAA,eACA,OAAA,KAAA,KACA,QAAA,GACA,mBAAA,IAAA,IAAA,WAAA,IAAA,IAOR,iBACI,QAAA,GAQI,+BACI,MAAA,qBACA,UAAA,KAQR,sBACI,SAAA,SACA,IAAA,KACA,MAAA,KAMR,gBACI,QAAA,KAAA,EAAA,KACA,iBAAA,QACA,MAAA,qBAEA,mCACI,MAAA,qBAKI,uCACI,QAAA,MACA,MAAA,qBACA,cAAA,KACA,mBAAA,IAAA,IAAA,WAAA,IAAA,IAEA,6CACI,MAAA,qBAQZ,iCACI,QAAA,MACA,MAAA,qBACA,QAAA,KAAA,EACA,cAAA,IAAA,MAAA,qBAEA,6CACI,MAAA,qBACA,UAAA,KAGJ,+CACI,YAAA,EAGJ,8CACI,eAAA,EACA,cAAA,EAKZ,+BACI,aAAA,qBC5VR,gBACI,UAAA,KACA,YAAA,IACA,WAAA,OACA,QAAA,YAAA,QAAA,YAAA,QAAA,KACA,qBACI,UAAA,KACA,YAAA,IACA,QAAA,MACA,YAAA,IAIR,YACI,MAAA,ICTI,mCACI,WAAA,OACA,QAAA,MAEA,oDACI,SAAA,SACA,4DACI,QAAA,GACA,SAAA,SACA,MAAA,KACA,IAAA,KACA,KAAA,EACA,MAAA,EACA,cAAA,IAAA,OAAA,uBAEJ,qEACI,SAAA,SACA,iBAAA,uBACA,QAAA,IAKJ,0DACI,kBAAA,UAAA,KAAA,SAAA,OAAA,UAAA,UAAA,KAAA,SAAA,OAKJ,2DACI,kBAAA,UAAA,KAAA,SAAA,OAAA,UAAA,UAAA,KAAA,SAAA,OACA,kEACI,QAAA,QAWxB,gBACI,YAAA,IAAA,OAAA,uBACA,OAAA,EAAA,KACA,4BACI,SAAA,SACA,QAAA,EAAA,EAAA,KAAA,KAEA,gDACI,SAAA,SACA,KAAA,KACA,IAAA,EACA,QAAA,EACA,UAAA,KAEJ,2CACI,SAAA,SACA,OAAA,IAAA,MAAA,uBACA,cAAA,IAIA,uDACI,MAAA,QAIR,uCACI,eAAA,ECtER,oBACI,SAAA,SAEA,4BACI,QAAA,GACA,SAAA,SACA,MAAA,KACA,OAAA,IACA,WAAA,uBACA,KAAA,EACA,MAAA,EACA,IAAA,KAOZ,gBACI,SAAA,SACA,IAAA,IACA,KAAA,EACA,MAAA,EACA,kBAAA,iBAAA,UAAA,iBACA,OAAA,EAAA,KC5BA,mBACI,MAAA,qBACA,mBAAA,IAAA,IAAA,KAAA,WAAA,IAAA,IAAA,KACA,yBACI,MAAA", "file": "app.min.css", "sourcesContent": ["//\r\n// Google font - Poppins\r\n//\r\n\r\n@import url('https://fonts.googleapis.com/css?family=Poppins:300,400,500,600,700&display=swap');", "// \r\n// _header.scss\r\n// \r\n\r\n#page-topbar {\r\n    position: fixed;\r\n    top: 0;\r\n    right: 0;\r\n    left: 0;\r\n    z-index: 1002;\r\n    background-color: var(--#{$prefix}header-bg);\r\n    box-shadow: $box-shadow;\r\n}\r\n\r\n.navbar-header {\r\n    display: flex;\r\n    -ms-flex-pack: justify;\r\n    justify-content: space-between;\r\n    align-items: center;\r\n    margin: 0 auto;\r\n    height: $header-height;\r\n    padding: 0 calc(#{$grid-gutter-width} * 0.5) 0 0 /*rtl: 0 0 0 calc(#{$grid-gutter-width} / 2) */;\r\n\r\n    .dropdown .show {\r\n        &.header-item {\r\n            background-color: var(--#{$prefix}tertiary-bg);\r\n        }\r\n    }\r\n}\r\n\r\n.navbar-brand-box {\r\n    padding: 0 1.5rem;\r\n    text-align: center;\r\n    width: $navbar-brand-box-width;\r\n}\r\n\r\n.logo {\r\n    line-height: 70px;\r\n\r\n    .logo-sm {\r\n        display: none;\r\n    }\r\n}\r\n\r\n.logo-light {\r\n    display: none;\r\n}\r\n\r\n/* Search */\r\n\r\n.app-search {\r\n    padding: calc(#{$header-height - 38px} * 0.5) 0;\r\n\r\n    .form-control {\r\n        border: none;\r\n        height: 38px;\r\n        padding-left: 40px;\r\n        padding-right: 20px;\r\n        background-color: var(--#{$prefix}topbar-search-bg);\r\n        box-shadow: none;\r\n        border-radius: 30px;\r\n    }\r\n    span {\r\n        position: absolute;\r\n        z-index: 10;\r\n        font-size: 16px;\r\n        line-height: 38px;\r\n        left: 13px;\r\n        top: 0;\r\n        color: var(--#{$prefix}secondary-color);\r\n    }\r\n}\r\n\r\n// Mega menu\r\n\r\n.megamenu-list {\r\n    li{\r\n        position: relative;\r\n        padding: 5px 0px;\r\n        a{\r\n            color: var(--#{$prefix}body-color);\r\n        }\r\n    }\r\n}\r\n\r\n@media (max-width: 992px) {\r\n    .navbar-brand-box {\r\n        width: auto;\r\n    }\r\n\r\n    .logo {\r\n\r\n        span.logo-lg {\r\n            display: none;\r\n        }\r\n\r\n        span.logo-sm {\r\n            display: inline-block;\r\n        }\r\n    }\r\n}\r\n\r\n.page-content {\r\n    padding: calc(#{$header-height} + #{$grid-gutter-width}) calc(#{$grid-gutter-width} * 0.75) $footer-height calc(#{$grid-gutter-width} * 0.75);\r\n}\r\n\r\n.header-item {\r\n    height: $header-height;\r\n    box-shadow: none !important;\r\n    color: var(--#{$prefix}header-item-color);\r\n    border: 0;\r\n    border-radius: 0px;\r\n\r\n    &:hover {\r\n        color: var(--#{$prefix}header-item-color);\r\n    }\r\n}\r\n\r\n.header-profile-user {\r\n    height: 36px;\r\n    width: 36px;\r\n    background-color: var(--#{$prefix}tertiary-bg);\r\n    padding: 3px;\r\n}\r\n\r\n.noti-icon {\r\n    i {\r\n        font-size: 22px;\r\n        color: var(--#{$prefix}header-item-color);\r\n    }\r\n\r\n    .badge {\r\n        position: absolute;\r\n        top: 12px;\r\n        right: 4px;\r\n    }\r\n}\r\n\r\n.notification-item {\r\n    .d-flex {\r\n        padding: 0.75rem 1rem;\r\n\r\n        &:hover {\r\n            background-color: var(--#{$prefix}tertiary-bg);\r\n        }\r\n    }\r\n}\r\n\r\n// Dropdown with Icons\r\n.dropdown-icon-item {\r\n    display: block;\r\n    border-radius: 3px;\r\n    line-height: 34px;\r\n    text-align: center;\r\n    padding: 15px 0 9px;\r\n    display: block;\r\n    border: 1px solid transparent;\r\n    color: var(--#{$prefix}secondary-color);\r\n\r\n    img {\r\n        height: 24px;\r\n    }\r\n\r\n    span {\r\n        display: block;\r\n        overflow: hidden;\r\n        text-overflow: ellipsis;\r\n        white-space: nowrap;\r\n    }\r\n\r\n    &:hover {\r\n        border-color: var(--#{$prefix}border-color);\r\n    }\r\n}\r\n\r\n// Full Screen\r\n.fullscreen-enable {\r\n    [data-bs-toggle=\"fullscreen\"] {\r\n        .bx-fullscreen::before {\r\n            content: \"\\ea3f\";\r\n        }\r\n    }\r\n}\r\n\r\nbody[data-topbar=\"dark\"] {\r\n    #page-topbar { \r\n        background-color: var(--#{$prefix}header-dark-bg);\r\n    }\r\n    .navbar-header {\r\n        .dropdown .show {\r\n            &.header-item {\r\n                background-color: rgba($white, 0.05);\r\n            }\r\n        }\r\n\r\n        .waves-effect .waves-ripple {\r\n            background: rgba($white, 0.4);\r\n        }\r\n    }\r\n\r\n    .header-item {\r\n        color: var(--#{$prefix}header-dark-item-color);\r\n    \r\n        &:hover {\r\n            color: var(--#{$prefix}header-dark-item-color);\r\n        }\r\n    }\r\n\r\n    .header-profile-user {\r\n        background-color: rgba($white, 0.25);\r\n    }\r\n    \r\n    .noti-icon {\r\n        i {\r\n            color: var(--#{$prefix}header-dark-item-color);\r\n        }\r\n    }\r\n\r\n    .logo-dark {\r\n        display: none;\r\n    }\r\n\r\n    .logo-light {\r\n        display: block;\r\n    }\r\n\r\n    .app-search {\r\n    \r\n        .form-control {\r\n            background-color: rgba(var(--#{$prefix}topbar-search-bg),0.07);\r\n            color: $white;\r\n        }\r\n        span,\r\n        input.form-control::-webkit-input-placeholder {\r\n            color: rgba($white,0.5);\r\n        }\r\n    }\r\n}\r\n\r\nbody[data-sidebar=\"dark\"] {\r\n    .navbar-brand-box {\r\n        background: $sidebar-dark-bg;\r\n    }\r\n\r\n    .logo-dark {\r\n        display: none;\r\n    }\r\n\r\n    .logo-light {\r\n        display: block;\r\n    }\r\n}\r\n\r\n@media (max-width: 600px) {\r\n    .navbar-header {\r\n        .dropdown {\r\n            position: static;\r\n\r\n            .dropdown-menu {\r\n                left: 10px !important;\r\n                right: 10px !important;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n@media (max-width: 380px) {\r\n    .navbar-brand-box {\r\n        display: none;\r\n    }\r\n}\r\n\r\nbody[data-layout=\"horizontal\"] {\r\n    .navbar-brand-box {\r\n        width: auto;\r\n    }\r\n    .page-content {\r\n        margin-top: $header-height;\r\n        padding: calc(55px + #{$grid-gutter-width}) calc(#{$grid-gutter-width} * 0.5) $footer-height calc(#{$grid-gutter-width} * 0.5);\r\n    }    \r\n}\r\n\r\n@media (max-width: 992px) { \r\n    body[data-layout=\"horizontal\"] {\r\n        .page-content {\r\n            margin-top: 15px;\r\n        }    \r\n    }\r\n}", "/*\nTemplate Name: <PERSON>kot<PERSON> - Admin & Dashboard Template\nAuthor: Themesbrand\nVersion: 4.1.0.\nWebsite: https://themesbrand.com/\nContact: <EMAIL>\nFile: Main Css File\n*/\n@import url(\"https://fonts.googleapis.com/css?family=Poppins:300,400,500,600,700&display=swap\");\n/* =============================Custom Variables========================== */\n#page-topbar {\n  position: fixed;\n  top: 0;\n  right: 0;\n  left: 0;\n  z-index: 1002;\n  background-color: var(--bs-header-bg);\n  box-shadow: 0 0.75rem 1.5rem rgba(18, 38, 63, 0.03);\n}\n\n.navbar-header {\n  display: flex;\n  -ms-flex-pack: justify;\n  justify-content: space-between;\n  align-items: center;\n  margin: 0 auto;\n  height: 70px;\n  padding: 0 calc(24px * 0.5) 0 0;\n}\n.navbar-header .dropdown .show.header-item {\n  background-color: var(--bs-tertiary-bg);\n}\n\n.navbar-brand-box {\n  padding: 0 1.5rem;\n  text-align: center;\n  width: 250px;\n}\n\n.logo {\n  line-height: 70px;\n}\n.logo .logo-sm {\n  display: none;\n}\n\n.logo-light {\n  display: none;\n}\n\n/* Search */\n.app-search {\n  padding: calc(32px * 0.5) 0;\n}\n.app-search .form-control {\n  border: none;\n  height: 38px;\n  padding-left: 40px;\n  padding-right: 20px;\n  background-color: var(--bs-topbar-search-bg);\n  box-shadow: none;\n  border-radius: 30px;\n}\n.app-search span {\n  position: absolute;\n  z-index: 10;\n  font-size: 16px;\n  line-height: 38px;\n  left: 13px;\n  top: 0;\n  color: var(--bs-secondary-color);\n}\n\n.megamenu-list li {\n  position: relative;\n  padding: 5px 0px;\n}\n.megamenu-list li a {\n  color: var(--bs-body-color);\n}\n\n@media (max-width: 992px) {\n  .navbar-brand-box {\n    width: auto;\n  }\n  .logo span.logo-lg {\n    display: none;\n  }\n  .logo span.logo-sm {\n    display: inline-block;\n  }\n}\n.page-content {\n  padding: calc(70px + 24px) calc(24px * 0.75) 60px calc(24px * 0.75);\n}\n\n.header-item {\n  height: 70px;\n  box-shadow: none !important;\n  color: var(--bs-header-item-color);\n  border: 0;\n  border-radius: 0px;\n}\n.header-item:hover {\n  color: var(--bs-header-item-color);\n}\n\n.header-profile-user {\n  height: 36px;\n  width: 36px;\n  background-color: var(--bs-tertiary-bg);\n  padding: 3px;\n}\n\n.noti-icon i {\n  font-size: 22px;\n  color: var(--bs-header-item-color);\n}\n.noti-icon .badge {\n  position: absolute;\n  top: 12px;\n  right: 4px;\n}\n\n.notification-item .d-flex {\n  padding: 0.75rem 1rem;\n}\n.notification-item .d-flex:hover {\n  background-color: var(--bs-tertiary-bg);\n}\n\n.dropdown-icon-item {\n  display: block;\n  border-radius: 3px;\n  line-height: 34px;\n  text-align: center;\n  padding: 15px 0 9px;\n  display: block;\n  border: 1px solid transparent;\n  color: var(--bs-secondary-color);\n}\n.dropdown-icon-item img {\n  height: 24px;\n}\n.dropdown-icon-item span {\n  display: block;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n.dropdown-icon-item:hover {\n  border-color: var(--bs-border-color);\n}\n\n.fullscreen-enable [data-bs-toggle=fullscreen] .bx-fullscreen::before {\n  content: \"\\ea3f\";\n}\n\nbody[data-topbar=dark] #page-topbar {\n  background-color: var(--bs-header-dark-bg);\n}\nbody[data-topbar=dark] .navbar-header .dropdown .show.header-item {\n  background-color: rgba(255, 255, 255, 0.05);\n}\nbody[data-topbar=dark] .navbar-header .waves-effect .waves-ripple {\n  background: rgba(255, 255, 255, 0.4);\n}\nbody[data-topbar=dark] .header-item {\n  color: var(--bs-header-dark-item-color);\n}\nbody[data-topbar=dark] .header-item:hover {\n  color: var(--bs-header-dark-item-color);\n}\nbody[data-topbar=dark] .header-profile-user {\n  background-color: rgba(255, 255, 255, 0.25);\n}\nbody[data-topbar=dark] .noti-icon i {\n  color: var(--bs-header-dark-item-color);\n}\nbody[data-topbar=dark] .logo-dark {\n  display: none;\n}\nbody[data-topbar=dark] .logo-light {\n  display: block;\n}\nbody[data-topbar=dark] .app-search .form-control {\n  background-color: rgba(var(--bs-topbar-search-bg), 0.07);\n  color: #fff;\n}\nbody[data-topbar=dark] .app-search span,\nbody[data-topbar=dark] .app-search input.form-control::-webkit-input-placeholder {\n  color: rgba(255, 255, 255, 0.5);\n}\n\nbody[data-sidebar=dark] .navbar-brand-box {\n  background: #2a3042;\n}\nbody[data-sidebar=dark] .logo-dark {\n  display: none;\n}\nbody[data-sidebar=dark] .logo-light {\n  display: block;\n}\n\n@media (max-width: 600px) {\n  .navbar-header .dropdown {\n    position: static;\n  }\n  .navbar-header .dropdown .dropdown-menu {\n    left: 10px !important;\n    right: 10px !important;\n  }\n}\n@media (max-width: 380px) {\n  .navbar-brand-box {\n    display: none;\n  }\n}\nbody[data-layout=horizontal] .navbar-brand-box {\n  width: auto;\n}\nbody[data-layout=horizontal] .page-content {\n  margin-top: 70px;\n  padding: calc(55px + 24px) calc(24px * 0.5) 60px calc(24px * 0.5);\n}\n\n@media (max-width: 992px) {\n  body[data-layout=horizontal] .page-content {\n    margin-top: 15px;\n  }\n}\n.page-title-box {\n  padding-bottom: 24px;\n}\n.page-title-box .breadcrumb {\n  background-color: transparent;\n  padding: 0;\n}\n.page-title-box h4 {\n  text-transform: uppercase;\n  font-weight: 600;\n  font-size: 16px !important;\n}\n\n.footer {\n  bottom: 0;\n  padding: 20px calc(24px * 0.75);\n  position: absolute;\n  right: 0;\n  color: var(--bs-footer-color);\n  left: 250px;\n  height: 60px;\n  background-color: var(--bs-footer-bg);\n}\n@media (max-width: 991.98px) {\n  .footer {\n    left: 0;\n  }\n}\n\n.vertical-collpsed .footer {\n  left: 70px;\n}\n@media (max-width: 991.98px) {\n  .vertical-collpsed .footer {\n    left: 0;\n  }\n}\n\nbody[data-layout=horizontal] .footer {\n  left: 0 !important;\n}\n\n.right-bar {\n  background-color: var(--bs-secondary-bg);\n  box-shadow: 0 0 24px 0 rgba(0, 0, 0, 0.06), 0 1px 0 0 rgba(0, 0, 0, 0.02);\n  display: block;\n  position: fixed;\n  transition: all 200ms ease-out;\n  width: 280px;\n  z-index: 9999;\n  float: right !important;\n  right: -290px;\n  top: 0;\n  bottom: 0;\n}\n.right-bar .right-bar-toggle {\n  background-color: var(--bs-dark);\n  height: 24px;\n  width: 24px;\n  line-height: 24px;\n  display: block;\n  color: var(--bs-gray-200);\n  text-align: center;\n  border-radius: 50%;\n}\n.right-bar .right-bar-toggle:hover {\n  background-color: var(--bs-dark);\n}\n\n.rightbar-overlay {\n  background-color: rgba(52, 58, 64, 0.55);\n  position: absolute;\n  left: 0;\n  right: 0;\n  top: 0;\n  bottom: 0;\n  display: none;\n  z-index: 9998;\n  transition: all 0.2s ease-out;\n}\n\n.right-bar-enabled .right-bar {\n  right: 0;\n}\n.right-bar-enabled .rightbar-overlay {\n  display: block;\n}\n\n@media (max-width: 767.98px) {\n  .right-bar {\n    overflow: auto;\n  }\n  .right-bar .slimscroll-menu {\n    height: auto !important;\n  }\n}\n.metismenu {\n  margin: 0;\n}\n.metismenu li {\n  display: block;\n  width: 100%;\n}\n.metismenu .mm-collapse {\n  display: none;\n}\n.metismenu .mm-collapse:not(.mm-show) {\n  display: none;\n}\n.metismenu .mm-collapse.mm-show {\n  display: block;\n}\n.metismenu .mm-collapsing {\n  position: relative;\n  height: 0;\n  overflow: hidden;\n  transition-timing-function: ease;\n  transition-duration: 0.35s;\n  transition-property: height, visibility;\n}\n\n.vertical-menu {\n  width: 250px;\n  z-index: 1001;\n  background: #ffffff;\n  bottom: 0;\n  margin-top: 0;\n  position: fixed;\n  top: 70px;\n  box-shadow: 0 0.75rem 1.5rem rgba(18, 38, 63, 0.03);\n}\n\n.main-content {\n  margin-left: 250px;\n  overflow: hidden;\n}\n.main-content .content {\n  padding: 0 15px 10px 15px;\n  margin-top: 70px;\n}\n\n#sidebar-menu {\n  padding: 10px 0 30px 0;\n}\n#sidebar-menu .mm-active > .has-arrow:after {\n  transform: rotate(-180deg);\n}\n#sidebar-menu .has-arrow:after {\n  content: \"\\f0140\";\n  font-family: \"Material Design Icons\";\n  display: block;\n  float: right;\n  transition: transform 0.2s;\n  font-size: 1rem;\n}\n#sidebar-menu ul li a {\n  display: block;\n  padding: 0.625rem 1.5rem;\n  color: #545a6d;\n  position: relative;\n  font-size: 13px;\n  transition: all 0.4s;\n}\n#sidebar-menu ul li a i {\n  display: inline-block;\n  min-width: 1.75rem;\n  padding-bottom: 0.125em;\n  font-size: 1.25rem;\n  line-height: 1.40625rem;\n  vertical-align: middle;\n  color: #7f8387;\n  transition: all 0.4s;\n}\n#sidebar-menu ul li a:hover {\n  color: #383c40;\n}\n#sidebar-menu ul li a:hover i {\n  color: #383c40;\n}\n#sidebar-menu ul li .badge {\n  margin-top: 4px;\n}\n#sidebar-menu ul li ul.sub-menu {\n  padding: 0;\n}\n#sidebar-menu ul li ul.sub-menu li a {\n  padding: 0.4rem 1.5rem 0.4rem 3.5rem;\n  font-size: 13px;\n  color: #545a6d;\n}\n#sidebar-menu ul li ul.sub-menu li a:hover {\n  color: #383c40;\n}\n#sidebar-menu ul li ul.sub-menu li ul.sub-menu {\n  padding: 0;\n}\n#sidebar-menu ul li ul.sub-menu li ul.sub-menu li a {\n  padding: 0.4rem 1.5rem 0.4rem 4.5rem;\n  font-size: 13px;\n}\n\n.menu-title {\n  padding: 12px 20px !important;\n  letter-spacing: 0.05em;\n  pointer-events: none;\n  cursor: default;\n  font-size: 11px;\n  text-transform: uppercase;\n  color: #7f8387;\n  font-weight: 600;\n}\n\n.mm-active {\n  color: #556ee6 !important;\n}\n.mm-active > a {\n  color: #556ee6 !important;\n}\n.mm-active > a i {\n  color: #556ee6 !important;\n}\n.mm-active .active {\n  color: #556ee6 !important;\n}\n.mm-active .active i {\n  color: #556ee6 !important;\n}\n.mm-active > i {\n  color: #556ee6 !important;\n}\n\n@media (max-width: 992px) {\n  .vertical-menu {\n    display: none;\n  }\n  .main-content {\n    margin-left: 0 !important;\n  }\n  body.sidebar-enable .vertical-menu {\n    display: block;\n  }\n}\n.vertical-collpsed .main-content {\n  margin-left: 70px;\n}\n.vertical-collpsed .navbar-brand-box {\n  width: 70px !important;\n}\n.vertical-collpsed .logo span.logo-lg {\n  display: none;\n}\n.vertical-collpsed .logo span.logo-sm {\n  display: block;\n}\n.vertical-collpsed .vertical-menu {\n  position: absolute;\n  width: 70px !important;\n  z-index: 5;\n}\n.vertical-collpsed .vertical-menu .simplebar-mask,\n.vertical-collpsed .vertical-menu .simplebar-content-wrapper {\n  overflow: visible !important;\n}\n.vertical-collpsed .vertical-menu .simplebar-scrollbar {\n  display: none !important;\n}\n.vertical-collpsed .vertical-menu .simplebar-offset {\n  bottom: 0 !important;\n}\n.vertical-collpsed .vertical-menu #sidebar-menu .menu-title,\n.vertical-collpsed .vertical-menu #sidebar-menu .badge,\n.vertical-collpsed .vertical-menu #sidebar-menu .collapse.in {\n  display: none !important;\n}\n.vertical-collpsed .vertical-menu #sidebar-menu .nav.collapse {\n  height: inherit !important;\n}\n.vertical-collpsed .vertical-menu #sidebar-menu .has-arrow:after {\n  display: none;\n}\n.vertical-collpsed .vertical-menu #sidebar-menu > ul > li {\n  position: relative;\n  white-space: nowrap;\n}\n.vertical-collpsed .vertical-menu #sidebar-menu > ul > li > a {\n  padding: 15px 20px;\n  min-height: 55px;\n  transition: none;\n}\n.vertical-collpsed .vertical-menu #sidebar-menu > ul > li > a:hover, .vertical-collpsed .vertical-menu #sidebar-menu > ul > li > a:active, .vertical-collpsed .vertical-menu #sidebar-menu > ul > li > a:focus {\n  color: #383c40;\n}\n.vertical-collpsed .vertical-menu #sidebar-menu > ul > li > a i {\n  font-size: 1.45rem;\n  margin-left: 4px;\n}\n.vertical-collpsed .vertical-menu #sidebar-menu > ul > li > a span {\n  display: none;\n  padding-left: 25px;\n}\n.vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > a {\n  position: relative;\n  width: calc(190px + 70px);\n  color: #556ee6;\n  background-color: whitesmoke;\n  transition: none;\n}\n.vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > a i {\n  color: #556ee6;\n}\n.vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > a span {\n  display: inline;\n}\n.vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > ul {\n  display: block;\n  left: 70px;\n  position: absolute;\n  width: 190px;\n  height: auto !important;\n  box-shadow: 3px 5px 10px 0 rgba(54, 61, 71, 0.1);\n}\n.vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > ul ul {\n  box-shadow: 3px 5px 10px 0 rgba(54, 61, 71, 0.1);\n}\n.vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > ul a {\n  box-shadow: none;\n  padding: 8px 20px;\n  position: relative;\n  width: 190px;\n  z-index: 6;\n  color: #545a6d;\n}\n.vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > ul a:hover {\n  color: #383c40;\n}\n.vertical-collpsed .vertical-menu #sidebar-menu > ul ul {\n  padding: 5px 0;\n  z-index: 9999;\n  display: none;\n  background-color: #ffffff;\n}\n.vertical-collpsed .vertical-menu #sidebar-menu > ul ul li:hover > ul {\n  display: block;\n  left: 190px;\n  height: auto !important;\n  margin-top: -36px;\n  position: absolute;\n  width: 190px;\n}\n.vertical-collpsed .vertical-menu #sidebar-menu > ul ul li > a span.pull-right {\n  position: absolute;\n  right: 20px;\n  top: 12px;\n  transform: rotate(270deg);\n}\n.vertical-collpsed .vertical-menu #sidebar-menu > ul ul li.active a {\n  color: #f8f9fa;\n}\n\nbody[data-sidebar=dark] .vertical-menu {\n  background: #2a3042;\n}\nbody[data-sidebar=dark] #sidebar-menu ul li a {\n  color: #a6b0cf;\n}\nbody[data-sidebar=dark] #sidebar-menu ul li a i {\n  color: #6a7187;\n}\nbody[data-sidebar=dark] #sidebar-menu ul li a:hover {\n  color: #ffffff;\n}\nbody[data-sidebar=dark] #sidebar-menu ul li a:hover i {\n  color: #ffffff;\n}\nbody[data-sidebar=dark] #sidebar-menu ul li ul.sub-menu li a {\n  color: #79829c;\n}\nbody[data-sidebar=dark] #sidebar-menu ul li ul.sub-menu li a:hover {\n  color: #ffffff;\n}\nbody[data-sidebar=dark].vertical-collpsed {\n  min-height: 1760px;\n}\nbody[data-sidebar=dark].vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > a {\n  background: #2e3548;\n  color: #ffffff;\n}\nbody[data-sidebar=dark].vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > a i {\n  color: #ffffff;\n}\nbody[data-sidebar=dark].vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > ul a {\n  color: #79829c;\n}\nbody[data-sidebar=dark].vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > ul a:hover {\n  color: #ffffff;\n}\nbody[data-sidebar=dark].vertical-collpsed .vertical-menu #sidebar-menu > ul ul {\n  background-color: #2a3042;\n}\nbody[data-sidebar=dark].vertical-collpsed .vertical-menu #sidebar-menu ul li.mm-active .active {\n  color: #ffffff !important;\n}\nbody[data-sidebar=dark].vertical-collpsed .vertical-menu #sidebar-menu ul li.mm-active .active i {\n  color: #ffffff !important;\n}\nbody[data-sidebar=dark] .mm-active {\n  color: #ffffff !important;\n}\nbody[data-sidebar=dark] .mm-active > a {\n  color: #ffffff !important;\n}\nbody[data-sidebar=dark] .mm-active > a i {\n  color: #ffffff !important;\n}\nbody[data-sidebar=dark] .mm-active > i {\n  color: #ffffff !important;\n}\nbody[data-sidebar=dark] .mm-active .active {\n  color: #ffffff !important;\n}\nbody[data-sidebar=dark] .mm-active .active i {\n  color: #ffffff !important;\n}\nbody[data-sidebar=dark] .menu-title {\n  color: #6a7187;\n}\n\nbody[data-layout=horizontal] .main-content {\n  margin-left: 0 !important;\n}\n\nbody[data-sidebar-size=small] .navbar-brand-box {\n  width: 160px;\n}\nbody[data-sidebar-size=small] .vertical-menu {\n  width: 160px;\n  text-align: center;\n}\nbody[data-sidebar-size=small] .vertical-menu .has-arrow:after,\nbody[data-sidebar-size=small] .vertical-menu .badge {\n  display: none !important;\n}\nbody[data-sidebar-size=small] .main-content {\n  margin-left: 160px;\n}\nbody[data-sidebar-size=small] .footer {\n  left: 160px;\n}\n@media (max-width: 991.98px) {\n  body[data-sidebar-size=small] .footer {\n    left: 0;\n  }\n}\nbody[data-sidebar-size=small] #sidebar-menu ul li.menu-title {\n  background-color: #2e3548;\n}\nbody[data-sidebar-size=small] #sidebar-menu ul li a i {\n  display: block;\n}\nbody[data-sidebar-size=small] #sidebar-menu ul li ul.sub-menu li a {\n  padding-left: 1.5rem;\n}\nbody[data-sidebar-size=small] #sidebar-menu ul li ul.sub-menu li ul.sub-menu li a {\n  padding-left: 1.5rem;\n}\nbody[data-sidebar-size=small].vertical-collpsed .main-content {\n  margin-left: 70px;\n}\nbody[data-sidebar-size=small].vertical-collpsed .vertical-menu #sidebar-menu {\n  text-align: left;\n}\nbody[data-sidebar-size=small].vertical-collpsed .vertical-menu #sidebar-menu > ul > li > a i {\n  display: inline-block;\n}\nbody[data-sidebar-size=small].vertical-collpsed .footer {\n  left: 70px;\n}\n\nbody[data-sidebar=colored] .vertical-menu {\n  background-color: #556ee6;\n}\nbody[data-sidebar=colored] .navbar-brand-box {\n  background-color: #556ee6;\n}\nbody[data-sidebar=colored] .navbar-brand-box .logo-dark {\n  display: none;\n}\nbody[data-sidebar=colored] .navbar-brand-box .logo-light {\n  display: block;\n}\nbody[data-sidebar=colored] .mm-active {\n  color: #fff !important;\n}\nbody[data-sidebar=colored] .mm-active > a {\n  color: #fff !important;\n}\nbody[data-sidebar=colored] .mm-active > a i {\n  color: #fff !important;\n}\nbody[data-sidebar=colored] .mm-active > i, body[data-sidebar=colored] .mm-active .active {\n  color: #fff !important;\n}\nbody[data-sidebar=colored] #sidebar-menu ul li.menu-title {\n  color: rgba(255, 255, 255, 0.6);\n}\nbody[data-sidebar=colored] #sidebar-menu ul li a {\n  color: rgba(255, 255, 255, 0.6);\n}\nbody[data-sidebar=colored] #sidebar-menu ul li a i {\n  color: rgba(255, 255, 255, 0.6);\n}\nbody[data-sidebar=colored] #sidebar-menu ul li a.waves-effect .waves-ripple {\n  background: rgba(255, 255, 255, 0.1);\n}\nbody[data-sidebar=colored] #sidebar-menu ul li a:hover {\n  color: #fff;\n}\nbody[data-sidebar=colored] #sidebar-menu ul li a:hover i {\n  color: #fff;\n}\nbody[data-sidebar=colored] #sidebar-menu ul li ul.sub-menu li a {\n  color: rgba(255, 255, 255, 0.5);\n}\nbody[data-sidebar=colored] #sidebar-menu ul li ul.sub-menu li a:hover {\n  color: #fff;\n}\nbody[data-sidebar=colored].vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > a {\n  background-color: #5e76e7;\n  color: #fff;\n}\nbody[data-sidebar=colored].vertical-collpsed .vertical-menu #sidebar-menu > ul > li:hover > a i {\n  color: #fff;\n}\nbody[data-sidebar=colored].vertical-collpsed .vertical-menu #sidebar-menu ul li.mm-active .active {\n  color: #556ee6 !important;\n}\nbody[data-sidebar=colored].vertical-collpsed .vertical-menu #sidebar-menu ul li ul.sub-menu li a:hover {\n  color: #556ee6;\n}\nbody[data-sidebar=colored].vertical-collpsed .vertical-menu #sidebar-menu ul li ul.sub-menu li.mm-active {\n  color: #556ee6 !important;\n}\nbody[data-sidebar=colored].vertical-collpsed .vertical-menu #sidebar-menu ul li ul.sub-menu li.mm-active > a {\n  color: #556ee6 !important;\n}\nbody[data-sidebar=colored].vertical-collpsed .vertical-menu #sidebar-menu ul li ul.sub-menu li.mm-active > a i {\n  color: #556ee6 !important;\n}\n\n.topnav {\n  background: var(--bs-topnav-bg);\n  padding: 0 calc(24px * 0.5);\n  box-shadow: 0 0.75rem 1.5rem rgba(18, 38, 63, 0.03);\n  margin-top: 70px;\n  position: fixed;\n  left: 0;\n  right: 0;\n  z-index: 100;\n}\n.topnav .topnav-menu {\n  margin: 0;\n  padding: 0;\n}\n.topnav .navbar-nav .nav-link {\n  font-size: 14px;\n  position: relative;\n  padding: 1rem 1.3rem;\n  color: var(--bs-menu-item-color);\n}\n.topnav .navbar-nav .nav-link i {\n  font-size: 15px;\n}\n.topnav .navbar-nav .nav-link:focus, .topnav .navbar-nav .nav-link:hover {\n  color: var(--bs-menu-item-active-color);\n  background-color: transparent;\n}\n.topnav .navbar-nav .dropdown-item {\n  color: var(--bs-menu-item-color);\n}\n.topnav .navbar-nav .dropdown-item.active, .topnav .navbar-nav .dropdown-item:hover {\n  color: var(--bs-menu-item-active-color);\n}\n.topnav .navbar-nav .nav-item .nav-link.active {\n  color: var(--bs-menu-item-active-color);\n}\n.topnav .navbar-nav .dropdown.active > a {\n  color: var(--bs-menu-item-active-color);\n  background-color: transparent;\n}\n\n@media (min-width: 1200px) {\n  body[data-layout=horizontal] .container-fluid,\n  body[data-layout=horizontal] .navbar-header {\n    max-width: 85%;\n  }\n}\n@media (min-width: 992px) {\n  .topnav .navbar-nav .nav-item:first-of-type .nav-link {\n    padding-left: 0;\n  }\n  .topnav .dropdown-item {\n    padding: 0.5rem 1.5rem;\n    min-width: 180px;\n  }\n  .topnav .dropdown.mega-dropdown .mega-dropdown-menu {\n    left: 0px;\n    right: auto;\n  }\n  .topnav .dropdown .dropdown-menu {\n    margin-top: 0;\n    border-radius: 0 0 var(--bs-border-radius) var(--bs-border-radius);\n  }\n  .topnav .dropdown .dropdown-menu .arrow-down::after {\n    right: 15px;\n    transform: rotate(-135deg) translateY(-50%);\n    position: absolute;\n  }\n  .topnav .dropdown .dropdown-menu .dropdown .dropdown-menu {\n    position: absolute;\n    top: 0 !important;\n    left: 100%;\n    display: none;\n  }\n  .topnav .dropdown:hover > .dropdown-menu {\n    display: block;\n  }\n  .topnav .dropdown:hover > .dropdown-menu > .dropdown:hover > .dropdown-menu {\n    display: block;\n  }\n  .navbar-toggle {\n    display: none;\n  }\n}\n.arrow-down {\n  display: inline-block;\n}\n.arrow-down:after {\n  border-color: initial;\n  border-style: solid;\n  border-width: 0 0 1px 1px;\n  content: \"\";\n  height: 0.4em;\n  display: inline-block;\n  right: 5px;\n  top: 50%;\n  margin-left: 10px;\n  transform: rotate(-45deg) translateY(-50%);\n  transform-origin: top;\n  transition: all 0.3s ease-out;\n  width: 0.4em;\n}\n\n@media (max-width: 1199.98px) {\n  .topnav-menu .navbar-nav li:last-of-type .dropdown .dropdown-menu {\n    right: 100%;\n    left: auto;\n  }\n}\n@media (max-width: 991.98px) {\n  .navbar-brand-box .logo-dark {\n    display: var(--bs-display-block);\n  }\n  .navbar-brand-box .logo-dark span.logo-sm {\n    display: var(--bs-display-block);\n  }\n  .navbar-brand-box .logo-light {\n    display: var(--bs-display-none);\n  }\n  .topnav {\n    max-height: 360px;\n    overflow-y: auto;\n    padding: 0;\n  }\n  .topnav .navbar-nav .nav-link {\n    padding: 0.75rem 1.1rem;\n  }\n  .topnav .dropdown .dropdown-menu {\n    background-color: transparent;\n    border: none;\n    box-shadow: none;\n    padding-left: 15px;\n  }\n  .topnav .dropdown .dropdown-menu.dropdown-mega-menu-xl {\n    width: auto;\n  }\n  .topnav .dropdown .dropdown-menu.dropdown-mega-menu-xl .row {\n    margin: 0px;\n  }\n  .topnav .dropdown .dropdown-item {\n    position: relative;\n    background-color: transparent;\n  }\n  .topnav .dropdown .dropdown-item.active, .topnav .dropdown .dropdown-item:active {\n    color: #556ee6;\n  }\n  .topnav .arrow-down::after {\n    right: 15px;\n    position: absolute;\n  }\n}\n@media (min-width: 992px) {\n  body[data-layout=horizontal][data-topbar=light] .navbar-brand-box .logo-dark {\n    display: var(--bs-display-block);\n  }\n  body[data-layout=horizontal][data-topbar=light] .navbar-brand-box .logo-light {\n    display: var(--bs-display-none);\n  }\n  body[data-layout=horizontal][data-topbar=light] .topnav {\n    background-color: #556ee6;\n  }\n  body[data-layout=horizontal][data-topbar=light] .topnav .navbar-nav .nav-link {\n    color: rgba(255, 255, 255, 0.6);\n  }\n  body[data-layout=horizontal][data-topbar=light] .topnav .navbar-nav .nav-link:focus, body[data-layout=horizontal][data-topbar=light] .topnav .navbar-nav .nav-link:hover {\n    color: rgba(255, 255, 255, 0.9);\n  }\n  body[data-layout=horizontal][data-topbar=light] .topnav .navbar-nav > .dropdown.active > a {\n    color: rgba(255, 255, 255, 0.9) !important;\n  }\n}\nbody[data-layout=horizontal][data-topbar=colored] #page-topbar {\n  background-color: #556ee6;\n  box-shadow: none;\n}\nbody[data-layout=horizontal][data-topbar=colored] .logo-dark {\n  display: none;\n}\nbody[data-layout=horizontal][data-topbar=colored] .logo-light {\n  display: block;\n}\nbody[data-layout=horizontal][data-topbar=colored] .app-search .form-control {\n  background-color: rgba(var(--bs-topbar-search-bg), 0.07);\n  color: #fff;\n}\nbody[data-layout=horizontal][data-topbar=colored] .app-search span,\nbody[data-layout=horizontal][data-topbar=colored] .app-search input.form-control::-webkit-input-placeholder {\n  color: rgba(255, 255, 255, 0.5);\n}\nbody[data-layout=horizontal][data-topbar=colored] .header-item {\n  color: var(--bs-header-dark-item-color);\n}\nbody[data-layout=horizontal][data-topbar=colored] .header-item:hover {\n  color: var(--bs-header-dark-item-color);\n}\nbody[data-layout=horizontal][data-topbar=colored] .navbar-header .dropdown .show.header-item {\n  background-color: rgba(255, 255, 255, 0.1);\n}\nbody[data-layout=horizontal][data-topbar=colored] .navbar-header .waves-effect .waves-ripple {\n  background: rgba(255, 255, 255, 0.4);\n}\nbody[data-layout=horizontal][data-topbar=colored] .noti-icon i {\n  color: var(--bs-header-dark-item-color);\n}\n@media (min-width: 992px) {\n  body[data-layout=horizontal][data-topbar=colored] .topnav {\n    background-color: #556ee6;\n  }\n  body[data-layout=horizontal][data-topbar=colored] .topnav .navbar-nav .nav-link {\n    color: rgba(255, 255, 255, 0.6);\n  }\n  body[data-layout=horizontal][data-topbar=colored] .topnav .navbar-nav .nav-link:focus, body[data-layout=horizontal][data-topbar=colored] .topnav .navbar-nav .nav-link:hover {\n    color: rgba(255, 255, 255, 0.9);\n  }\n  body[data-layout=horizontal][data-topbar=colored] .topnav .navbar-nav > .dropdown.active > a {\n    color: rgba(255, 255, 255, 0.9) !important;\n  }\n}\n\nbody[data-layout-size=boxed] {\n  background-color: var(--bs-boxed-body-bg);\n}\nbody[data-layout-size=boxed] #layout-wrapper {\n  background-color: var(--bs-body-bg);\n  max-width: 1300px;\n  margin: 0 auto;\n  box-shadow: 0 0.75rem 1.5rem rgba(18, 38, 63, 0.03);\n}\nbody[data-layout-size=boxed] #page-topbar {\n  max-width: 1300px;\n  margin: 0 auto;\n}\nbody[data-layout-size=boxed] .footer {\n  margin: 0 auto;\n  max-width: calc(1300px - 250px);\n}\nbody[data-layout-size=boxed].vertical-collpsed .footer {\n  max-width: calc(1300px - 70px);\n}\n\nbody[data-layout=horizontal][data-layout-size=boxed] #page-topbar, body[data-layout=horizontal][data-layout-size=boxed] #layout-wrapper, body[data-layout=horizontal][data-layout-size=boxed] .footer {\n  max-width: 100%;\n}\nbody[data-layout=horizontal][data-layout-size=boxed] .container-fluid, body[data-layout=horizontal][data-layout-size=boxed] .navbar-header {\n  max-width: 1300px;\n}\n\n@media (min-width: 992px) {\n  body[data-layout-scrollable=true] #page-topbar, body[data-layout-scrollable=true] .vertical-menu {\n    position: absolute;\n  }\n}\n@media (min-width: 992px) {\n  body[data-layout-scrollable=true][data-layout=horizontal] #page-topbar, body[data-layout-scrollable=true][data-layout=horizontal] .topnav {\n    position: absolute;\n  }\n}\n\n/*!\n * Waves v0.7.6\n * http://fian.my.id/Waves \n * \n * Copyright 2014-2018 Alfiana E. Sibuea and other contributors \n * Released under the MIT license \n * https://github.com/fians/Waves/blob/master/LICENSE */\n.waves-effect {\n  position: relative;\n  cursor: pointer;\n  display: inline-block;\n  overflow: hidden;\n  -webkit-user-select: none;\n  -moz-user-select: none;\n  -ms-user-select: none;\n  user-select: none;\n  -webkit-tap-highlight-color: transparent;\n}\n\n.waves-effect .waves-ripple {\n  position: absolute;\n  border-radius: 50%;\n  width: 100px;\n  height: 100px;\n  margin-top: -50px;\n  margin-left: -50px;\n  opacity: 0;\n  background: rgba(0, 0, 0, 0.2);\n  background: -webkit-radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  background: -o-radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  background: -moz-radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  background: radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  -webkit-transition: all 0.5s ease-out;\n  -moz-transition: all 0.5s ease-out;\n  -o-transition: all 0.5s ease-out;\n  transition: all 0.5s ease-out;\n  -webkit-transition-property: -webkit-transform, opacity;\n  -moz-transition-property: -moz-transform, opacity;\n  -o-transition-property: -o-transform, opacity;\n  transition-property: transform, opacity;\n  -webkit-transform: scale(0) translate(0, 0);\n  -moz-transform: scale(0) translate(0, 0);\n  -ms-transform: scale(0) translate(0, 0);\n  -o-transform: scale(0) translate(0, 0);\n  transform: scale(0) translate(0, 0);\n  pointer-events: none;\n}\n\n.waves-effect.waves-light .waves-ripple {\n  background: rgba(255, 255, 255, 0.4);\n  background: -webkit-radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  background: -o-radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  background: -moz-radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n  background: radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\n}\n\n.waves-effect.waves-classic .waves-ripple {\n  background: rgba(0, 0, 0, 0.2);\n}\n\n.waves-effect.waves-classic.waves-light .waves-ripple {\n  background: rgba(255, 255, 255, 0.4);\n}\n\n.waves-notransition {\n  -webkit-transition: none !important;\n  -moz-transition: none !important;\n  -o-transition: none !important;\n  transition: none !important;\n}\n\n.waves-button,\n.waves-circle {\n  -webkit-transform: translateZ(0);\n  -moz-transform: translateZ(0);\n  -ms-transform: translateZ(0);\n  -o-transform: translateZ(0);\n  transform: translateZ(0);\n  -webkit-mask-image: -webkit-radial-gradient(circle, white 100%, black 100%);\n}\n\n.waves-button,\n.waves-button:hover,\n.waves-button:visited,\n.waves-button-input {\n  white-space: nowrap;\n  vertical-align: middle;\n  cursor: pointer;\n  border: none;\n  outline: none;\n  color: inherit;\n  background-color: rgba(0, 0, 0, 0);\n  font-size: 1em;\n  line-height: 1em;\n  text-align: center;\n  text-decoration: none;\n  z-index: 1;\n}\n\n.waves-button {\n  padding: 0.85em 1.1em;\n  border-radius: 0.2em;\n}\n\n.waves-button-input {\n  margin: 0;\n  padding: 0.85em 1.1em;\n}\n\n.waves-input-wrapper {\n  border-radius: 0.2em;\n  vertical-align: bottom;\n}\n\n.waves-input-wrapper.waves-button {\n  padding: 0;\n}\n\n.waves-input-wrapper .waves-button-input {\n  position: relative;\n  top: 0;\n  left: 0;\n  z-index: 1;\n}\n\n.waves-circle {\n  text-align: center;\n  width: 2.5em;\n  height: 2.5em;\n  line-height: 2.5em;\n  border-radius: 50%;\n}\n\n.waves-float {\n  -webkit-mask-image: none;\n  -webkit-box-shadow: 0px 1px 1.5px 1px rgba(0, 0, 0, 0.12);\n  box-shadow: 0px 1px 1.5px 1px rgba(0, 0, 0, 0.12);\n  -webkit-transition: all 300ms;\n  -moz-transition: all 300ms;\n  -o-transition: all 300ms;\n  transition: all 300ms;\n}\n\n.waves-float:active {\n  -webkit-box-shadow: 0px 8px 20px 1px rgba(0, 0, 0, 0.3);\n  box-shadow: 0px 8px 20px 1px rgba(0, 0, 0, 0.3);\n}\n\n.waves-block {\n  display: block;\n}\n\n.waves-effect.waves-light .waves-ripple {\n  background-color: rgba(255, 255, 255, 0.4);\n}\n\n.waves-effect.waves-primary .waves-ripple {\n  background-color: rgba(85, 110, 230, 0.4);\n}\n\n.waves-effect.waves-success .waves-ripple {\n  background-color: rgba(52, 195, 143, 0.4);\n}\n\n.waves-effect.waves-info .waves-ripple {\n  background-color: rgba(80, 165, 241, 0.4);\n}\n\n.waves-effect.waves-warning .waves-ripple {\n  background-color: rgba(241, 180, 76, 0.4);\n}\n\n.waves-effect.waves-danger .waves-ripple {\n  background-color: rgba(244, 106, 106, 0.4);\n}\n\n.avatar-xs {\n  height: 2rem;\n  width: 2rem;\n}\n\n.avatar-sm {\n  height: 3rem;\n  width: 3rem;\n}\n\n.avatar-md {\n  height: 4.5rem;\n  width: 4.5rem;\n}\n\n.avatar-lg {\n  height: 6rem;\n  width: 6rem;\n}\n\n.avatar-xl {\n  height: 7.5rem;\n  width: 7.5rem;\n}\n\n.avatar-title {\n  align-items: center;\n  background-color: #556ee6;\n  color: #fff;\n  display: flex;\n  font-weight: 500;\n  height: 100%;\n  justify-content: center;\n  width: 100%;\n}\n\n.avatar-group {\n  padding-left: 12px;\n  display: flex;\n  flex-wrap: wrap;\n}\n.avatar-group .avatar-group-item {\n  margin-left: -12px;\n  border: 2px solid var(--bs-secondary-bg);\n  border-radius: 50%;\n  transition: all 0.2s;\n}\n.avatar-group .avatar-group-item:hover {\n  position: relative;\n  transform: translateY(-2px);\n}\n\n.custom-accordion .accordion-list {\n  display: flex;\n  border-radius: 7px;\n  background-color: var(--bs-tertiary-bg);\n  padding: 12px 20px;\n  color: var(--bs-body-color);\n  font-weight: 600;\n  align-items: center;\n  justify-content: space-between;\n}\n.custom-accordion .accordion-list.collapsed i.accor-plus-icon:before {\n  content: \"\\f0415\";\n}\n.custom-accordion .accordion-list .accor-plus-icon {\n  display: inline-block;\n  font-size: 16px;\n  height: 24px;\n  width: 24px;\n  line-height: 22px;\n  background-color: var(--bs-secondary-bg);\n  text-align: center;\n  border-radius: 50%;\n}\n.custom-accordion a.collapsed i.accor-down-icon:before {\n  content: \"\\f0140\";\n}\n.custom-accordion .card-body {\n  color: var(--prefixsecondary-color);\n}\n\n.font-size-10 {\n  font-size: 10px !important;\n}\n\n.font-size-11 {\n  font-size: 11px !important;\n}\n\n.font-size-12 {\n  font-size: 12px !important;\n}\n\n.font-size-13 {\n  font-size: 13px !important;\n}\n\n.font-size-14 {\n  font-size: 14px !important;\n}\n\n.font-size-15 {\n  font-size: 15px !important;\n}\n\n.font-size-16 {\n  font-size: 16px !important;\n}\n\n.font-size-17 {\n  font-size: 17px !important;\n}\n\n.font-size-18 {\n  font-size: 18px !important;\n}\n\n.font-size-20 {\n  font-size: 20px !important;\n}\n\n.font-size-22 {\n  font-size: 22px !important;\n}\n\n.font-size-24 {\n  font-size: 24px !important;\n}\n\n.social-list-item {\n  height: 2rem;\n  width: 2rem;\n  line-height: calc(2rem - 4px);\n  display: block;\n  border: 2px solid #adb5bd;\n  border-radius: 50%;\n  color: #adb5bd;\n  text-align: center;\n  transition: all 0.4s;\n}\n.social-list-item:hover {\n  color: var(--bs-secondary-color);\n  background-color: #eff2f7;\n}\n\n.w-xs {\n  min-width: 80px;\n}\n\n.w-sm {\n  min-width: 95px;\n}\n\n.w-md {\n  min-width: 110px;\n}\n\n.w-lg {\n  min-width: 140px;\n}\n\n.w-xl {\n  min-width: 160px;\n}\n\n.alert-dismissible .btn-close {\n  font-size: 10px;\n  padding: 1.05rem 1.25rem;\n}\n\n.chartjs-chart {\n  max-height: 300px;\n}\n\n#preloader {\n  position: fixed;\n  top: 0;\n  left: 0;\n  right: 0;\n  bottom: 0;\n  background-color: var(--bs-secondary-bg);\n  z-index: 9999;\n}\n\n#status {\n  width: 40px;\n  height: 40px;\n  position: absolute;\n  left: 50%;\n  top: 50%;\n  margin: -20px 0 0 -20px;\n}\n\n.spinner-chase {\n  margin: 0 auto;\n  width: 40px;\n  height: 40px;\n  position: relative;\n  animation: spinner-chase 2.5s infinite linear both;\n}\n\n.chase-dot {\n  width: 100%;\n  height: 100%;\n  position: absolute;\n  left: 0;\n  top: 0;\n  animation: chase-dot 2s infinite ease-in-out both;\n}\n.chase-dot:before {\n  content: \"\";\n  display: block;\n  width: 25%;\n  height: 25%;\n  background-color: #556ee6;\n  border-radius: 100%;\n  animation: chase-dot-before 2s infinite ease-in-out both;\n}\n.chase-dot:nth-child(1) {\n  animation-delay: -1.1s;\n}\n.chase-dot:nth-child(1):before {\n  animation-delay: -1.1s;\n}\n.chase-dot:nth-child(2) {\n  animation-delay: -1s;\n}\n.chase-dot:nth-child(2):before {\n  animation-delay: -1s;\n}\n.chase-dot:nth-child(3) {\n  animation-delay: -0.9s;\n}\n.chase-dot:nth-child(3):before {\n  animation-delay: -0.9s;\n}\n.chase-dot:nth-child(4) {\n  animation-delay: -0.8s;\n}\n.chase-dot:nth-child(4):before {\n  animation-delay: -0.8s;\n}\n.chase-dot:nth-child(5) {\n  animation-delay: -0.7s;\n}\n.chase-dot:nth-child(5):before {\n  animation-delay: -0.7s;\n}\n.chase-dot:nth-child(6) {\n  animation-delay: -0.6s;\n}\n.chase-dot:nth-child(6):before {\n  animation-delay: -0.6s;\n}\n\n@keyframes spinner-chase {\n  100% {\n    transform: rotate(360deg);\n  }\n}\n@keyframes chase-dot {\n  80%, 100% {\n    transform: rotate(360deg);\n  }\n}\n@keyframes chase-dot-before {\n  50% {\n    transform: scale(0.4);\n  }\n  100%, 0% {\n    transform: scale(1);\n  }\n}\n[type=tel]::placeholder,\n[type=url]::placeholder,\n[type=email]::placeholder,\n[type=number]::placeholder {\n  text-align: left;\n}\n\n.form-check {\n  position: relative;\n  text-align: left;\n}\n\n.form-check-right {\n  padding-left: 0;\n  display: inline-block;\n  padding-right: 1.5em;\n}\n.form-check-right .form-check-input {\n  float: right;\n  margin-left: 0;\n  margin-right: -1.5em;\n}\n.form-check-right .form-check-label {\n  display: block;\n}\n\n.form-checkbox-outline .form-check-input {\n  border-width: 2px;\n  background-color: var(--bs-secondary-bg);\n}\n.form-checkbox-outline .form-check-input:active {\n  filter: none;\n}\n.form-checkbox-outline .form-check-input:checked {\n  background-color: var(--bs-secondary-bg) !important;\n}\n.form-checkbox-outline .form-check-input:checked[type=checkbox] {\n  background-image: none;\n}\n.form-checkbox-outline .form-check-input:checked:after {\n  position: absolute;\n  content: \"\\f012c\";\n  font-family: \"Material Design Icons\";\n  top: -4px !important;\n  left: 1px;\n  /*rtl: -4px */\n  font-size: 16px;\n  color: var(--bs-body-color);\n}\n\n.form-radio-outline .form-check-input {\n  background-color: var(--bs-secondary-bg);\n  position: relative;\n}\n.form-radio-outline .form-check-input:active {\n  filter: none;\n}\n.form-radio-outline .form-check-input:checked {\n  background-color: var(--bs-secondary-bg) !important;\n}\n.form-radio-outline .form-check-input:checked[type=checkbox] {\n  background-image: none;\n}\n.form-radio-outline .form-check-input:checked:after {\n  position: absolute;\n  content: \"\";\n  top: 3px !important;\n  left: 3px;\n  width: 5px;\n  height: 5px;\n  border-radius: 50%;\n}\n\n.form-check-primary .form-check-input:checked {\n  background-color: #556ee6;\n  border-color: #556ee6;\n}\n\n.form-radio-primary .form-check-input:checked {\n  border-color: #556ee6;\n  background-color: #556ee6;\n}\n.form-radio-primary .form-check-input:checked:after {\n  background-color: #556ee6;\n}\n\n.form-check-secondary .form-check-input:checked {\n  background-color: #74788d;\n  border-color: #74788d;\n}\n\n.form-radio-secondary .form-check-input:checked {\n  border-color: #74788d;\n  background-color: #74788d;\n}\n.form-radio-secondary .form-check-input:checked:after {\n  background-color: #74788d;\n}\n\n.form-check-success .form-check-input:checked {\n  background-color: #34c38f;\n  border-color: #34c38f;\n}\n\n.form-radio-success .form-check-input:checked {\n  border-color: #34c38f;\n  background-color: #34c38f;\n}\n.form-radio-success .form-check-input:checked:after {\n  background-color: #34c38f;\n}\n\n.form-check-info .form-check-input:checked {\n  background-color: #50a5f1;\n  border-color: #50a5f1;\n}\n\n.form-radio-info .form-check-input:checked {\n  border-color: #50a5f1;\n  background-color: #50a5f1;\n}\n.form-radio-info .form-check-input:checked:after {\n  background-color: #50a5f1;\n}\n\n.form-check-warning .form-check-input:checked {\n  background-color: #f1b44c;\n  border-color: #f1b44c;\n}\n\n.form-radio-warning .form-check-input:checked {\n  border-color: #f1b44c;\n  background-color: #f1b44c;\n}\n.form-radio-warning .form-check-input:checked:after {\n  background-color: #f1b44c;\n}\n\n.form-check-danger .form-check-input:checked {\n  background-color: #f46a6a;\n  border-color: #f46a6a;\n}\n\n.form-radio-danger .form-check-input:checked {\n  border-color: #f46a6a;\n  background-color: #f46a6a;\n}\n.form-radio-danger .form-check-input:checked:after {\n  background-color: #f46a6a;\n}\n\n.form-check-pink .form-check-input:checked {\n  background-color: #e83e8c;\n  border-color: #e83e8c;\n}\n\n.form-radio-pink .form-check-input:checked {\n  border-color: #e83e8c;\n  background-color: #e83e8c;\n}\n.form-radio-pink .form-check-input:checked:after {\n  background-color: #e83e8c;\n}\n\n.form-check-light .form-check-input:checked {\n  background-color: #eff2f7;\n  border-color: #eff2f7;\n}\n\n.form-radio-light .form-check-input:checked {\n  border-color: #eff2f7;\n  background-color: #eff2f7;\n}\n.form-radio-light .form-check-input:checked:after {\n  background-color: #eff2f7;\n}\n\n.form-check-dark .form-check-input:checked {\n  background-color: #343a40;\n  border-color: #343a40;\n}\n\n.form-radio-dark .form-check-input:checked {\n  border-color: #343a40;\n  background-color: #343a40;\n}\n.form-radio-dark .form-check-input:checked:after {\n  background-color: #343a40;\n}\n\n.form-check,\n.form-check-input,\n.form-check-label {\n  cursor: pointer;\n  margin-bottom: 0;\n}\n\n.form-switch-md {\n  padding-left: 2.5rem;\n  min-height: 24px;\n  line-height: 24px;\n}\n.form-switch-md .form-check-input {\n  width: 40px;\n  height: 20px;\n  left: -0.5rem;\n  position: relative;\n}\n.form-switch-md .form-check-label {\n  vertical-align: middle;\n}\n\n.form-switch-lg {\n  padding-left: 2.75rem;\n  min-height: 28px;\n  line-height: 28px;\n}\n.form-switch-lg .form-check-input {\n  width: 48px;\n  height: 24px;\n  left: -0.75rem;\n  position: relative;\n}\n\n.input-group-text {\n  margin-bottom: 0px;\n}\n\n.mini-stats-wid .mini-stat-icon {\n  overflow: hidden;\n  position: relative;\n}\n.mini-stats-wid .mini-stat-icon:before, .mini-stats-wid .mini-stat-icon:after {\n  content: \"\";\n  position: absolute;\n  width: 8px;\n  height: 54px;\n  background-color: rgba(255, 255, 255, 0.1);\n  left: 16px;\n  transform: rotate(32deg);\n  top: -5px;\n  transition: all 0.4s;\n}\n.mini-stats-wid .mini-stat-icon::after {\n  left: -12px;\n  width: 12px;\n  transition: all 0.2s;\n}\n.mini-stats-wid:hover .mini-stat-icon::after {\n  left: 60px;\n}\n\n.mfp-popup-form {\n  max-width: 1140px;\n}\n\n.mfp-close {\n  color: var(--bs-body-color) !important;\n}\n\n.bs-example-modal {\n  position: relative;\n  top: auto;\n  right: auto;\n  bottom: auto;\n  left: auto;\n  z-index: 1;\n  display: block;\n}\n\n[dir=rtl] .modal-open {\n  padding-left: 0px !important;\n}\n\n.icon-demo-content {\n  text-align: center;\n  color: #adb5bd;\n}\n.icon-demo-content i {\n  display: block;\n  font-size: 24px;\n  margin-bottom: 16px;\n  color: var(--prefixsecondary-color);\n  transition: all 0.4s;\n}\n.icon-demo-content .col-lg-4 {\n  margin-top: 24px;\n}\n.icon-demo-content .col-lg-4:hover i {\n  color: #556ee6;\n  transform: scale(1.5);\n}\n\n.grid-structure .grid-container {\n  background-color: var(--bs-gray-100);\n  margin-top: 10px;\n  font-size: 0.8rem;\n  font-weight: 500;\n  padding: 10px 20px;\n}\n\n.card-radio {\n  background-color: var(--bs-secondary-bg);\n  border: 2px solid var(--bs-border-color);\n  border-radius: var(--bs-border-radius);\n  padding: 1rem;\n  overflow: hidden;\n  text-overflow: ellipsis;\n  white-space: nowrap;\n}\n.card-radio:hover {\n  cursor: pointer;\n}\n\n.card-radio-label {\n  display: block;\n}\n\n.card-radio-input {\n  display: none;\n}\n.card-radio-input:checked + .card-radio {\n  border-color: #556ee6 !important;\n}\n\n.navs-carousel .owl-nav {\n  margin-top: 16px;\n}\n.navs-carousel .owl-nav button {\n  width: 30px;\n  height: 30px;\n  line-height: 28px !important;\n  font-size: 20px !important;\n  border-radius: 50% !important;\n  background-color: rgba(85, 110, 230, 0.25) !important;\n  color: #556ee6 !important;\n  margin: 4px 8px !important;\n}\n\n@media print {\n  .vertical-menu,\n  .right-bar,\n  .page-title-box,\n  .navbar-header,\n  .footer {\n    display: none !important;\n  }\n  .card-body,\n  .main-content,\n  .right-bar,\n  .page-content,\n  body {\n    padding: 0;\n    margin: 0;\n  }\n  .card {\n    border: 0;\n  }\n}\n[data-simplebar] {\n  position: relative;\n  flex-direction: column;\n  flex-wrap: wrap;\n  justify-content: flex-start;\n  align-content: flex-start;\n  align-items: flex-start;\n}\n\n.simplebar-wrapper {\n  overflow: hidden;\n  width: inherit;\n  height: inherit;\n  max-width: inherit;\n  max-height: inherit;\n}\n\n.simplebar-mask {\n  direction: inherit;\n  position: absolute;\n  overflow: hidden;\n  padding: 0;\n  margin: 0;\n  left: 0;\n  top: 0;\n  bottom: 0;\n  right: 0;\n  width: auto !important;\n  height: auto !important;\n  z-index: 0;\n}\n\n.simplebar-offset {\n  direction: inherit !important;\n  box-sizing: inherit !important;\n  resize: none !important;\n  position: absolute;\n  top: 0;\n  left: 0 !important;\n  bottom: 0;\n  right: 0 !important;\n  padding: 0;\n  margin: 0;\n  -webkit-overflow-scrolling: touch;\n}\n\n.simplebar-content-wrapper {\n  direction: inherit;\n  box-sizing: border-box !important;\n  position: relative;\n  display: block;\n  height: 100%; /* Required for horizontal native scrollbar to not appear if parent is taller than natural height */\n  width: auto;\n  visibility: visible;\n  overflow: auto; /* Scroll on this element otherwise element can't have a padding applied properly */\n  max-width: 100%; /* Not required for horizontal scroll to trigger */\n  max-height: 100%; /* Needed for vertical scroll to trigger */\n  scrollbar-width: none;\n  padding: 0px !important;\n}\n\n.simplebar-content-wrapper::-webkit-scrollbar,\n.simplebar-hide-scrollbar::-webkit-scrollbar {\n  display: none;\n}\n\n.simplebar-content:before,\n.simplebar-content:after {\n  content: \" \";\n  display: table;\n}\n\n.simplebar-placeholder {\n  max-height: 100%;\n  max-width: 100%;\n  width: 100%;\n  pointer-events: none;\n}\n\n.simplebar-height-auto-observer-wrapper {\n  box-sizing: inherit !important;\n  height: 100%;\n  width: 100%;\n  max-width: 1px;\n  position: relative;\n  float: left;\n  max-height: 1px;\n  overflow: hidden;\n  z-index: -1;\n  padding: 0;\n  margin: 0;\n  pointer-events: none;\n  flex-grow: inherit;\n  flex-shrink: 0;\n  flex-basis: 0;\n}\n\n.simplebar-height-auto-observer {\n  box-sizing: inherit;\n  display: block;\n  opacity: 0;\n  position: absolute;\n  top: 0;\n  left: 0;\n  height: 1000%;\n  width: 1000%;\n  min-height: 1px;\n  min-width: 1px;\n  overflow: hidden;\n  pointer-events: none;\n  z-index: -1;\n}\n\n.simplebar-track {\n  z-index: 1;\n  position: absolute;\n  right: 0;\n  bottom: 0;\n  pointer-events: none;\n  overflow: hidden;\n}\n\n[data-simplebar].simplebar-dragging .simplebar-content {\n  pointer-events: none;\n  user-select: none;\n  -webkit-user-select: none;\n}\n\n[data-simplebar].simplebar-dragging .simplebar-track {\n  pointer-events: all;\n}\n\n.simplebar-scrollbar {\n  position: absolute;\n  right: 2px;\n  width: 4px;\n  min-height: 10px;\n}\n\n.simplebar-scrollbar:before {\n  position: absolute;\n  content: \"\";\n  background: #a2adb7;\n  border-radius: 7px;\n  left: 0;\n  right: 0;\n  opacity: 0;\n  transition: opacity 0.2s linear;\n}\n\n.simplebar-scrollbar.simplebar-visible:before {\n  /* When hovered, remove all transitions from drag handle */\n  opacity: 0.5;\n  transition: opacity 0s linear;\n}\n\n.simplebar-track.simplebar-vertical {\n  top: 0;\n  width: 11px;\n}\n\n.simplebar-track.simplebar-vertical .simplebar-scrollbar:before {\n  top: 2px;\n  bottom: 2px;\n}\n\n.simplebar-track.simplebar-horizontal {\n  left: 0;\n  height: 11px;\n}\n\n.simplebar-track.simplebar-horizontal .simplebar-scrollbar:before {\n  height: 100%;\n  left: 2px;\n  right: 2px;\n}\n\n.simplebar-track.simplebar-horizontal .simplebar-scrollbar {\n  right: auto;\n  left: 0;\n  top: 2px;\n  height: 7px;\n  min-height: 0;\n  min-width: 10px;\n  width: auto;\n}\n\n/* Rtl support */\n[data-simplebar-direction=rtl] .simplebar-track.simplebar-vertical {\n  right: auto;\n  left: 0;\n}\n\n.hs-dummy-scrollbar-size {\n  direction: rtl;\n  position: fixed;\n  opacity: 0;\n  visibility: hidden;\n  height: 500px;\n  width: 500px;\n  overflow-y: hidden;\n  overflow-x: scroll;\n}\n\n.simplebar-hide-scrollbar {\n  position: fixed;\n  left: 0;\n  visibility: hidden;\n  overflow-y: scroll;\n  scrollbar-width: none;\n}\n\n.custom-scroll {\n  height: 100%;\n}\n\n/* ==============\n  Calendar\n===================*/\n.lnb-calendars-item {\n  display: inline-block;\n  margin-right: 7px;\n}\n\ninput[type=checkbox].tui-full-calendar-checkbox-round + span {\n  margin-right: 4px;\n  margin-left: 0;\n}\n\n.tui-full-calendar-layout,\n.tui-full-calendar-timegrid-timezone {\n  background-color: var(--bs-secondary-bg) !important;\n}\n\n.tui-full-calendar-dayname-container,\n.tui-full-calendar-left,\n.tui-full-calendar-splitter,\n.tui-full-calendar-time-date,\n.tui-full-calendar-weekday-grid-line,\n.tui-full-calendar-timegrid-timezone,\n.tui-full-calendar-timegrid-gridline {\n  border-color: var(--bs-border-color) !important;\n}\n\n.tui-full-calendar-weekday-exceed-in-week {\n  text-align: center;\n  width: 30px;\n  height: 30px;\n  line-height: 28px;\n  border-radius: 4px;\n  background-color: var(--bs-secondary-bg) !important;\n  color: var(--bs-body-color);\n  border-color: var(--bs-border-color);\n}\n\n.tui-full-calendar-timegrid-hour {\n  color: var(--bs-body-color) !important;\n}\n\n.tui-full-calendar-weekday-schedule-title {\n  color: var(--bs-emphasis-color) !important;\n}\n.tui-full-calendar-weekday-schedule-title .tui-full-calendar-time-schedule {\n  font-weight: 600;\n}\n\n.tui-full-calendar-popup-container {\n  background-color: var(--bs-secondary-bg) !important;\n  border-color: var(--bs-border-color) !important;\n}\n\n.tui-full-calendar-dropdown:hover .tui-full-calendar-dropdown-button {\n  border-color: var(--bs-border-color);\n}\n\n.tui-full-calendar-popup-section-item:hover, .tui-full-calendar-popup-section-item:focus {\n  border-color: var(--bs-border-color);\n}\n\n.tui-full-calendar-arrow-bottom .tui-full-calendar-popup-arrow-fill {\n  border-top-color: var(--bs-border-color) !important;\n}\n\n.tui-full-calendar-arrow-top .tui-full-calendar-popup-arrow-fill {\n  border-bottom-color: var(--bs-border-color) !important;\n}\n\n.tui-full-calendar-arrow-bottom .tui-full-calendar-popup-arrow-borde {\n  border-bottom-color: var(--bs-border-color) !important;\n}\n\n.tui-full-calendar-button {\n  color: var(--bs-emphasis-color);\n  background-color: var(--bs-secondary-bg) !important;\n  border-color: var(--bs-border-color);\n}\n\n.tui-full-calendar-popup-section-item {\n  border-color: var(--bs-border-color);\n}\n\n.tui-full-calendar-month-dayname,\n.tui-full-calendar-weekday-border {\n  border-top-color: var(--bs-border-color) !important;\n}\n\n.tui-full-calendar-arrow-top .tui-full-calendar-popup-arrow-border {\n  border-bottom-color: var(--bs-border-color) !important;\n}\n\n.tui-full-calendar-dropdown-menu {\n  border-color: var(--bs-border-color) !important;\n}\n\n.tui-full-calendar-dropdown-menu-item,\n.tui-full-calendar-dropdown-menu {\n  background-color: var(--bs-secondary-bg) !important;\n}\n\n.tui-full-calendar-arrow-bottom .tui-full-calendar-popup-arrow-border {\n  border-top-color: var(--bs-border-color);\n}\n\n.tui-full-calendar-content {\n  background-color: var(--bs-secondary-bg) !important;\n  color: var(--bs-emphasis-color) !important;\n}\n\n.tui-full-calendar-confirm {\n  background-color: #f46a6a !important;\n  color: #fff !important;\n}\n.tui-full-calendar-confirm:hover {\n  background-color: #c35555 !important;\n  color: #fff !important;\n}\n\n.tui-full-calendar-month-dayname-item span {\n  color: var(--bs-emphasis-color) !important;\n}\n\n.tui-full-calendar-weekday-grid-line.tui-full-calendar-near-month-day.tui-full-calendar-extra-date .tui-full-calendar-weekday-grid-header .tui-full-calendar-weekday-grid-date {\n  color: var(--bs-secondary-color) !important;\n}\n\n.tui-full-calendar-weekday-grid-line.tui-full-calendar-near-month-day .tui-full-calendar-weekday-grid-header .tui-full-calendar-weekday-grid-date {\n  color: var(--bs-emphasis-color) !important;\n}\n\n/* ==============\n  Calendar\n===================*/\n.fc td,\n.fc th {\n  border: var(--bs-border-width) solid var(--bs-border-color);\n}\n\n.fc .fc-toolbar h2 {\n  font-size: 16px;\n  line-height: 30px;\n  text-transform: uppercase;\n}\n@media (max-width: 767.98px) {\n  .fc .fc-toolbar .fc-left,\n  .fc .fc-toolbar .fc-right,\n  .fc .fc-toolbar .fc-center {\n    float: none;\n    display: block;\n    text-align: center;\n    clear: both;\n    margin: 10px 0;\n  }\n  .fc .fc-toolbar > * > * {\n    float: none;\n  }\n  .fc .fc-toolbar .fc-today-button {\n    display: none;\n  }\n}\n.fc .fc-toolbar .btn {\n  text-transform: capitalize;\n}\n.fc .fc-col-header-cell-cushion {\n  color: var(--bs-body-color);\n}\n.fc .fc-daygrid-day-number {\n  color: var(--bs-body-color);\n}\n\n.fc-daygrid-event-dot {\n  display: none;\n}\n\n.fc-prev-button,\n.fc-next-button {\n  position: relative;\n  padding: 6px 8px !important;\n}\n\n.fc-toolbar-chunk .fc-button-group .fc-button {\n  background-color: var(--bs-primary);\n  border-color: var(--bs-primary);\n  box-shadow: none;\n}\n.fc-toolbar-chunk .fc-button-group .fc-button:hover, .fc-toolbar-chunk .fc-button-group .fc-button.active {\n  color: #fff;\n  background-color: var(--bs-primary);\n  border-color: var(--bs-primary);\n}\n.fc-toolbar-chunk .fc-today-button {\n  background-color: var(--bs-primary) !important;\n  border-color: var(--bs-primary) !important;\n}\n\n.fc .fc-button-primary:not(:disabled).fc-button-active,\n.fc .fc-button-primary:not(:disabled):active {\n  background-color: var(--bs-primary) !important;\n  border-color: var(--bs-primary) !important;\n  color: #fff !important;\n}\n\n@media (max-width: 575.98px) {\n  .fc-toolbar {\n    flex-direction: column;\n    gap: 16px;\n  }\n}\n\n.fc th.fc-widget-header {\n  background: #f6f6f6;\n  color: #495057;\n  line-height: 20px;\n  padding: 10px 0;\n  text-transform: uppercase;\n  font-weight: 600;\n}\n\n.fc-unthemed .fc-content,\n.fc-unthemed .fc-divider,\n.fc-unthemed .fc-list-heading td,\n.fc-unthemed .fc-list-view,\n.fc-unthemed .fc-popover,\n.fc-unthemed .fc-row,\n.fc-unthemed tbody,\n.fc-unthemed td,\n.fc-unthemed th,\n.fc-unthemed thead {\n  border-color: #f6f6f6;\n}\n.fc-unthemed td.fc-today {\n  background: #f6f8fa;\n}\n\n.fc-button {\n  background: var(--bs-secondary-bg);\n  border-color: var(--bs-border-color);\n  color: #495057;\n  text-transform: capitalize;\n  box-shadow: none;\n  padding: 6px 12px !important;\n  height: auto !important;\n}\n\n.fc-state-down,\n.fc-state-active,\n.fc-state-disabled {\n  background-color: #556ee6;\n  color: #fff;\n  text-shadow: none;\n}\n\n.fc-event {\n  border-radius: 2px;\n  border: none;\n  cursor: move;\n  font-size: 0.8125rem;\n  margin: 5px 7px;\n  padding: 5px 5px;\n  text-align: center;\n  color: #fff;\n}\n.fc-event.bg-dark {\n  background-color: var(--bs-secondary-color) !important;\n}\n.fc-event.bg-dark .fc-event-time {\n  color: var(--bs-secondary-bg) !important;\n}\n.fc-event.bg-dark .fc-event-title {\n  color: var(--bs-secondary-bg) !important;\n}\n\n.fc-event,\n.fc-event-dot {\n  background-color: #556ee6;\n}\n\n.fc-event .fc-content {\n  color: #fff;\n}\n\n#external-events .external-event {\n  text-align: left;\n  padding: 8px 16px;\n  margin-left: 0;\n  margin-right: 0;\n}\n\n.fc-day-grid-event.fc-h-event.fc-event.fc-start.fc-end.bg-dark .fc-content {\n  color: #eff2f7;\n}\n\n[dir=rtl] .fc-header-toolbar {\n  direction: ltr !important;\n}\n[dir=rtl] .fc-toolbar > * > :not(:first-child) {\n  margin-left: 0.75em;\n}\n\n.fc-theme-standard .fc-scrollgrid {\n  border-color: var(--bs-border-color);\n}\n\n.fc .fc-daygrid-week-number {\n  background-color: var(--bs-body-bg);\n  color: var(--bs-body-color);\n}\n\n.fc .fc-daygrid-more-link {\n  padding: 5px;\n  font-size: 11px;\n  font-weight: 600;\n}\n\n.fc .fc-daygrid-more-link:hover {\n  background-color: rgba(var(--bs-primary-rgb), 0.1);\n}\n\n.fc .fc-popover-header {\n  padding: 6px 12px;\n}\n\n.fc-theme-standard .fc-popover-header {\n  background: var(--bs-body-bg);\n}\n\n.fc-theme-standard .fc-popover {\n  background: var(--bs-secondary-bg);\n  border: 1px solid var(--bs-border-color);\n}\n\n/* ==============\n  Druafula\n===================*/\n.task-box {\n  border: 1px solid var(--bs-border-color);\n}\n\n.gu-transit {\n  border: 1px dashed var(--bs-border-color) !important;\n  background-color: var(--bs-gray-200) !important;\n}\n\n#session-timeout-dialog .close {\n  display: none;\n}\n#session-timeout-dialog .countdown-holder {\n  color: #f46a6a;\n  font-weight: 500;\n}\n#session-timeout-dialog .btn-default {\n  background-color: #fff;\n  color: #f46a6a;\n  box-shadow: none;\n}\n\n.irs {\n  font-family: var(--bs-font-sans-serif);\n}\n\n.irs--square {\n  cursor: pointer;\n}\n.irs--square .irs-bar,\n.irs--square .irs-to,\n.irs--square .irs-from,\n.irs--square .irs-single {\n  background: #556ee6 !important;\n  font-size: 11px;\n}\n.irs--square .irs-to:before,\n.irs--square .irs-from:before,\n.irs--square .irs-single:before {\n  border-top-color: #556ee6;\n}\n.irs--square .irs-line {\n  background: var(--bs-tertiary-bg);\n  border-color: var(--bs-tertiary-bg);\n}\n.irs--square .irs-grid-text {\n  font-size: 11px;\n  color: var(--bs-secondary-color);\n}\n.irs--square .irs-min,\n.irs--square .irs-max {\n  color: var(--bs-secondary-color);\n  background: var(--bs-tertiary-bg);\n  font-size: 11px;\n}\n.irs--square .irs-handle {\n  border: 2px solid #556ee6;\n  width: 12px;\n  height: 12px;\n  top: 26px;\n  background-color: var(--bs-secondary-bg) !important;\n  cursor: pointer;\n}\n.irs--square .irs-handle:active {\n  cursor: pointer;\n}\n.irs--square .irs-grid-pol {\n  background-color: var(--bs-secondary-color);\n}\n\n.swal2-container .swal2-title {\n  font-size: 20px;\n  font-weight: 500;\n}\n\n.swal2-modal {\n  font-size: 14px;\n}\n\n.swal2-icon.swal2-question {\n  border-color: #50a5f1;\n  color: #50a5f1;\n}\n.swal2-icon.swal2-success [class^=swal2-success-line] {\n  background-color: #34c38f;\n}\n.swal2-icon.swal2-success .swal2-success-ring {\n  border-color: rgba(52, 195, 143, 0.3);\n}\n.swal2-icon.swal2-warning {\n  border-color: #f1b44c;\n  color: #f1b44c;\n}\n\n.swal2-styled:focus {\n  box-shadow: none;\n}\n\n.swal2-progress-steps .swal2-progress-step {\n  background: #556ee6;\n}\n.swal2-progress-steps .swal2-progress-step.swal2-active-progress-step {\n  background: #556ee6;\n}\n.swal2-progress-steps .swal2-progress-step.swal2-active-progress-step ~ .swal2-progress-step, .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step ~ .swal2-progress-step-line {\n  background: rgba(85, 110, 230, 0.3);\n}\n.swal2-progress-steps .swal2-progress-step-line {\n  background: #556ee6;\n}\n\n.swal2-loader {\n  border-color: #556ee6 transparent #556ee6 transparent;\n}\n\n.swal2-popup {\n  background: var(--bs-secondary-bg);\n}\n\n.swal2-title, .swal2-html-container {\n  color: var(--bs-emphasis-color);\n}\n\n.swal2-file,\n.swal2-input,\n.swal2-textarea {\n  border-color: var(--bs-border-color);\n  color: var(--bs-body-color);\n}\n.swal2-file:focus,\n.swal2-input:focus,\n.swal2-textarea:focus {\n  box-shadow: none;\n  border-color: var(--bs-border-color);\n}\n\ndiv:where(.swal2-container) div:where(.swal2-popup) {\n  color: var(--bs-secondary-color);\n}\n\n.symbol {\n  border-color: var(--bs-secondary-bg);\n}\n\n.rating-symbol-background, .rating-symbol-foreground {\n  font-size: 24px;\n}\n\n.symbol-empty {\n  background-color: var(--bs-gray-400);\n}\n\n.rating-symbol-foreground {\n  top: 0px;\n}\n\n.rating-star > span {\n  display: inline-block;\n  vertical-align: middle;\n}\n.rating-star > span.badge {\n  margin-left: 4px;\n}\n\n/* =============\n   Notification\n============= */\n#toast-container > div {\n  box-shadow: 0 0.75rem 1.5rem rgba(18, 38, 63, 0.03);\n  opacity: 1;\n}\n#toast-container > div:hover {\n  box-shadow: 0 0.75rem 1.5rem rgba(18, 38, 63, 0.03);\n  opacity: 0.9;\n}\n#toast-container.toast-top-full-width > div, #toast-container.toast-bottom-full-width > div {\n  min-width: 96%;\n  margin: 4px auto;\n}\n\n.toast-primary {\n  border: 2px solid #556ee6 !important;\n  background-color: rgba(85, 110, 230, 0.8) !important;\n}\n\n.toast-secondary {\n  border: 2px solid #74788d !important;\n  background-color: rgba(116, 120, 141, 0.8) !important;\n}\n\n.toast-success {\n  border: 2px solid #34c38f !important;\n  background-color: rgba(52, 195, 143, 0.8) !important;\n}\n\n.toast-info {\n  border: 2px solid #50a5f1 !important;\n  background-color: rgba(80, 165, 241, 0.8) !important;\n}\n\n.toast-warning {\n  border: 2px solid #f1b44c !important;\n  background-color: rgba(241, 180, 76, 0.8) !important;\n}\n\n.toast-danger {\n  border: 2px solid #f46a6a !important;\n  background-color: rgba(244, 106, 106, 0.8) !important;\n}\n\n.toast-pink {\n  border: 2px solid #e83e8c !important;\n  background-color: rgba(232, 62, 140, 0.8) !important;\n}\n\n.toast-light {\n  border: 2px solid #eff2f7 !important;\n  background-color: rgba(239, 242, 247, 0.8) !important;\n}\n\n.toast-dark {\n  border: 2px solid #343a40 !important;\n  background-color: rgba(52, 58, 64, 0.8) !important;\n}\n\n.toast-error {\n  background-color: rgba(244, 106, 106, 0.8);\n  border: 2px solid #f46a6a;\n}\n\n.toastr-options {\n  padding: 24px;\n  background-color: var(--bs-tertiary-bg);\n  margin-bottom: 0;\n  border: 1px solid var(--bs-border-color);\n}\n\n.error {\n  color: #f46a6a;\n}\n\n.parsley-error {\n  border-color: #f46a6a;\n}\n\n.parsley-errors-list {\n  display: none;\n  margin: 0;\n  padding: 0;\n}\n.parsley-errors-list.filled {\n  display: block;\n}\n.parsley-errors-list > li {\n  font-size: 12px;\n  list-style: none;\n  color: #f46a6a;\n  margin-top: 5px;\n}\n\n.select2-container .select2-selection--single {\n  background-color: var(--bs-secondary-bg);\n  border: 1px solid var(--bs-border-color-translucent);\n  height: 38px;\n}\n.select2-container .select2-selection--single:focus {\n  outline: none;\n}\n.select2-container .select2-selection--single .select2-selection__rendered {\n  line-height: 36px;\n  padding-left: 0.75rem;\n  color: var(--bs-secondary-color);\n}\n.select2-container .select2-selection--single .select2-selection__arrow {\n  height: 34px;\n  width: 34px;\n  right: 3px;\n}\n.select2-container .select2-selection--single .select2-selection__arrow b {\n  border-color: var(--bs-gray-500) transparent transparent transparent;\n  border-width: 6px 6px 0 6px;\n}\n.select2-container .select2-selection--single .select2-selection__placeholder {\n  color: var(--bs-body-color);\n}\n\n[dir=rtl] .select2-selection__rendered {\n  text-align: end;\n}\n\n.select2-container--open .select2-selection--single .select2-selection__arrow b {\n  border-color: transparent transparent var(--bs-gray-500) transparent !important;\n  border-width: 0 6px 6px 6px !important;\n}\n\n.select2-container--default .select2-search--dropdown {\n  padding: 10px;\n  background-color: var(--bs-secondary-bg);\n}\n.select2-container--default .select2-search--dropdown .select2-search__field {\n  border: 1px solid var(--bs-border-color-translucent);\n  background-color: var(--bs-secondary-bg);\n  color: var(--bs-secondary-color);\n  outline: none;\n}\n.select2-container--default .select2-results__option--highlighted[aria-selected] {\n  background-color: #556ee6;\n}\n.select2-container--default .select2-results__option[aria-selected=true] {\n  background-color: var(--bs-light);\n  color: var(--bs-emphasis-color);\n}\n.select2-container--default .select2-results__option[aria-selected=true]:hover {\n  background-color: #556ee6;\n  color: #fff;\n}\n.select2-container--default .select2-selection--multiple .select2-selection__choice__remove {\n  border-right: none;\n  color: var(--bs-emphasis-color);\n}\n\n.select2-results__option {\n  padding: 6px 12px;\n}\n\n.select2-container[dir=rtl] .select2-selection--single .select2-selection__rendered {\n  padding-left: 0.75rem;\n}\n\n.select2-dropdown {\n  border: 1px solid var(--bs-border-color);\n  background-color: var(--bs-secondary-bg);\n  box-shadow: 0 0.75rem 1.5rem rgba(18, 38, 63, 0.03);\n}\n\n.select2-search input {\n  border: 1px solid var(--bs-border-color);\n}\n\n.select2-container .select2-selection--multiple {\n  min-height: 38px;\n  background-color: var(--bs-secondary-bg);\n  border: 1px solid var(--bs-border-color-translucent) !important;\n  padding: 2px 0.75rem;\n}\n.select2-container .select2-selection--multiple .select2-selection__rendered {\n  padding: 2px 0.75rem;\n}\n.select2-container .select2-selection--multiple .select2-search__field {\n  border: 0;\n  color: var(--bs-emphasis-color);\n  margin: 0;\n  margin-top: 7px;\n}\n.select2-container .select2-selection--multiple .select2-search__field::placeholder {\n  color: var(--bs-secondary-color);\n}\n.select2-container .select2-selection--multiple .select2-selection__choice {\n  background-color: var(--bs-tertiary-bg);\n  border: 1px solid var(--bs-border-color);\n  border-radius: 1px;\n  padding: 0 7px;\n}\n.select2-container .select2-selection--multiple .select2-selection__rendered {\n  padding: 0;\n}\n\n.select2-container--default.select2-container--focus .select2-selection--multiple {\n  border-color: var(--bs-border-color-translucent);\n}\n.select2-container--default .select2-results__group {\n  font-weight: 600;\n}\n.select2-container--default .select2-selection--multiple .select2-selection__choice__display {\n  padding-left: 16px;\n}\n\n.select2-result-repository__avatar {\n  float: left;\n  width: 60px;\n  margin-right: 10px;\n}\n.select2-result-repository__avatar img {\n  width: 100%;\n  height: auto;\n  border-radius: 2px;\n}\n\n.select2-result-repository__statistics {\n  margin-top: 7px;\n}\n\n.select2-result-repository__forks,\n.select2-result-repository__stargazers,\n.select2-result-repository__watchers {\n  display: inline-block;\n  font-size: 11px;\n  margin-right: 1em;\n  color: var(--bs-secondary-color);\n}\n.select2-result-repository__forks .fa,\n.select2-result-repository__stargazers .fa,\n.select2-result-repository__watchers .fa {\n  margin-right: 4px;\n}\n.select2-result-repository__forks .fa.fa-flash::before,\n.select2-result-repository__stargazers .fa.fa-flash::before,\n.select2-result-repository__watchers .fa.fa-flash::before {\n  content: \"\\f0e7\";\n  font-family: \"Font Awesome 5 Free\";\n}\n\n.select2-results__option--highlighted .select2-result-repository__forks,\n.select2-results__option--highlighted .select2-result-repository__stargazers,\n.select2-results__option--highlighted .select2-result-repository__watchers {\n  color: rgba(255, 255, 255, 0.8);\n}\n\n.select2-result-repository__meta {\n  overflow: hidden;\n}\n\n.img-flag {\n  margin-right: 7px;\n  height: 15px;\n  width: 18px;\n}\n\n.select2-container--default .select2-results__option--selected {\n  background-color: var(--bs-tertiary-bg);\n}\n\n/* CSS Switch */\ninput[switch] {\n  display: none;\n}\ninput[switch] + label {\n  font-size: 1em;\n  line-height: 1;\n  width: 56px;\n  height: 24px;\n  background-color: #ced4da;\n  background-image: none;\n  border-radius: 2rem;\n  padding: 0.16667rem;\n  cursor: pointer;\n  display: inline-block;\n  text-align: center;\n  position: relative;\n  font-weight: 500;\n  transition: all 0.1s ease-in-out;\n}\ninput[switch] + label:before {\n  color: #343a40;\n  content: attr(data-off-label);\n  display: block;\n  font-family: inherit;\n  font-weight: 500;\n  font-size: 12px;\n  line-height: 21px;\n  position: absolute;\n  right: 1px;\n  margin: 3px;\n  top: -2px;\n  text-align: center;\n  min-width: 1.66667rem;\n  overflow: hidden;\n  transition: all 0.1s ease-in-out;\n}\ninput[switch] + label:after {\n  content: \"\";\n  position: absolute;\n  left: 3px;\n  background-color: #eff2f7;\n  box-shadow: none;\n  border-radius: 2rem;\n  height: 20px;\n  width: 20px;\n  top: 2px;\n  transition: all 0.1s ease-in-out;\n}\ninput[switch]:checked + label {\n  background-color: #556ee6;\n}\n\ninput[switch]:checked + label {\n  background-color: #556ee6;\n}\ninput[switch]:checked + label:before {\n  color: #fff;\n  content: attr(data-on-label);\n  right: auto;\n  left: 3px;\n}\ninput[switch]:checked + label:after {\n  left: 33px;\n  background-color: #eff2f7;\n}\n\ninput[switch=bool] + label {\n  background-color: #f46a6a;\n}\n\ninput[switch=bool] + label:before, input[switch=bool]:checked + label:before,\ninput[switch=default]:checked + label:before {\n  color: #fff;\n}\n\ninput[switch=bool]:checked + label {\n  background-color: #34c38f;\n}\n\ninput[switch=default]:checked + label {\n  background-color: #a2a2a2;\n}\n\ninput[switch=primary]:checked + label {\n  background-color: #556ee6;\n}\n\ninput[switch=success]:checked + label {\n  background-color: #34c38f;\n}\n\ninput[switch=info]:checked + label {\n  background-color: #50a5f1;\n}\n\ninput[switch=warning]:checked + label {\n  background-color: #f1b44c;\n}\n\ninput[switch=danger]:checked + label {\n  background-color: #f46a6a;\n}\n\ninput[switch=dark]:checked + label {\n  background-color: #343a40;\n}\ninput[switch=dark]:checked + label:before {\n  color: #eff2f7;\n}\n\n.square-switch {\n  margin-right: 7px;\n}\n.square-switch input[switch] + label, .square-switch input[switch] + label:after {\n  border-radius: 4px;\n}\n\n.sp-container {\n  background-color: var(--bs-secondary-bg);\n}\n.sp-container button {\n  padding: 0.25rem 0.5rem;\n  font-size: 0.71094rem;\n  border-radius: 0.2rem;\n  font-weight: 400;\n  color: #343a40;\n}\n.sp-container button.sp-palette-toggle {\n  background-color: #eff2f7;\n}\n.sp-container button.sp-choose {\n  background-color: #34c38f;\n  margin-left: 5px;\n  margin-right: 0;\n}\n\n.sp-palette-container {\n  border-right: 1px solid #eff2f7;\n}\n\n.sp-input {\n  background-color: var(--bs-secondary-bg);\n  border-color: var(--bs-border-color-translucent) !important;\n  color: var(--bs-emphasis-color);\n}\n.sp-input:focus {\n  outline: none;\n}\n\n[dir=rtl] .sp-alpha {\n  direction: ltr;\n}\n[dir=rtl] .sp-original-input-container .sp-add-on {\n  border-top-right-radius: 0 !important;\n  border-bottom-right-radius: 0 !important;\n  border-top-left-radius: 4px !important;\n  border-bottom-left-radius: 4px !important;\n}\n[dir=rtl] input.spectrum.with-add-on {\n  border: 1px solid var(--bs-border-color-translucent);\n  border-left: 0;\n  border-top-left-radius: 0;\n  border-bottom-left-radius: 0;\n  border-top-right-radius: var(--bs-border-radius);\n  border-bottom-right-radius: var(--bs-border-radius);\n}\n\n/* Timepicker */\n.bootstrap-timepicker-widget table td a {\n  color: var(--bs-emphasis-color);\n}\n.bootstrap-timepicker-widget table td a:hover {\n  background-color: transparent;\n  border-color: transparent;\n  border-radius: 4px;\n  color: #556ee6;\n  text-decoration: none;\n}\n.bootstrap-timepicker-widget table td input {\n  width: 32px;\n  height: 32px;\n  border: 0;\n  color: var(--bs-body-color);\n  border: 1px solid var(--bs-border-color);\n  background-color: var(--bs-secondary-bg);\n}\n.bootstrap-timepicker-widget.dropdown-menu:after {\n  border-bottom-color: var(--bs-gray-200);\n}\n.bootstrap-timepicker-widget.timepicker-orient-bottom:after {\n  border-top-color: var(--bs-gray-200);\n}\n\n.timepicker-orient-top {\n  top: calc(1.5em + 0.94rem + calc(var(--bs-border-width) * 2)) !important;\n}\n\n.timepicker-orient-bottom {\n  top: auto !important;\n  bottom: calc(1.5em + 0.94rem + calc(var(--bs-border-width) * 2)) !important;\n}\n\n.bootstrap-timepicker-widget {\n  left: 0 !important;\n  right: auto !important;\n}\n\n.bootstrap-timepicker-widget.timepicker-orient-left:before {\n  left: 6px;\n  right: auto;\n}\n.bootstrap-timepicker-widget.timepicker-orient-left::after {\n  left: 7px;\n  right: auto;\n}\n\n.datepicker {\n  border: 1px solid var(--bs-border-color);\n  padding: 8px;\n  z-index: 999 !important;\n}\n.datepicker table tr th {\n  font-weight: 500;\n}\n.datepicker table tr td.active, .datepicker table tr td.active:hover, .datepicker table tr td .active.disabled, .datepicker table tr td.active.disabled:hover, .datepicker table tr td.today, .datepicker table tr td.today:hover, .datepicker table tr td.today.disabled, .datepicker table tr td.today.disabled:hover, .datepicker table tr td.selected, .datepicker table tr td.selected:hover, .datepicker table tr td.selected.disabled, .datepicker table tr td.selected.disabled:hover,\n.datepicker table tr td span.active.active, .datepicker table tr td span.active:hover.active {\n  background-color: #556ee6 !important;\n  background-image: none;\n  box-shadow: none;\n  color: #fff !important;\n}\n.datepicker table tr td.day.focused, .datepicker table tr td.day:hover,\n.datepicker table tr td span.focused,\n.datepicker table tr td span:hover {\n  background: var(--bs-light);\n}\n.datepicker table tr td.new, .datepicker table tr td.old,\n.datepicker table tr td span.new,\n.datepicker table tr td span.old {\n  color: var(--bs-gray-500);\n  opacity: 0.6;\n}\n.datepicker table tr td.range, .datepicker table tr td.range.disabled, .datepicker table tr td.range.disabled:hover, .datepicker table tr td.range:hover {\n  background-color: var(--bs-tertiary-bg);\n}\n\n.table-condensed > thead > tr > th, .table-condensed > tbody > tr > td {\n  padding: 7px;\n}\n\n.bootstrap-datepicker-inline .datepicker-inline {\n  width: auto !important;\n  display: inline-block;\n}\n\n.datepicker-container {\n  border: 1px solid var(--bs-border-color);\n  box-shadow: none;\n  background-color: var(--bs-secondary-bg);\n}\n.datepicker-container.datepicker-inline {\n  width: 212px;\n}\n\n.datepicker {\n  color: var(--bs-secondary-color);\n}\n.datepicker .datepicker-switch:hover, .datepicker .next:hover, .datepicker .prev:hover, .datepicker tfoot tr th:hover {\n  background: var(--bs-light);\n}\n\n.datepicker-panel > ul > li {\n  background-color: var(--bs-secondary-bg);\n  border-radius: 4px;\n}\n.datepicker-panel > ul > li.picked, .datepicker-panel > ul > li.picked:hover {\n  background-color: rgba(85, 110, 230, 0.25);\n  color: #556ee6;\n}\n.datepicker-panel > ul > li.highlighted, .datepicker-panel > ul > li.highlighted:hover, .datepicker-panel > ul > li:hover {\n  background-color: #556ee6;\n  color: #fff;\n}\n.datepicker-panel > ul > li.muted, .datepicker-panel > ul > li.muted:hover {\n  color: var(--bs-gray-500);\n  opacity: 0.6;\n}\n.datepicker-panel > ul[data-view=week] > li {\n  font-weight: 500;\n}\n.datepicker-panel > ul[data-view=week] > li, .datepicker-panel > ul[data-view=week] > li:hover {\n  background-color: var(--bs-secondary-bg);\n}\n\n.bootstrap-touchspin.input-group > .input-group-prepend > .btn, .bootstrap-touchspin.input-group > .input-group-prepend > .input-group-text {\n  border-top-right-radius: 0;\n  border-bottom-right-radius: 0;\n}\n.bootstrap-touchspin.input-group > .input-group-append > .btn, .bootstrap-touchspin.input-group > .input-group-append > .input-group-text {\n  border-top-left-radius: 0;\n  border-bottom-left-radius: 0;\n}\n\nform .tox-tinymce {\n  border: 1px solid var(--bs-border-color) !important;\n}\nform .tox-toolbar-overlord {\n  background-color: var(--bs-secondary-bg) !important;\n}\nform .tox :not(svg):not(rect) {\n  color: #fff !important;\n}\nform .tox:not(.tox-tinymce-inline) .tox-editor-header {\n  box-shadow: var(--bs-box-shadow);\n}\nform .tox .tox-mbtn {\n  color: #74788d;\n}\nform .tox .tox-mbtn:hover:not(:disabled):not(.tox-mbtn--active) {\n  background-color: #f8f9fa;\n}\nform .tox .tox-tbtn:hover {\n  color: rgba(73, 80, 87, 0.5);\n  background-color: #f8f9fa;\n}\nform .tox .tox-tbtn--disabled svg,\nform .tox .tox-tbtn--disabled:hover svg,\nform .tox .tox-tbtn:disabled svg,\nform .tox .tox-tbtn:disabled:hover svg {\n  fill: rgba(73, 80, 87, 0.5);\n}\nform .tox .tox-tbtn--bespoke {\n  background-color: #f8f9fa;\n}\nform .tox .tox-editor-header {\n  background-color: var(--bs-secondary-bg) !important;\n}\nform .tox .tox-menubar {\n  background-color: var(--bs-secondary-bg) !important;\n}\nform .tox .tox-menubar .tox-mbtn {\n  background-color: var(--bs-tertiary-bg) !important;\n}\nform .tox .tox-menubar .tox-mbtn .tox-mbtn__select-label {\n  color: #fff !important;\n}\nform .tox .tox-toolbar__primary {\n  background-color: var(--bs-secondary-bg) !important;\n}\nform .tox .tox-toolbar__primary .tox-toolbar__group .tox-tbtn {\n  background-color: var(--bs-tertiary-bg) !important;\n}\nform .tox .tox-toolbar__primary .tox-toolbar__group .tox-tbtn .tox-icon svg {\n  fill: #fff !important;\n}\nform .tox .tox-edit-area {\n  background-color: var(--bs-secondary-bg) !important;\n}\nform .tox .tox-promotion {\n  background-color: var(--bs-secondary-bg) !important;\n}\nform .tox .tox-statusbar {\n  background-color: var(--bs-secondary-bg) !important;\n  color: var(--bs-body-color) !important;\n}\n\n.tox .tox-collection--list .tox-collection__group {\n  border-color: var(--bs-border-color) !important;\n}\n\n.tox-collection {\n  background-color: var(--bs-secondary-bg) !important;\n}\n\n.tox-collection__item-label {\n  color: #fff !important;\n}\n\n.tox .tox-collection--list .tox-collection__item--enabled {\n  background-color: var(--bs-tertiary-bg) !important;\n}\n\n.tox-collection__item-icon svg {\n  fill: var(--bs-body-color) !important;\n}\n\n/* Dropzone */\n.dropzone {\n  min-height: 230px;\n  border: 2px dashed var(--bs-border-color);\n  background: var(--bs-secondary-bg);\n  border-radius: 6px;\n}\n.dropzone .dz-message {\n  font-size: 24px;\n  width: 100%;\n  margin: 3rem 0;\n}\n\n.form-wizard-wrapper label {\n  font-size: 14px;\n  text-align: right;\n}\n\n.wizard .steps > ul {\n  display: flex;\n  flex-wrap: wrap;\n  padding-left: 0;\n  margin-bottom: 0;\n  list-style: none;\n}\n@media (max-width: 1199.98px) {\n  .wizard .steps > ul {\n    flex-direction: column;\n  }\n}\n.wizard .steps > ul > a, .wizard .steps > ul > li {\n  flex-basis: 0;\n  flex-grow: 1;\n}\n.wizard .steps > ul > li {\n  width: 100%;\n}\n.wizard .steps > ul > li a {\n  display: block;\n  padding: 0.5rem 1rem;\n  color: var(--bs-secondary-color);\n  font-weight: 500;\n  background-color: rgba(85, 110, 230, 0.1);\n}\n.wizard .steps > ul .current-info {\n  position: absolute;\n  left: -999em;\n}\n.wizard .steps .number {\n  display: inline-block;\n  width: 38px;\n  height: 38px;\n  line-height: 34px;\n  border: 2px solid #556ee6;\n  color: #556ee6;\n  text-align: center;\n  border-radius: 50%;\n  margin-right: 0.5rem;\n}\n.wizard .steps .current a, .wizard .steps .current a:active, .wizard .steps .current a:hover {\n  background-color: rgba(85, 110, 230, 0.2);\n  color: var(--bs-emphasis-color);\n}\n.wizard .steps .current a .number, .wizard .steps .current a:active .number, .wizard .steps .current a:hover .number {\n  background-color: #556ee6;\n  color: #fff;\n}\n.wizard > .content {\n  background-color: transparent;\n  padding: 14px;\n  margin-top: 0;\n  border-radius: 0;\n  min-height: 150px;\n}\n.wizard > .content > .title {\n  position: absolute;\n  left: -999em;\n}\n.wizard > .content > .body {\n  width: 100%;\n  height: 100%;\n  padding: 14px 0 0;\n  position: static;\n}\n.wizard > .actions {\n  position: relative;\n  display: block;\n  text-align: right;\n  width: 100%;\n}\n.wizard > .actions > ul {\n  display: block;\n  text-align: right;\n  padding-left: 0;\n}\n.wizard > .actions > ul > li {\n  display: inline-block;\n  margin: 0 0.5em;\n}\n.wizard > .actions a, .wizard > .actions a:active, .wizard > .actions a:hover {\n  background-color: #556ee6;\n  border-radius: 4px;\n  padding: 8px 15px;\n  color: #fff;\n}\n.wizard > .actions .disabled a, .wizard > .actions .disabled a:active, .wizard > .actions .disabled a:hover {\n  opacity: 0.65;\n  background-color: #556ee6;\n  color: #fff;\n  cursor: not-allowed;\n}\n.wizard.vertical-wizard {\n  display: flex;\n  flex-wrap: wrap;\n}\n.wizard.vertical-wizard .steps > ul {\n  flex-direction: column;\n}\n.wizard.vertical-wizard .steps > ul > li {\n  width: 100% !important;\n}\n.wizard.vertical-wizard .steps, .wizard.vertical-wizard .content, .wizard.vertical-wizard .actions {\n  width: 100%;\n}\n@media (min-width: 1200px) {\n  .wizard.vertical-wizard .steps {\n    width: 25%;\n  }\n}\n.wizard.vertical-wizard .content {\n  padding: 24px;\n}\n@media (min-width: 1200px) {\n  .wizard.vertical-wizard .content {\n    width: 75%;\n    padding: 12px 24px;\n  }\n}\n.wizard.vertical-wizard .content > .body {\n  padding: 0;\n}\n\n/* \nDatatable\n*/\ndiv.dataTables_wrapper div.dataTables_filter {\n  text-align: right;\n}\n@media (max-width: 767px) {\n  div.dataTables_wrapper div.dataTables_filter {\n    text-align: center;\n  }\n}\ndiv.dataTables_wrapper div.dataTables_filter input {\n  margin-left: 0.5em;\n  margin-right: 0;\n}\n\n.table.dataTable,\ntable.dataTable {\n  border-collapse: collapse !important;\n}\n.table.dataTable.dtr-inline.collapsed > tbody > tr > td,\ntable.dataTable.dtr-inline.collapsed > tbody > tr > td {\n  position: relative;\n}\n.table.dataTable.dtr-inline.collapsed > tbody > tr > td.dtr-control,\ntable.dataTable.dtr-inline.collapsed > tbody > tr > td.dtr-control {\n  padding-left: 30px;\n}\n.table.dataTable.dtr-inline.collapsed > tbody > tr > td.dtr-control:before,\ntable.dataTable.dtr-inline.collapsed > tbody > tr > td.dtr-control:before {\n  top: 50%;\n  left: 5px;\n  height: 14px;\n  width: 14px;\n  margin-top: -9px;\n  display: block;\n  position: absolute;\n  color: #fff;\n  border: 2px solid #fff;\n  border-radius: 14px;\n  box-sizing: content-box;\n  text-align: center;\n  text-indent: 0 !important;\n  line-height: 14px;\n  content: \"+\";\n  background-color: #556ee6;\n}\n\n.table-rep-plugin .btn-toolbar {\n  display: block;\n}\n.table-rep-plugin .table-responsive {\n  border: none !important;\n}\n.table-rep-plugin .btn-group .btn-default {\n  background-color: #74788d;\n  color: #eff2f7;\n  border: 1px solid #74788d;\n}\n.table-rep-plugin .btn-group .btn-default.btn-primary {\n  background-color: #556ee6;\n  border-color: #556ee6;\n  color: #fff;\n  box-shadow: 0 0 0 2px rgba(85, 110, 230, 0.5);\n}\n.table-rep-plugin .btn-group.pull-right {\n  float: right;\n}\n.table-rep-plugin .btn-group.pull-right .dropdown-menu {\n  right: 0;\n  transform: none !important;\n  top: 100% !important;\n}\n.table-rep-plugin tbody th {\n  font-size: 14px;\n  font-weight: normal;\n}\n.table-rep-plugin .checkbox-row {\n  padding-left: 40px;\n  color: var(--bs-body-color) !important;\n}\n.table-rep-plugin .checkbox-row:hover {\n  background-color: #f6f8fa !important;\n}\n.table-rep-plugin .checkbox-row label {\n  display: inline-block;\n  padding-left: 5px;\n  position: relative;\n}\n.table-rep-plugin .checkbox-row label::before {\n  -o-transition: 0.3s ease-in-out;\n  -webkit-transition: 0.3s ease-in-out;\n  background-color: #fff;\n  border-radius: 3px;\n  border: 1px solid #f6f6f6;\n  content: \"\";\n  display: inline-block;\n  height: 17px;\n  left: 0;\n  margin-left: -20px;\n  position: absolute;\n  transition: 0.3s ease-in-out;\n  width: 17px;\n  outline: none !important;\n}\n.table-rep-plugin .checkbox-row label::after {\n  color: #eff2f7;\n  display: inline-block;\n  font-size: 11px;\n  height: 16px;\n  left: 0;\n  margin-left: -20px;\n  padding-left: 3px;\n  padding-top: 1px;\n  position: absolute;\n  top: -1px;\n  width: 16px;\n}\n.table-rep-plugin .checkbox-row input[type=checkbox] {\n  cursor: pointer;\n  opacity: 0;\n  z-index: 1;\n  outline: none !important;\n}\n.table-rep-plugin .checkbox-row input[type=checkbox]:disabled + label {\n  opacity: 0.65;\n}\n.table-rep-plugin .checkbox-row input[type=checkbox]:focus + label::before {\n  outline-offset: -2px;\n  outline: none;\n}\n.table-rep-plugin .checkbox-row input[type=checkbox]:checked + label::after {\n  content: \"\\f00c\";\n  font-family: \"Font Awesome 5 Free\";\n  font-weight: 900;\n}\n.table-rep-plugin .checkbox-row input[type=checkbox]:disabled + label::before {\n  background-color: #f8f9fa;\n  cursor: not-allowed;\n}\n.table-rep-plugin .checkbox-row input[type=checkbox]:checked + label::before {\n  background-color: #556ee6;\n  border-color: #556ee6;\n}\n.table-rep-plugin .checkbox-row input[type=checkbox]:checked + label::after {\n  color: #fff;\n}\n.table-rep-plugin .fixed-solution .sticky-table-header {\n  top: 70px !important;\n  background-color: #556ee6;\n}\n.table-rep-plugin .fixed-solution .sticky-table-header table {\n  color: #fff;\n}\n.table-rep-plugin table.focus-on tbody tr.focused th,\n.table-rep-plugin table.focus-on tbody tr.focused td,\n.table-rep-plugin .sticky-table-header {\n  background: #556ee6;\n  border-color: #556ee6;\n  color: #fff;\n}\n.table-rep-plugin table.focus-on tbody tr.focused th table,\n.table-rep-plugin table.focus-on tbody tr.focused td table,\n.table-rep-plugin .sticky-table-header table {\n  color: #fff;\n}\n.table-rep-plugin table.focus-on tbody tr.unfocused th,\n.table-rep-plugin table.focus-on tfoot tr.unfocused th,\n.table-rep-plugin table.focus-on tbody tr.unfocused td,\n.table-rep-plugin table.focus-on tfoot tr.unfocused td {\n  color: var(--bs-body-color);\n}\n\n@media (min-width: 992px) {\n  body[data-layout=horizontal] .fixed-solution .sticky-table-header {\n    top: 120px !important;\n  }\n}\n\n.table-edits input, .table-edits select {\n  height: calc(1.5em + 0.5rem + calc(var(--bs-border-width) * 2));\n  padding: 0.25rem 0.5rem;\n  border: 1px solid var(--bs-border-color-translucent);\n  background-color: var(--bs-secondary-bg);\n  color: var(--bs-emphasis-color);\n  border-radius: var(--bs-border-radius);\n}\n.table-edits input:focus, .table-edits select:focus {\n  outline: none;\n  border-color: rgb(var(--bs-body-color-rgb), 0.2);\n}\n\n.apex-charts {\n  min-height: 10px !important;\n}\n.apex-charts text {\n  font-family: var(--bs-font-sans-serif) !important;\n  fill: var(--bs-gray-500);\n}\n.apex-charts .apexcharts-canvas {\n  margin: 0 auto;\n}\n\n.apexcharts-tooltip-title,\n.apexcharts-tooltip-text {\n  font-family: var(--bs-font-sans-serif) !important;\n}\n\n.apexcharts-legend-series {\n  font-weight: 500;\n}\n\n.apexcharts-gridline {\n  pointer-events: none;\n  stroke: var(--bs-border-color);\n}\n\n.apexcharts-legend-text {\n  color: #74788d !important;\n  font-family: var(--bs-font-sans-serif) !important;\n  font-size: 13px !important;\n}\n\n.apexcharts-pie-label {\n  fill: #fff !important;\n}\n\n.apexcharts-yaxis text,\n.apexcharts-xaxis text {\n  font-family: var(--bs-font-sans-serif) !important;\n  fill: var(--bs-secondary-color);\n}\n\n.apexcharts-gridline {\n  stroke: var(--bs-border-color);\n}\n\n.apexcharts-radialbar-track.apexcharts-track path {\n  stroke: var(--bs-border-color);\n}\n\n.apexcharts-tooltip {\n  background-color: var(--bs-secondary-bg) !important;\n  border: 1px solid var(--bs-border-color) !important;\n}\n.apexcharts-tooltip .apexcharts-tooltip-title {\n  background-color: var(--bs-border-color) !important;\n  border-bottom: 1px solid var(--bs-border-color) !important;\n}\n\n.apexcharts-pie-area {\n  stroke: var(--bs-secondary-bg);\n}\n\n.apexcharts-grid-borders line {\n  stroke: var(--bs-border-color);\n}\n\n.apexcharts-pie-label {\n  fill: var(--bs-white) !important;\n}\n\n.apexcharts-xaxis-tick {\n  stroke: var(--bs-border-color);\n}\n\n.e-charts {\n  height: 350px;\n}\n\n/* Flot chart */\n.flot-charts-height {\n  height: 320px;\n}\n\n.flotTip {\n  padding: 8px 12px;\n  background-color: rgba(52, 58, 64, 0.9);\n  z-index: 100;\n  color: #f8f9fa;\n  box-shadow: 0 0.75rem 1.5rem rgba(18, 38, 63, 0.03);\n  border-radius: 4px;\n}\n\n.legendLabel {\n  color: #adb5bd;\n}\n\n.jqstooltip {\n  box-sizing: content-box;\n  width: auto !important;\n  height: auto !important;\n  background-color: #343a40 !important;\n  box-shadow: 0 1rem 3rem rgba(0, 0, 0, 0.175);\n  padding: 5px 10px !important;\n  border-radius: 3px;\n  border-color: #212529 !important;\n}\n\n.jqsfield {\n  color: #eff2f7 !important;\n  font-size: 12px !important;\n  line-height: 18px !important;\n  font-family: var(--bs-font-sans-serif) !important;\n  font-weight: 500 !important;\n}\n\n.gmaps, .gmaps-panaroma {\n  height: 300px !important;\n  background: #f8f9fa;\n  border-radius: 3px;\n}\n\n.gmaps-overlay {\n  display: block;\n  text-align: center;\n  color: #fff;\n  font-size: 16px;\n  line-height: 40px;\n  background: #556ee6;\n  border-radius: 4px;\n  padding: 10px 20px;\n}\n\n.gmaps-overlay_arrow {\n  left: 50%;\n  margin-left: -16px;\n  width: 0;\n  height: 0;\n  position: absolute;\n}\n.gmaps-overlay_arrow.above {\n  bottom: -15px;\n  border-left: 16px solid transparent;\n  border-right: 16px solid transparent;\n  border-top: 16px solid #556ee6;\n}\n.gmaps-overlay_arrow.below {\n  top: -15px;\n  border-left: 16px solid transparent;\n  border-right: 16px solid transparent;\n  border-bottom: 16px solid #556ee6;\n}\n\n.jvectormap-label {\n  border: none;\n  background: #343a40;\n  color: #f8f9fa;\n  font-family: var(--bs-font-sans-serif);\n  font-size: 0.8125rem;\n  padding: 5px 8px;\n}\n\n.leaflet-map {\n  height: 300px;\n}\n.leaflet-map.leaflet-container {\n  z-index: 99;\n}\n\n.home-btn {\n  position: absolute;\n  top: 15px;\n  right: 25px;\n}\n\n.auth-logo .auth-logo-dark {\n  display: var(--bs-display-block);\n}\n.auth-logo .auth-logo-light {\n  display: var(--bs-display-none);\n}\n\n.auth-body-bg {\n  background-color: var(--bs-secondary-bg);\n}\n\n.auth-pass-inputgroup input[type=input] + .btn .mdi-eye-outline:before {\n  content: \"\\f06d1\";\n}\n\n.auth-full-bg {\n  background-color: rgba(85, 110, 230, 0.25);\n  display: flex;\n}\n@media (min-width: 1200px) {\n  .auth-full-bg {\n    height: 100vh;\n  }\n}\n.auth-full-bg::before {\n  content: \"\";\n  position: absolute;\n  width: 300px;\n  height: 300px;\n  border-radius: 50%;\n}\n.auth-full-bg .bg-overlay {\n  background: url(\"../images/bg-auth-overlay.png\");\n  background-size: cover;\n  background-repeat: no-repeat;\n  background-position: center;\n}\n\n.auth-full-page-content {\n  display: flex;\n}\n@media (min-width: 1200px) {\n  .auth-full-page-content {\n    min-height: 100vh;\n  }\n}\n\n.auth-review-carousel.owl-theme .owl-dots .owl-dot span {\n  background-color: rgba(85, 110, 230, 0.25);\n}\n.auth-review-carousel.owl-theme .owl-dots .owl-dot.active span, .auth-review-carousel.owl-theme .owl-dots .owl-dot:hover span {\n  background-color: #556ee6;\n}\n\n.search-box .form-control {\n  border-radius: 30px;\n  padding-left: 40px;\n}\n.search-box .search-icon {\n  font-size: 16px;\n  position: absolute;\n  left: 13px;\n  top: 0;\n  line-height: 38px;\n}\n\n.product-list li a {\n  display: block;\n  padding: 4px 0px;\n  color: var(--bs-body-color);\n}\n\n.product-view-nav.nav-pills .nav-item {\n  margin-left: 4px;\n}\n.product-view-nav.nav-pills .nav-link {\n  width: 36px;\n  height: 36px;\n  font-size: 16px;\n  padding: 0;\n  line-height: 36px;\n  text-align: center;\n  border-radius: 50%;\n}\n\n.product-ribbon {\n  position: absolute;\n  right: 0px;\n  top: 0px;\n}\n\n.product-detai-imgs .nav .nav-link {\n  margin: 7px 0px;\n}\n.product-detai-imgs .nav .nav-link.active {\n  background-color: var(--bs-tertiary-bg);\n}\n\n.product-color a {\n  display: inline-block;\n  text-align: center;\n  color: var(--bs-body-color);\n}\n.product-color a .product-color-item {\n  margin: 7px;\n}\n.product-color a.active, .product-color a:hover {\n  color: #556ee6;\n}\n.product-color a.active .product-color-item, .product-color a:hover .product-color-item {\n  border-color: #556ee6 !important;\n}\n\n.visa-card .visa-logo {\n  line-height: 0.5;\n}\n.visa-card .visa-pattern {\n  position: absolute;\n  font-size: 385px;\n  color: rgba(255, 255, 255, 0.05);\n  line-height: 0.4;\n  right: 0px;\n  bottom: 0px;\n}\n\n.checkout-tabs .nav-pills .nav-link {\n  margin-bottom: 24px;\n  text-align: center;\n  background-color: var(--bs-secondary-bg);\n  box-shadow: 0 0.75rem 1.5rem rgba(18, 38, 63, 0.03);\n}\n.checkout-tabs .nav-pills .nav-link.active {\n  background-color: #556ee6;\n}\n.checkout-tabs .nav-pills .nav-link .check-nav-icon {\n  font-size: 36px;\n}\n\n/* ==============\n  Email\n===================*/\n.email-leftbar {\n  width: 236px;\n  float: left;\n  padding: 20px;\n  border-radius: 5px;\n}\n\n.email-rightbar {\n  margin-left: 260px;\n}\n\n.chat-user-box p.user-title {\n  color: var(--bs-emphasis-color);\n  font-weight: 500;\n}\n.chat-user-box p {\n  font-size: 12px;\n}\n\n@media (max-width: 767px) {\n  .email-leftbar {\n    float: none;\n    width: 100%;\n  }\n  .email-rightbar {\n    margin: 0;\n  }\n}\n.mail-list a {\n  display: block;\n  color: var(--bs-secondary-color);\n  line-height: 24px;\n  padding: 8px 5px;\n}\n.mail-list a.active {\n  color: #f46a6a;\n  font-weight: 500;\n}\n\n.message-list {\n  display: block;\n  padding-left: 0;\n}\n.message-list li {\n  position: relative;\n  display: block;\n  height: 50px;\n  line-height: 50px;\n  cursor: default;\n  transition-duration: 0.3s;\n}\n.message-list li a {\n  color: var(--bs-secondary-color);\n}\n.message-list li:hover {\n  background: var(--bs-tertiary-bg);\n  transition-duration: 0.05s;\n}\n.message-list li .col-mail {\n  float: left;\n  position: relative;\n}\n.message-list li .col-mail-1 {\n  width: 320px;\n}\n.message-list li .col-mail-1 .star-toggle,\n.message-list li .col-mail-1 .checkbox-wrapper-mail,\n.message-list li .col-mail-1 .dot {\n  display: block;\n  float: left;\n}\n.message-list li .col-mail-1 .dot {\n  border: 4px solid transparent;\n  border-radius: 100px;\n  margin: 22px 26px 0;\n  height: 0;\n  width: 0;\n  line-height: 0;\n  font-size: 0;\n}\n.message-list li .col-mail-1 .checkbox-wrapper-mail {\n  margin: 15px 10px 0 20px;\n}\n.message-list li .col-mail-1 .star-toggle {\n  margin-top: 18px;\n  margin-left: 5px;\n}\n.message-list li .col-mail-1 .title {\n  position: absolute;\n  top: 0;\n  left: 110px;\n  right: 0;\n  text-overflow: ellipsis;\n  overflow: hidden;\n  white-space: nowrap;\n  margin-bottom: 0;\n}\n.message-list li .col-mail-2 {\n  position: absolute;\n  top: 0;\n  left: 320px;\n  right: 0;\n  bottom: 0;\n}\n.message-list li .col-mail-2 .subject,\n.message-list li .col-mail-2 .date {\n  position: absolute;\n  top: 0;\n}\n.message-list li .col-mail-2 .subject {\n  left: 0;\n  right: 200px;\n  text-overflow: ellipsis;\n  overflow: hidden;\n  white-space: nowrap;\n}\n.message-list li .col-mail-2 .date {\n  right: 0;\n  width: 170px;\n  padding-left: 80px;\n}\n.message-list li.active, .message-list li.active:hover {\n  box-shadow: inset 3px 0 0 #556ee6;\n}\n.message-list li.unread {\n  background-color: var(--bs-tertiary-bg);\n  font-weight: 500;\n  color: var(--bs-body-color);\n}\n.message-list li.unread a {\n  color: var(--bs-body-color);\n  font-weight: 500;\n}\n.message-list .checkbox-wrapper-mail {\n  cursor: pointer;\n  height: 20px;\n  width: 20px;\n  position: relative;\n  display: inline-block;\n  background-color: var(--bs-secondary-bg);\n  box-shadow: inset 0 0 0 1px var(--bs-border-color-translucent);\n  border-radius: 1px;\n}\n.message-list .checkbox-wrapper-mail input {\n  opacity: 0;\n  cursor: pointer;\n}\n.message-list .checkbox-wrapper-mail input:checked ~ label {\n  opacity: 1;\n}\n.message-list .checkbox-wrapper-mail label {\n  position: absolute;\n  height: 20px;\n  width: 20px;\n  left: 0;\n  cursor: pointer;\n  opacity: 0;\n  margin-bottom: 0;\n  transition-duration: 0.05s;\n  top: 0;\n}\n.message-list .checkbox-wrapper-mail label:before {\n  content: \"\\f012c\";\n  font-family: \"Material Design Icons\";\n  top: 0;\n  height: 20px;\n  color: var(--bs-body-color);\n  width: 20px;\n  position: absolute;\n  margin-top: -16px;\n  left: 4px;\n  font-size: 13px;\n}\n\n@media (max-width: 575.98px) {\n  .message-list li .col-mail-1 {\n    width: 200px;\n  }\n}\n@media (min-width: 1200px) {\n  .filemanager-sidebar {\n    min-width: 230px;\n    max-width: 230px;\n  }\n}\n@media (min-width: 1366px) {\n  .filemanager-sidebar {\n    min-width: 280px;\n    max-width: 280px;\n  }\n}\n\n.categories-list {\n  padding: 4px 0;\n}\n.categories-list li a {\n  display: block;\n  padding: 8px 12px;\n  color: var(--bs-body-color);\n  font-weight: 500;\n}\n.categories-list li.active a {\n  color: #556ee6;\n}\n.categories-list li ul {\n  padding-left: 16px;\n}\n.categories-list li ul li a {\n  padding: 4px 12px;\n  color: var(--bs-secondary-color);\n  font-size: 13px;\n  font-weight: 400;\n}\n\n@media (min-width: 992px) {\n  .chat-leftsidebar {\n    min-width: 260px;\n  }\n}\n@media (min-width: 1200px) {\n  .chat-leftsidebar {\n    min-width: 380px;\n  }\n}\n.chat-leftsidebar .chat-leftsidebar-nav .nav {\n  background-color: var(--bs-secondary-bg);\n}\n.chat-leftsidebar .chat-leftsidebar-nav .tab-content {\n  min-height: 488px;\n}\n\n.chat-noti-dropdown.active:before {\n  content: \"\";\n  position: absolute;\n  width: 8px;\n  height: 8px;\n  background-color: #f46a6a;\n  border-radius: 50%;\n  right: 0;\n}\n.chat-noti-dropdown .btn {\n  padding: 6px;\n  box-shadow: none;\n  font-size: 20px;\n}\n\n.chat-search-box .form-control {\n  border: 0;\n}\n\n.chat-list {\n  margin: 0;\n}\n.chat-list li.active a {\n  background-color: var(--bs-secondary-bg);\n  border-color: transparent;\n  box-shadow: 0 0.75rem 1.5rem rgba(18, 38, 63, 0.03);\n}\n.chat-list li a {\n  display: block;\n  padding: 14px 16px;\n  color: var(--bs-secondary-color);\n  transition: all 0.4s;\n  border-top: 1px solid var(--bs-border-color);\n  border-radius: 4px;\n}\n.chat-list li a:hover {\n  background-color: var(--bs-secondary-bg);\n  border-color: transparent;\n  box-shadow: 0 0.75rem 1.5rem rgba(18, 38, 63, 0.03);\n}\n\n.user-chat-nav .dropdown .nav-btn {\n  color: var(--bs-body-color);\n  height: 40px;\n  width: 40px;\n  line-height: 42px;\n  box-shadow: none;\n  padding: 0;\n  font-size: 16px;\n  background-color: var(--bs-light);\n  border-radius: 50%;\n  border: none;\n}\n.user-chat-nav .dropdown .dropdown-menu {\n  box-shadow: 0 0.75rem 1.5rem rgba(18, 38, 63, 0.03);\n  border: 1px solid var(--bs-border-color);\n}\n\n.chat-conversation li {\n  clear: both;\n}\n.chat-conversation .chat-day-title {\n  position: relative;\n  text-align: center;\n  margin-bottom: 24px;\n}\n.chat-conversation .chat-day-title .title {\n  background-color: var(--bs-secondary-bg);\n  position: relative;\n  z-index: 1;\n  padding: 6px 24px;\n}\n.chat-conversation .chat-day-title:before {\n  content: \"\";\n  position: absolute;\n  width: 100%;\n  height: 1px;\n  left: 0;\n  right: 0;\n  background-color: var(--bs-border-color);\n  top: 10px;\n}\n.chat-conversation .chat-day-title .badge {\n  font-size: 12px;\n}\n.chat-conversation .conversation-list {\n  margin-bottom: 24px;\n  display: inline-block;\n  position: relative;\n}\n.chat-conversation .conversation-list .ctext-wrap {\n  padding: 12px 24px;\n  background-color: rgba(85, 110, 230, 0.1);\n  border-radius: 8px 8px 8px 0px;\n  overflow: hidden;\n}\n.chat-conversation .conversation-list .ctext-wrap .conversation-name {\n  font-weight: 600;\n  color: #556ee6;\n  margin-bottom: 4px;\n}\n.chat-conversation .conversation-list .dropdown {\n  float: right;\n}\n.chat-conversation .conversation-list .dropdown .dropdown-toggle {\n  font-size: 18px;\n  padding: 4px;\n  color: var(--bs-secondary-color);\n}\n@media (max-width: 575.98px) {\n  .chat-conversation .conversation-list .dropdown .dropdown-toggle {\n    display: none;\n  }\n}\n.chat-conversation .conversation-list .dropdown .dropdown-menu {\n  box-shadow: 0 0.75rem 1.5rem rgba(18, 38, 63, 0.03);\n  border: 1px solid var(--bs-border-color);\n}\n.chat-conversation .conversation-list .chat-time {\n  font-size: 12px;\n}\n.chat-conversation .right .conversation-list {\n  float: right;\n}\n.chat-conversation .right .conversation-list .ctext-wrap {\n  background-color: var(--bs-light);\n  text-align: right;\n  border-radius: 8px 8px 0px 8px;\n}\n.chat-conversation .right .conversation-list .dropdown {\n  float: left;\n}\n.chat-conversation .right .conversation-list.last-chat .conversation-list:before {\n  right: 0;\n  left: auto;\n}\n.chat-conversation .last-chat .conversation-list:before {\n  content: \"\\f0009\";\n  font-family: \"Material Design Icons\";\n  position: absolute;\n  color: #556ee6;\n  right: 0;\n  bottom: 0;\n  font-size: 16px;\n}\n@media (max-width: 575.98px) {\n  .chat-conversation .last-chat .conversation-list:before {\n    display: none;\n  }\n}\n\n.chat-input-section {\n  border-top: 1px solid var(--bs-border-color);\n}\n\n.chat-input {\n  border-radius: 30px;\n  background-color: var(--bs-light) !important;\n  border-color: var(--bs-light) !important;\n  padding-right: 120px;\n}\n\n.chat-input-links {\n  position: absolute;\n  right: 16px;\n  top: 50%;\n  transform: translateY(-50%);\n}\n.chat-input-links li a {\n  font-size: 16px;\n  line-height: 36px;\n  padding: 0px 4px;\n  display: inline-block;\n}\n\n@media (max-width: 575.98px) {\n  .chat-send {\n    min-width: auto;\n  }\n}\n\n.project-list-table {\n  border-collapse: separate;\n  border-spacing: 0 12px;\n}\n.project-list-table tr {\n  background-color: var(--bs-secondary-bg);\n}\n\n.contact-links a {\n  color: var(--bs-body-color);\n}\n\n.profile-user-wid {\n  margin-top: -26px;\n}\n\n@media (min-width: 576px) {\n  .currency-value {\n    position: relative;\n  }\n  .currency-value:after {\n    content: \"\\f04e1\";\n    font-family: \"Material Design Icons\";\n    font-size: 24px;\n    position: absolute;\n    width: 45px;\n    height: 45px;\n    line-height: 45px;\n    border-radius: 50%;\n    text-align: center;\n    right: 0;\n    top: 50%;\n    transform: translateY(-50%);\n    background-color: #556ee6;\n    color: #fff;\n    z-index: 9;\n    right: -34px;\n  }\n}\n.crypto-buy-sell-nav-content {\n  border: 2px solid var(--bs-border-color);\n  border-top: 0;\n}\n\n.kyc-doc-verification .dropzone {\n  min-height: 180px;\n}\n.kyc-doc-verification .dropzone .dz-message {\n  margin: 24px 0px;\n}\n\n/******************\n    Ico Landing\n*******************/\n.section {\n  position: relative;\n  padding-top: 80px;\n  padding-bottom: 80px;\n}\n.section.bg-white {\n  background-color: var(--bs-secondary-bg) !important;\n}\n\n.small-title {\n  color: var(--bs-secondary-color);\n  margin-bottom: 8px;\n}\n\n.navigation {\n  padding: 0 16px;\n  width: 100%;\n  z-index: 999;\n  margin-bottom: 0px;\n  transition: all 0.5s ease-in-out;\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n}\n@media (max-width: 991.98px) {\n  .navigation {\n    background-color: var(--bs-topnav-bg);\n  }\n}\n.navigation .navbar-logo {\n  line-height: 70px;\n  transition: all 0.4s;\n}\n.navigation .navbar-logo .logo-dark {\n  display: none;\n}\n@media (max-width: 991.98px) {\n  .navigation .navbar-logo .logo-dark {\n    display: block;\n  }\n}\n.navigation .navbar-logo .logo-light {\n  display: block;\n}\n@media (max-width: 991.98px) {\n  .navigation .navbar-logo .logo-light {\n    display: none;\n  }\n}\n.navigation .navbar-nav .nav-item .nav-link {\n  color: rgba(255, 255, 255, 0.6);\n  line-height: 58px;\n  padding: 6px 16px;\n  font-weight: 500;\n  transition: all 0.4s;\n}\n@media (max-width: 991.98px) {\n  .navigation .navbar-nav .nav-item .nav-link {\n    color: var(--bs-header-item-color);\n  }\n}\n.navigation .navbar-nav .nav-item .nav-link:hover, .navigation .navbar-nav .nav-item .nav-link.active {\n  color: rgba(255, 255, 255, 0.9);\n}\n@media (max-width: 991.98px) {\n  .navigation .navbar-nav .nav-item .nav-link:hover, .navigation .navbar-nav .nav-item .nav-link.active {\n    color: #556ee6;\n  }\n}\n@media (max-width: 991.98px) {\n  .navigation .navbar-nav .nav-item .nav-link {\n    line-height: 28px !important;\n  }\n}\n.navigation.nav-sticky {\n  background-color: var(--bs-topnav-bg);\n  box-shadow: 0 0.75rem 1.5rem rgba(18, 38, 63, 0.03);\n}\n.navigation.nav-sticky .navbar-logo {\n  line-height: 60px;\n}\n.navigation.nav-sticky .navbar-logo .logo-dark {\n  display: var(--bs-display-block);\n}\n.navigation.nav-sticky .navbar-logo .logo-light {\n  display: var(--bs-display-none);\n}\n.navigation.nav-sticky .navbar-nav .nav-item .nav-link {\n  line-height: 48px;\n  color: var(--bs-header-item-color);\n}\n.navigation.nav-sticky .navbar-nav .nav-item .nav-link:hover, .navigation.nav-sticky .navbar-nav .nav-item .nav-link.active {\n  color: #556ee6;\n}\n\n.bg-overlay {\n  position: absolute;\n  height: 100%;\n  width: 100%;\n  right: 0;\n  bottom: 0;\n  left: 0;\n  top: 0;\n  opacity: 0.7;\n  background-color: #000;\n}\n\n.hero-section {\n  padding-top: 220px;\n  padding-bottom: 190px;\n}\n.hero-section.bg-ico-hero {\n  background-image: url(\"../images/crypto/bg-ico-hero.jpg\");\n  background-size: cover;\n  background-position: top;\n}\n@media (max-width: 575.98px) {\n  .hero-section {\n    padding-top: 140px;\n    padding-bottom: 80px;\n  }\n}\n.hero-section .hero-title {\n  font-size: 42px;\n}\n@media (max-width: 575.98px) {\n  .hero-section .hero-title {\n    font-size: 26px;\n  }\n}\n.hero-section .ico-countdown {\n  font-size: 22px;\n  margin-right: -12px;\n  margin-left: -12px;\n}\n@media (max-width: 575.98px) {\n  .hero-section .ico-countdown {\n    display: block;\n  }\n}\n.hero-section .ico-countdown .coming-box {\n  margin-right: 12px;\n  margin-left: 12px;\n  border: 1px solid var(--bs-border-color);\n  border-radius: 4px;\n  padding: 8px;\n  background-color: var(--bs-secondary-bg);\n}\n@media (max-width: 575.98px) {\n  .hero-section .ico-countdown .coming-box {\n    display: inline-block;\n    width: 40%;\n    margin-bottom: 24px;\n  }\n}\n.hero-section .ico-countdown .coming-box span {\n  background-color: var(--bs-light);\n  font-size: 12px;\n  padding: 4px;\n  margin-top: 8px;\n}\n.hero-section .softcap-progress {\n  overflow: visible;\n}\n.hero-section .softcap-progress .progress-bar {\n  overflow: visible;\n}\n.hero-section .softcap-progress .progress-label {\n  position: relative;\n  text-align: right;\n  color: var(--bs-body-color);\n  bottom: 20px;\n  font-size: 12px;\n  font-weight: 500;\n}\n\n.currency-price {\n  position: relative;\n  bottom: 40px;\n}\n\n.client-images img {\n  max-height: 34px;\n  width: auto !important;\n  margin: 12px auto;\n  opacity: 0.7;\n  transition: all 0.4s;\n}\n\n.features-number {\n  opacity: 0.1;\n}\n\n.team-box .team-social-links a {\n  color: var(--bs-body-color);\n  font-size: 14px;\n}\n\n.blog-box .blog-badge {\n  position: absolute;\n  top: 12px;\n  right: 12px;\n}\n\n.landing-footer {\n  padding: 80px 0 40px;\n  background-color: #2a3042;\n  color: rgba(255, 255, 255, 0.5);\n}\n.landing-footer .footer-list-title {\n  color: rgba(255, 255, 255, 0.9);\n}\n.landing-footer .footer-list-menu li a {\n  display: block;\n  color: rgba(255, 255, 255, 0.5);\n  margin-bottom: 14px;\n  transition: all 0.4s;\n}\n.landing-footer .footer-list-menu li a:hover {\n  color: rgba(255, 255, 255, 0.8);\n}\n.landing-footer .blog-post .post {\n  display: block;\n  color: rgba(255, 255, 255, 0.5);\n  padding: 16px 0px;\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\n}\n.landing-footer .blog-post .post .post-title {\n  color: rgba(255, 255, 255, 0.8);\n  font-size: 14px;\n}\n.landing-footer .blog-post .post:first-of-type {\n  padding-top: 0;\n}\n.landing-footer .blog-post .post:last-of-type {\n  padding-bottom: 0;\n  border-bottom: 0;\n}\n.landing-footer .footer-border {\n  border-color: rgba(255, 255, 255, 0.1);\n}\n\n.counter-number {\n  font-size: 32px;\n  font-weight: 600;\n  text-align: center;\n  display: flex;\n}\n.counter-number span {\n  font-size: 16px;\n  font-weight: 400;\n  display: block;\n  padding-top: 5px;\n}\n\n.coming-box {\n  width: 25%;\n}\n\n/************** Horizontal timeline **************/\n.hori-timeline .events .event-list {\n  text-align: center;\n  display: block;\n}\n.hori-timeline .events .event-list .event-down-icon {\n  position: relative;\n}\n.hori-timeline .events .event-list .event-down-icon::before {\n  content: \"\";\n  position: absolute;\n  width: 100%;\n  top: 16px;\n  left: 0;\n  right: 0;\n  border-bottom: 3px dashed var(--bs-border-color);\n}\n.hori-timeline .events .event-list .event-down-icon .down-arrow-icon {\n  position: relative;\n  background-color: var(--bs-secondary-bg);\n  padding: 4px;\n}\n.hori-timeline .events .event-list:hover .down-arrow-icon {\n  animation: fade-down 1.5s infinite linear;\n}\n.hori-timeline .events .event-list.active .down-arrow-icon {\n  animation: fade-down 1.5s infinite linear;\n}\n.hori-timeline .events .event-list.active .down-arrow-icon:before {\n  content: \"\\ec4c\";\n}\n\n/************** vertical timeline **************/\n.verti-timeline {\n  border-left: 3px dashed var(--bs-border-color);\n  margin: 0 10px;\n}\n.verti-timeline .event-list {\n  position: relative;\n  padding: 0px 0px 40px 30px;\n}\n.verti-timeline .event-list .event-timeline-dot {\n  position: absolute;\n  left: -9px;\n  top: 0px;\n  z-index: 9;\n  font-size: 16px;\n}\n.verti-timeline .event-list .event-content {\n  position: relative;\n  border: 2px solid var(--bs-border-color);\n  border-radius: 7px;\n}\n.verti-timeline .event-list.active .event-timeline-dot {\n  color: #556ee6;\n}\n.verti-timeline .event-list:last-child {\n  padding-bottom: 0px;\n}\n\n.plan-box .plan-btn {\n  position: relative;\n}\n.plan-box .plan-btn::before {\n  content: \"\";\n  position: absolute;\n  width: 100%;\n  height: 2px;\n  background: var(--bs-border-color);\n  left: 0px;\n  right: 0px;\n  top: 12px;\n}\n\n.blog-play-icon {\n  position: absolute;\n  top: 50%;\n  left: 0;\n  right: 0;\n  transform: translateY(-50%);\n  margin: 0px auto;\n}\n\n.jobs-categories a {\n  color: var(--bs-body-color);\n  transition: all 0.5s ease;\n}\n.jobs-categories a:hover {\n  color: #556ee6;\n}", "// \r\n// Page-title\r\n// \r\n\r\n.page-title-box {\r\n    padding-bottom: $grid-gutter-width;\r\n\r\n    .breadcrumb {\r\n        background-color: transparent;\r\n        padding: 0;\r\n    }\r\n\r\n    h4 {\r\n        text-transform: uppercase;\r\n        font-weight: 600;\r\n        font-size: 16px !important;\r\n    }\r\n}", "// \r\n// _footer.scss\r\n// \r\n\r\n.footer {\r\n    bottom: 0;\r\n    padding: 20px calc(#{$grid-gutter-width} * 0.75);\r\n    position: absolute;\r\n    right: 0;\r\n    color: var(--#{$prefix}footer-color);\r\n    left: 250px;\r\n    height: $footer-height;\r\n    background-color: var(--#{$prefix}footer-bg);\r\n\r\n    @media (max-width: 991.98px) {\r\n        left: 0;\r\n    }\r\n}\r\n  \r\n// Enlarge menu\r\n.vertical-collpsed {\r\n    .footer {\r\n        left: $sidebar-collapsed-width;\r\n\r\n        @media (max-width: 991.98px) {\r\n            left: 0;\r\n        }\r\n    }\r\n}\r\n\r\nbody[data-layout=\"horizontal\"] {\r\n    .footer {\r\n        left: 0 !important;\r\n    }  \r\n}", "//\r\n// right-sidebar.scss\r\n//\r\n\r\n.right-bar {\r\n    background-color: var(--#{$prefix}secondary-bg);\r\n    box-shadow: 0 0 24px 0 rgba(0, 0, 0, 0.06), 0 1px 0 0 rgba(0, 0, 0, 0.02);\r\n    display: block;\r\n    position: fixed;\r\n    transition: all 200ms ease-out;\r\n    width: $rightbar-width;\r\n    z-index: 9999;\r\n    float: right !important;\r\n    right: -($rightbar-width + 10px);\r\n    top: 0;\r\n    bottom: 0;\r\n\r\n    .right-bar-toggle {\r\n        background-color: var(--#{$prefix}dark);\r\n        height: 24px;\r\n        width: 24px;\r\n        line-height: 24px;\r\n        display: block;\r\n        color: var(--#{$prefix}gray-200);\r\n        text-align: center;\r\n        border-radius: 50%;\r\n\r\n        &:hover {\r\n            background-color: var(--#{$prefix}dark);\r\n        }\r\n    }\r\n}\r\n\r\n// Rightbar overlay\r\n.rightbar-overlay {\r\n    background-color: rgba($dark, 0.55);\r\n    position: absolute;\r\n    left: 0;\r\n    right: 0;\r\n    top: 0;\r\n    bottom: 0;\r\n    display: none;\r\n    z-index: 9998;\r\n    transition: all .2s ease-out;\r\n}\r\n\r\n.right-bar-enabled {\r\n    .right-bar {\r\n        right: 0;\r\n    }\r\n    .rightbar-overlay {\r\n        display: block;\r\n    }\r\n}\r\n\r\n@include media-breakpoint-down(md) {\r\n    .right-bar {\r\n        overflow: auto;\r\n        .slimscroll-menu {\r\n            height: auto !important;\r\n        }\r\n    }\r\n}", "// Breakpoint viewport sizes and media queries.\n//\n// Breakpoints are defined as a map of (name: minimum width), order from small to large:\n//\n//    (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px)\n//\n// The map defined in the `$grid-breakpoints` global variable is used as the `$breakpoints` argument by default.\n\n// Name of the next breakpoint, or null for the last breakpoint.\n//\n//    >> breakpoint-next(sm)\n//    md\n//    >> breakpoint-next(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px))\n//    md\n//    >> breakpoint-next(sm, $breakpoint-names: (xs sm md lg xl xxl))\n//    md\n@function breakpoint-next($name, $breakpoints: $grid-breakpoints, $breakpoint-names: map-keys($breakpoints)) {\n  $n: index($breakpoint-names, $name);\n  @if not $n {\n    @error \"breakpoint `#{$name}` not found in `#{$breakpoints}`\";\n  }\n  @return if($n < length($breakpoint-names), nth($breakpoint-names, $n + 1), null);\n}\n\n// Minimum breakpoint width. Null for the smallest (first) breakpoint.\n//\n//    >> breakpoint-min(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px))\n//    576px\n@function breakpoint-min($name, $breakpoints: $grid-breakpoints) {\n  $min: map-get($breakpoints, $name);\n  @return if($min != 0, $min, null);\n}\n\n// Maximum breakpoint width.\n// The maximum value is reduced by 0.02px to work around the limitations of\n// `min-` and `max-` prefixes and viewports with fractional widths.\n// See https://www.w3.org/TR/mediaqueries-4/#mq-min-max\n// Uses 0.02px rather than 0.01px to work around a current rounding bug in Safari.\n// See https://bugs.webkit.org/show_bug.cgi?id=178261\n//\n//    >> breakpoint-max(md, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px))\n//    767.98px\n@function breakpoint-max($name, $breakpoints: $grid-breakpoints) {\n  $max: map-get($breakpoints, $name);\n  @return if($max and $max > 0, $max - .02, null);\n}\n\n// Returns a blank string if smallest breakpoint, otherwise returns the name with a dash in front.\n// Useful for making responsive utilities.\n//\n//    >> breakpoint-infix(xs, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px))\n//    \"\"  (Returns a blank string)\n//    >> breakpoint-infix(sm, (xs: 0, sm: 576px, md: 768px, lg: 992px, xl: 1200px, xxl: 1400px))\n//    \"-sm\"\n@function breakpoint-infix($name, $breakpoints: $grid-breakpoints) {\n  @return if(breakpoint-min($name, $breakpoints) == null, \"\", \"-#{$name}\");\n}\n\n// Media of at least the minimum breakpoint width. No query for the smallest breakpoint.\n// Makes the @content apply to the given breakpoint and wider.\n@mixin media-breakpoint-up($name, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($name, $breakpoints);\n  @if $min {\n    @media (min-width: $min) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media of at most the maximum breakpoint width. No query for the largest breakpoint.\n// Makes the @content apply to the given breakpoint and narrower.\n@mixin media-breakpoint-down($name, $breakpoints: $grid-breakpoints) {\n  $max: breakpoint-max($name, $breakpoints);\n  @if $max {\n    @media (max-width: $max) {\n      @content;\n    }\n  } @else {\n    @content;\n  }\n}\n\n// Media that spans multiple breakpoint widths.\n// Makes the @content apply between the min and max breakpoints\n@mixin media-breakpoint-between($lower, $upper, $breakpoints: $grid-breakpoints) {\n  $min: breakpoint-min($lower, $breakpoints);\n  $max: breakpoint-max($upper, $breakpoints);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($lower, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($upper, $breakpoints) {\n      @content;\n    }\n  }\n}\n\n// Media between the breakpoint's minimum and maximum widths.\n// No minimum for the smallest breakpoint, and no maximum for the largest one.\n// Makes the @content apply only to the given breakpoint, not viewports any wider or narrower.\n@mixin media-breakpoint-only($name, $breakpoints: $grid-breakpoints) {\n  $min:  breakpoint-min($name, $breakpoints);\n  $next: breakpoint-next($name, $breakpoints);\n  $max:  breakpoint-max($next, $breakpoints);\n\n  @if $min != null and $max != null {\n    @media (min-width: $min) and (max-width: $max) {\n      @content;\n    }\n  } @else if $max == null {\n    @include media-breakpoint-up($name, $breakpoints) {\n      @content;\n    }\n  } @else if $min == null {\n    @include media-breakpoint-down($next, $breakpoints) {\n      @content;\n    }\n  }\n}\n", "//\r\n// _menu.scss\r\n// \r\n\r\n.metismenu {\r\n    margin: 0;\r\n\r\n    li {\r\n        display: block;\r\n        width: 100%;\r\n    }\r\n\r\n    .mm-collapse {\r\n        display: none;\r\n\r\n        &:not(.mm-show) {\r\n            display: none;\r\n        }\r\n\r\n        &.mm-show {\r\n            display: block\r\n        }\r\n    }\r\n\r\n    .mm-collapsing {\r\n        position: relative;\r\n        height: 0;\r\n        overflow: hidden;\r\n        transition-timing-function: ease;\r\n        transition-duration: .35s;\r\n        transition-property: height, visibility;\r\n    }\r\n}\r\n\r\n\r\n.vertical-menu {\r\n    width: $sidebar-width;\r\n    z-index: 1001;\r\n    background: $sidebar-bg;\r\n    bottom: 0;\r\n    margin-top: 0;\r\n    position: fixed;\r\n    top: $header-height;\r\n    box-shadow: $box-shadow;\r\n}\r\n\r\n.main-content {\r\n    margin-left: $sidebar-width;\r\n    overflow: hidden;\r\n\r\n    .content {\r\n        padding: 0 15px 10px 15px;\r\n        margin-top: $header-height;\r\n    }\r\n}\r\n\r\n\r\n#sidebar-menu {\r\n    padding: 10px 0 30px 0;\r\n\r\n    .mm-active {\r\n        >.has-arrow {\r\n            &:after {\r\n                transform: rotate(-180deg);\r\n            }\r\n        }\r\n    }\r\n\r\n    .has-arrow {\r\n        &:after {\r\n            content: \"\\F0140\";\r\n            font-family: 'Material Design Icons';\r\n            display: block;\r\n            float: right;\r\n            transition: transform .2s;\r\n            font-size: 1rem;\r\n        }\r\n    }\r\n\r\n    ul {\r\n        li {\r\n            a {\r\n                display: block;\r\n                padding: .625rem 1.5rem;\r\n                color: $sidebar-menu-item-color;\r\n                position: relative;\r\n                font-size: 13px;\r\n                transition: all .4s;\r\n\r\n                i {\r\n                    display: inline-block;\r\n                    min-width: 1.75rem;\r\n                    padding-bottom: .125em;\r\n                    font-size: 1.25rem;\r\n                    line-height: 1.40625rem;\r\n                    vertical-align: middle;\r\n                    color: $sidebar-menu-item-icon-color;\r\n                    transition: all .4s;\r\n                }\r\n\r\n                &:hover {\r\n                    color: $sidebar-menu-item-hover-color;\r\n\r\n                    i {\r\n                        color: $sidebar-menu-item-hover-color;\r\n                    }\r\n                }\r\n            }\r\n\r\n            .badge {\r\n                margin-top: 4px;\r\n            }\r\n\r\n            ul.sub-menu {\r\n                padding: 0;\r\n\r\n                li {\r\n\r\n                    a {\r\n                        padding: .4rem 1.5rem .4rem 3.5rem;\r\n                        font-size: 13px;\r\n                        color: $sidebar-menu-sub-item-color;\r\n                        &:hover {\r\n                            color: $sidebar-menu-item-hover-color;\r\n                        }\r\n                    }\r\n\r\n                    ul.sub-menu {\r\n                        padding: 0;\r\n\r\n                        li {\r\n                            a {\r\n                                padding: .4rem 1.5rem .4rem 4.5rem;\r\n                                font-size: 13px;\r\n\r\n                                \r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n    }\r\n}\r\n\r\n.menu-title {\r\n    padding: 12px 20px !important;\r\n    letter-spacing: .05em;\r\n    pointer-events: none;\r\n    cursor: default;\r\n    font-size: 11px;\r\n    text-transform: uppercase;\r\n    color: $sidebar-menu-item-icon-color;\r\n    font-weight: $font-weight-semibold;\r\n}\r\n\r\n.mm-active {\r\n    color: $sidebar-menu-item-active-color !important;\r\n    > a{\r\n        color: $sidebar-menu-item-active-color !important;\r\n        i {\r\n            color: $sidebar-menu-item-active-color !important;\r\n        }\r\n    }\r\n    .active {\r\n        color: $sidebar-menu-item-active-color !important;\r\n\r\n        i {\r\n            color: $sidebar-menu-item-active-color !important;\r\n        }\r\n    }\r\n    > i {\r\n        color: $sidebar-menu-item-active-color !important;\r\n    }\r\n}\r\n\r\n@media (max-width: 992px) {\r\n    .vertical-menu {\r\n        display: none;\r\n    }\r\n\r\n    .main-content {\r\n        margin-left: 0 !important;\r\n    }\r\n\r\n    body.sidebar-enable {\r\n        .vertical-menu {\r\n            display: block;\r\n        }\r\n    }\r\n}\r\n\r\n// Enlarge menu\r\n.vertical-collpsed {\r\n\r\n    .main-content {\r\n        margin-left: $sidebar-collapsed-width;\r\n    }\r\n\r\n    .navbar-brand-box {\r\n        width: $sidebar-collapsed-width !important;\r\n    }\r\n\r\n    .logo {\r\n        span.logo-lg {\r\n            display: none;\r\n        }\r\n\r\n        span.logo-sm {\r\n            display: block;\r\n        }\r\n    }\r\n\r\n    // Side menu\r\n    .vertical-menu {\r\n        position: absolute;\r\n        width: $sidebar-collapsed-width !important;\r\n        z-index: 5;\r\n\r\n        .simplebar-mask,\r\n        .simplebar-content-wrapper {\r\n            overflow: visible !important;\r\n        }\r\n\r\n        .simplebar-scrollbar {\r\n            display: none !important;\r\n        }\r\n\r\n        .simplebar-offset {\r\n            bottom: 0 !important;\r\n        }\r\n\r\n        // Sidebar Menu\r\n        #sidebar-menu {\r\n\r\n            .menu-title,\r\n            .badge,\r\n            .collapse.in {\r\n                display: none !important;\r\n            }\r\n\r\n            .nav.collapse {\r\n                height: inherit !important;\r\n            }\r\n\r\n            .has-arrow {\r\n                &:after {\r\n                    display: none;\r\n                }\r\n            }\r\n\r\n            > ul {\r\n                > li {\r\n                    position: relative;\r\n                    white-space: nowrap;\r\n\r\n                    > a {\r\n                        padding: 15px 20px;\r\n                        min-height: 55px;\r\n                        transition: none;\r\n                        \r\n                        &:hover,\r\n                        &:active,\r\n                        &:focus {\r\n                            color: $sidebar-menu-item-hover-color;\r\n                        }\r\n\r\n                        i {\r\n                            font-size: 1.45rem;\r\n                            margin-left: 4px;\r\n                        }\r\n\r\n                        span {\r\n                            display: none;\r\n                            padding-left: 25px;\r\n                        }\r\n                    }\r\n\r\n                    &:hover {\r\n                        > a {\r\n                            position: relative;\r\n                            width: calc(190px + #{$sidebar-collapsed-width});\r\n                            color: $primary;\r\n                            background-color: darken($sidebar-bg, 4%);\r\n                            transition: none;\r\n\r\n                            i{\r\n                                color: $primary;\r\n                            }\r\n\r\n                            span {\r\n                                display: inline;\r\n                            }\r\n                        }\r\n\r\n                        >ul {\r\n                            display: block;\r\n                            left: $sidebar-collapsed-width;\r\n                            position: absolute;\r\n                            width: 190px;\r\n                            height: auto !important;\r\n                            box-shadow: 3px 5px 10px 0 rgba(54, 61, 71, .1);\r\n\r\n                            ul {\r\n                                box-shadow: 3px 5px 10px 0 rgba(54, 61, 71, .1);\r\n                            }\r\n\r\n                            a {\r\n                                box-shadow: none;\r\n                                padding: 8px 20px;\r\n                                position: relative;\r\n                                width: 190px;\r\n                                z-index: 6;\r\n                                color: $sidebar-menu-sub-item-color;\r\n\r\n                                &:hover {\r\n                                    color: $sidebar-menu-item-hover-color;\r\n                                }\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n\r\n                ul {\r\n                    padding: 5px 0;\r\n                    z-index: 9999;\r\n                    display: none;\r\n                    background-color: $sidebar-bg;\r\n\r\n                    li {\r\n                        &:hover {\r\n                            >ul {\r\n                                display: block;\r\n                                left: 190px;\r\n                                height: auto !important;\r\n                                margin-top: -36px;\r\n                                position: absolute;\r\n                                width: 190px;\r\n                            }\r\n                        }\r\n\r\n                        >a {\r\n                            span.pull-right {\r\n                                position: absolute;\r\n                                right: 20px;\r\n                                top: 12px;\r\n                                transform: rotate(270deg);\r\n                            }\r\n                        }\r\n                    }\r\n\r\n                    li.active {\r\n                        a {\r\n                            color: $gray-100;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n    }\r\n}\r\n\r\n\r\nbody[data-sidebar=\"dark\"] {\r\n    .vertical-menu {\r\n        background: $sidebar-dark-bg;\r\n    }\r\n\r\n    #sidebar-menu {\r\n    \r\n        ul {\r\n            li {\r\n                a {\r\n                    color: $sidebar-dark-menu-item-color;\r\n\r\n                    i {\r\n                        color: $sidebar-dark-menu-item-icon-color;\r\n                    }\r\n    \r\n                    &:hover {\r\n                        color: $sidebar-dark-menu-item-hover-color;\r\n\r\n                        i {\r\n                            color: $sidebar-dark-menu-item-hover-color;\r\n                        }\r\n                    }\r\n                }\r\n\r\n                ul.sub-menu {\r\n                    li {\r\n\r\n                        a {\r\n                            color: $sidebar-dark-menu-sub-item-color;\r\n\r\n                            &:hover {\r\n                                color: $sidebar-dark-menu-item-hover-color;\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n    // Enlarge menu\r\n    &.vertical-collpsed {\r\n        min-height: 1760px;\r\n        // Side menu\r\n        .vertical-menu {\r\n\r\n            // Sidebar Menu\r\n            #sidebar-menu {\r\n\r\n                > ul {\r\n                    > li {\r\n                        \r\n                        &:hover {\r\n                            > a {\r\n                                background: lighten($sidebar-dark-bg, 2%);\r\n                                color: $sidebar-dark-menu-item-hover-color;\r\n                                i{\r\n                                    color: $sidebar-dark-menu-item-hover-color;\r\n                                }\r\n                            }\r\n\r\n                            >ul {\r\n                                a{\r\n                                    color: $sidebar-dark-menu-sub-item-color;\r\n                                    &:hover{\r\n                                        color: $sidebar-dark-menu-item-hover-color;\r\n                                    }\r\n                                }\r\n                            }\r\n                        }\r\n                    }\r\n\r\n                    ul{\r\n                        background-color: $sidebar-dark-bg;\r\n                    }\r\n                    \r\n                }\r\n\r\n                ul{\r\n                    \r\n                    li{\r\n                        &.mm-active .active{\r\n                            color: $sidebar-dark-menu-item-active-color !important;\r\n                            i{\r\n                                color: $sidebar-dark-menu-item-active-color !important;\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n\r\n            }\r\n\r\n\r\n        }\r\n    }\r\n    .mm-active {\r\n        color: $sidebar-dark-menu-item-active-color !important;\r\n        > a{\r\n            color: $sidebar-dark-menu-item-active-color !important;\r\n            i {\r\n                color: $sidebar-dark-menu-item-active-color !important;\r\n            }\r\n        }\r\n        > i {\r\n            color: $sidebar-dark-menu-item-active-color !important;\r\n        }\r\n        .active {\r\n            color: $sidebar-dark-menu-item-active-color !important;\r\n\r\n            i {\r\n                color: $sidebar-dark-menu-item-active-color !important;\r\n            }\r\n        }\r\n    }\r\n\r\n    .menu-title {\r\n        color: $sidebar-dark-menu-item-icon-color;\r\n    }\r\n}\r\n\r\n\r\nbody[data-layout=\"horizontal\"] {\r\n    .main-content {\r\n        margin-left: 0 !important;\r\n    }\r\n}\r\n\r\n// Compact Sidebar\r\n\r\nbody[data-sidebar-size=\"small\"] {\r\n    .navbar-brand-box{\r\n        width: $sidebar-width-sm;\r\n    }\r\n    .vertical-menu{\r\n        width: $sidebar-width-sm;\r\n        text-align: center;\r\n\r\n        .has-arrow:after,\r\n        .badge {\r\n            display: none !important;\r\n        }\r\n    }\r\n    .main-content {\r\n        margin-left: $sidebar-width-sm;\r\n    }\r\n    .footer {\r\n        left: $sidebar-width-sm;\r\n        @media (max-width: 991.98px) {\r\n            left: 0;\r\n        }\r\n    }\r\n\r\n    #sidebar-menu {\r\n        ul li {\r\n            &.menu-title{\r\n                background-color: lighten($sidebar-dark-bg, 2%);\r\n            }\r\n            a{\r\n                i{\r\n                    display: block;\r\n                }\r\n            }\r\n            ul.sub-menu {\r\n                li {\r\n                    a{\r\n                        padding-left: 1.5rem;\r\n                    }\r\n\r\n                    ul.sub-menu li a {\r\n                        padding-left: 1.5rem;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n    &.vertical-collpsed {\r\n        .main-content {\r\n            margin-left: $sidebar-collapsed-width;\r\n        }\r\n        .vertical-menu {\r\n            #sidebar-menu{\r\n                text-align: left;\r\n                >ul{\r\n                    >li{\r\n                        >a {\r\n                            i{\r\n                                display: inline-block;\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n        .footer {\r\n            left: $sidebar-collapsed-width;\r\n        }\r\n    }\r\n}\r\n\r\n// colored sidebar\r\n\r\nbody[data-sidebar=\"colored\"] {\r\n    .vertical-menu{\r\n        background-color: $primary;\r\n    }\r\n    .navbar-brand-box{\r\n        background-color: $primary;\r\n        .logo-dark{\r\n            display: none;\r\n        }\r\n        .logo-light{\r\n            display: block;\r\n        }\r\n    }\r\n\r\n    .mm-active {\r\n        color: $white !important;\r\n        > a{\r\n            color: $white !important;\r\n            i {\r\n                color: $white !important;\r\n            }\r\n        }\r\n        > i, .active {\r\n            color: $white !important;\r\n        }\r\n    }\r\n\r\n    #sidebar-menu {\r\n        ul {\r\n            li {\r\n                &.menu-title{\r\n                    color: rgba($white, 0.6);\r\n                }\r\n\r\n                a{\r\n                    color: rgba($white, 0.6);\r\n                    i{\r\n                        color: rgba($white, 0.6);\r\n                    }\r\n                    &.waves-effect {\r\n                        .waves-ripple {\r\n                          background: rgba($white, 0.1);\r\n                        }\r\n                    }\r\n\r\n                    &:hover {\r\n                        color: $white;\r\n    \r\n                        i {\r\n                            color: $white;\r\n                        }\r\n                    }\r\n                }\r\n\r\n                ul.sub-menu {\r\n                    li {\r\n                        a{\r\n                            color: rgba($white,.5);\r\n                            &:hover {\r\n                                color: $white;\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    &.vertical-collpsed {\r\n        .vertical-menu {\r\n            #sidebar-menu{\r\n                >ul{\r\n                    >li{\r\n                        &:hover>a{\r\n                            background-color: lighten($primary, 2%);\r\n                            color: $white;\r\n                            i{\r\n                                color: $white;\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n\r\n                ul{\r\n                    li{\r\n                        &.mm-active {\r\n                            .active{\r\n                                color: $sidebar-menu-item-active-color !important;\r\n                            }\r\n                        }\r\n\r\n                        ul.sub-menu {\r\n                            li {\r\n                                a{\r\n                                    &:hover {\r\n                                        color: $sidebar-menu-item-active-color;\r\n                                    }\r\n                                }\r\n                                &.mm-active {\r\n                                    color: $sidebar-menu-item-active-color !important;\r\n                                    > a{\r\n                                        color: $sidebar-menu-item-active-color !important;\r\n                                        i {\r\n                                            color: $sidebar-menu-item-active-color !important;\r\n                                        }\r\n                                    }\r\n                                }\r\n                            }\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}", "// \r\n// _horizontal.scss\r\n// \r\n\r\n.topnav {\r\n    background: var(--#{$prefix}topnav-bg);\r\n    padding: 0 calc(#{$grid-gutter-width} * 0.5);\r\n    box-shadow: $box-shadow;\r\n    margin-top: $header-height;\r\n    position: fixed;\r\n    left: 0;\r\n    right: 0;\r\n    z-index: 100;\r\n    \r\n    .topnav-menu {\r\n        margin: 0;\r\n        padding: 0;\r\n    }\r\n\r\n    .navbar-nav {\r\n        \r\n        .nav-link {\r\n            font-size: 14px;\r\n            position: relative;\r\n            padding: 1rem 1.3rem;\r\n            color: var(--#{$prefix}menu-item-color);\r\n            i{\r\n                font-size: 15px;\r\n            }\r\n            &:focus, &:hover{\r\n                color: var(--#{$prefix}menu-item-active-color);\r\n                background-color: transparent;\r\n            }\r\n        }\r\n        \r\n        .dropdown-item{\r\n            color: var(--#{$prefix}menu-item-color);\r\n            &.active, &:hover{\r\n                color: var(--#{$prefix}menu-item-active-color);\r\n            }\r\n        }\r\n        \r\n        .nav-item{\r\n            .nav-link.active{\r\n                color: var(--#{$prefix}menu-item-active-color);\r\n            }\r\n        }\r\n\r\n        .dropdown{\r\n            &.active{\r\n              >a {\r\n                    color: var(--#{$prefix}menu-item-active-color);\r\n                    background-color: transparent;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n@include media-breakpoint-up(xl) {\r\n\r\n    body[data-layout=\"horizontal\"] {\r\n        .container-fluid,\r\n        .navbar-header {\r\n            max-width: 85%;\r\n        }\r\n    }\r\n}\r\n\r\n@include media-breakpoint-up(lg) {\r\n    .topnav {\r\n        .navbar-nav {\r\n            .nav-item {\r\n                &:first-of-type {\r\n                    .nav-link {\r\n                        padding-left: 0;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        .dropdown-item {\r\n            padding: .5rem 1.5rem;\r\n            min-width: 180px;\r\n        }\r\n\r\n        .dropdown {\r\n            &.mega-dropdown{\r\n                // position: static;\r\n                .mega-dropdown-menu{\r\n                    left: 0px;\r\n                    right: auto;\r\n                }\r\n            }\r\n            .dropdown-menu {\r\n                margin-top: 0;\r\n                border-radius: 0 0 $dropdown-border-radius $dropdown-border-radius;\r\n\r\n                .arrow-down {\r\n                    &::after {\r\n                        right: 15px;\r\n                        transform: rotate(-135deg) translateY(-50%);\r\n                        position: absolute;\r\n                    }\r\n                }\r\n\r\n                .dropdown {\r\n                    .dropdown-menu {\r\n                        position: absolute;\r\n                        top: 0 !important;\r\n                        left: 100%;\r\n                        display: none\r\n                    }\r\n                }\r\n            }\r\n\r\n            &:hover {\r\n                >.dropdown-menu {\r\n                    display: block;\r\n                }\r\n            }\r\n        }\r\n\r\n        .dropdown:hover>.dropdown-menu>.dropdown:hover>.dropdown-menu {\r\n            display: block\r\n        }\r\n    }\r\n\r\n    .navbar-toggle {\r\n        display: none;\r\n    }\r\n}\r\n\r\n.arrow-down {\r\n    display: inline-block;\r\n\r\n    &:after {\r\n        border-color: initial;\r\n        border-style: solid;\r\n        border-width: 0 0 1px 1px;\r\n        content: \"\";\r\n        height: .4em;\r\n        display: inline-block;\r\n        right: 5px;\r\n        top: 50%;\r\n        margin-left: 10px;\r\n        transform: rotate(-45deg) translateY(-50%);\r\n        transform-origin: top;\r\n        transition: all .3s ease-out;\r\n        width: .4em;\r\n    }\r\n}\r\n\r\n\r\n@include media-breakpoint-down(xl) {\r\n    .topnav-menu {\r\n        .navbar-nav {\r\n            li {\r\n                &:last-of-type {\r\n                    .dropdown {\r\n                        .dropdown-menu {\r\n                            right: 100%;\r\n                            left: auto;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n@include media-breakpoint-down(lg) {\r\n\r\n    .navbar-brand-box{\r\n        .logo-dark {\r\n            display: var(--#{$prefix}display-block);\r\n            span.logo-sm{\r\n                display: var(--#{$prefix}display-block);\r\n            }\r\n        }\r\n    \r\n        .logo-light {\r\n            display: var(--#{$prefix}display-none);\r\n        }\r\n    }\r\n    \r\n    .topnav {\r\n        max-height: 360px;\r\n        overflow-y: auto;\r\n        padding: 0;\r\n        .navbar-nav {\r\n            .nav-link {\r\n                padding: 0.75rem 1.1rem;\r\n            }\r\n        }\r\n\r\n        .dropdown {\r\n            .dropdown-menu {\r\n                background-color: transparent;\r\n                border: none;\r\n                box-shadow: none;\r\n                padding-left: 15px;\r\n                &.dropdown-mega-menu-xl{\r\n                    width: auto;\r\n    \r\n                    .row{\r\n                        margin: 0px;\r\n                    }\r\n                }\r\n            }\r\n\r\n            .dropdown-item {\r\n                position: relative;\r\n                background-color: transparent;\r\n\r\n                &.active,\r\n                &:active {\r\n                    color: $primary;\r\n                }\r\n            }\r\n        }\r\n\r\n        .arrow-down {\r\n            &::after {\r\n                right: 15px;\r\n                position: absolute;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\n@include media-breakpoint-up(lg) {\r\n\r\n    body[data-layout=\"horizontal\"][data-topbar=\"light\"] {\r\n        .navbar-brand-box{\r\n            .logo-dark {\r\n                display: var(--#{$prefix}display-block);\r\n            }\r\n        \r\n            .logo-light {\r\n                display: var(--#{$prefix}display-none);\r\n            }\r\n        }\r\n\r\n        .topnav{\r\n            background-color: $primary;\r\n            .navbar-nav {\r\n        \r\n                .nav-link {\r\n                    color: rgba($white, 0.6);\r\n                    \r\n                    &:focus, &:hover{\r\n                        color: rgba($white, 0.9);\r\n                    }\r\n                }\r\n        \r\n                > .dropdown{\r\n                    &.active{\r\n                    >a {\r\n                            color: rgba($white, 0.9) !important;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n// Colored Topbar \r\n\r\nbody[data-layout=\"horizontal\"][data-topbar=\"colored\"] {\r\n    #page-topbar{\r\n        background-color: $primary;\r\n        box-shadow: none;\r\n    }\r\n\r\n    .logo-dark {\r\n        display: none;\r\n    }\r\n\r\n    .logo-light {\r\n        display: block;\r\n    }\r\n\r\n    .app-search {\r\n    \r\n        .form-control {\r\n            background-color: rgba(var(--#{$prefix}topbar-search-bg),0.07);\r\n            color: $white;\r\n        }\r\n        span,\r\n        input.form-control::-webkit-input-placeholder {\r\n            color: rgba($white,0.5);\r\n        }\r\n    }\r\n    .header-item {\r\n        color: var(--#{$prefix}header-dark-item-color);\r\n    \r\n        &:hover {\r\n            color: var(--#{$prefix}header-dark-item-color);\r\n        }\r\n    }\r\n\r\n    .navbar-header {\r\n        .dropdown .show {\r\n            &.header-item{\r\n                background-color: rgba($white,0.1);\r\n            }\r\n        }\r\n\r\n        .waves-effect .waves-ripple {\r\n            background: rgba($white, 0.4);\r\n        }\r\n    }\r\n\r\n    .noti-icon {\r\n        i {\r\n            color: var(--#{$prefix}header-dark-item-color);\r\n        }\r\n    }\r\n\r\n    @include media-breakpoint-up(lg) {\r\n        .topnav{\r\n            background-color: $primary;\r\n            .navbar-nav {\r\n        \r\n                .nav-link {\r\n                    color: rgba($white, 0.6);\r\n                    \r\n                    &:focus, &:hover{\r\n                        color: rgba($white, 0.9);\r\n                    }\r\n                }\r\n        \r\n                > .dropdown{\r\n                    &.active{\r\n                      >a {\r\n                            color: rgba($white, 0.9) !important;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n", "// \r\n// _layouts.scss\r\n// \r\n\r\nbody[data-layout-size=\"boxed\"] {\r\n    background-color: var(--#{$prefix}boxed-body-bg);\r\n    #layout-wrapper {\r\n        background-color: var(--#{$prefix}body-bg);\r\n        max-width: $boxed-layout-width;\r\n        margin: 0 auto;\r\n        box-shadow: $box-shadow;\r\n    }\r\n\r\n    #page-topbar {\r\n        max-width: $boxed-layout-width;\r\n        margin: 0 auto;\r\n    }\r\n\r\n    .footer {\r\n        margin: 0 auto;\r\n        max-width: calc(#{$boxed-layout-width} - #{$sidebar-width});\r\n    }\r\n\r\n    &.vertical-collpsed {\r\n        .footer {\r\n            max-width: calc(#{$boxed-layout-width} - #{$sidebar-collapsed-width});\r\n        }\r\n    }\r\n}\r\n\r\n\r\n// Horizontal Boxed Layout\r\n\r\nbody[data-layout=\"horizontal\"][data-layout-size=\"boxed\"]{\r\n    #page-topbar, #layout-wrapper, .footer {\r\n        max-width: 100%;\r\n    }\r\n    .container-fluid, .navbar-header {\r\n        max-width: $boxed-layout-width;\r\n    }\r\n}\r\n\r\n// Scrollable layout\r\n\r\nbody[data-layout-scrollable=\"true\"] {\r\n    @media (min-width: 992px) {\r\n        #page-topbar, .vertical-menu{\r\n            position: absolute;\r\n        }\r\n    }\r\n\r\n    &[data-layout=\"horizontal\"]{\r\n        @media (min-width: 992px) {\r\n            #page-topbar, .topnav{\r\n                position: absolute;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n", "\r\n/*!\r\n * Waves v0.7.6\r\n * http://fian.my.id/Waves \r\n * \r\n * Copyright 2014-2018 <PERSON><PERSON><PERSON> Si<PERSON> and other contributors \r\n * Released under the MIT license \r\n * https://github.com/fians/Waves/blob/master/LICENSE */\r\n .waves-effect {\r\n    position: relative;\r\n    cursor: pointer;\r\n    display: inline-block;\r\n    overflow: hidden;\r\n    -webkit-user-select: none;\r\n    -moz-user-select: none;\r\n    -ms-user-select: none;\r\n    user-select: none;\r\n    -webkit-tap-highlight-color: transparent;\r\n  }\r\n  .waves-effect .waves-ripple {\r\n    position: absolute;\r\n    border-radius: 50%;\r\n    width: 100px;\r\n    height: 100px;\r\n    margin-top: -50px;\r\n    margin-left: -50px;\r\n    opacity: 0;\r\n    background: rgba(0, 0, 0, 0.2);\r\n    background: -webkit-radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n    background: -o-radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n    background: -moz-radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n    background: radial-gradient(rgba(0, 0, 0, 0.2) 0, rgba(0, 0, 0, 0.3) 40%, rgba(0, 0, 0, 0.4) 50%, rgba(0, 0, 0, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n    -webkit-transition: all 0.5s ease-out;\r\n    -moz-transition: all 0.5s ease-out;\r\n    -o-transition: all 0.5s ease-out;\r\n    transition: all 0.5s ease-out;\r\n    -webkit-transition-property: -webkit-transform, opacity;\r\n    -moz-transition-property: -moz-transform, opacity;\r\n    -o-transition-property: -o-transform, opacity;\r\n    transition-property: transform, opacity;\r\n    -webkit-transform: scale(0) translate(0, 0);\r\n    -moz-transform: scale(0) translate(0, 0);\r\n    -ms-transform: scale(0) translate(0, 0);\r\n    -o-transform: scale(0) translate(0, 0);\r\n    transform: scale(0) translate(0, 0);\r\n    pointer-events: none;\r\n  }\r\n  .waves-effect.waves-light .waves-ripple {\r\n    background: rgba(255, 255, 255, 0.4);\r\n    background: -webkit-radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n    background: -o-radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n    background: -moz-radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n    background: radial-gradient(rgba(255, 255, 255, 0.2) 0, rgba(255, 255, 255, 0.3) 40%, rgba(255, 255, 255, 0.4) 50%, rgba(255, 255, 255, 0.5) 60%, rgba(255, 255, 255, 0) 70%);\r\n  }\r\n  .waves-effect.waves-classic .waves-ripple {\r\n    background: rgba(0, 0, 0, 0.2);\r\n  }\r\n  .waves-effect.waves-classic.waves-light .waves-ripple {\r\n    background: rgba(255, 255, 255, 0.4);\r\n  }\r\n  .waves-notransition {\r\n    -webkit-transition: none !important;\r\n    -moz-transition: none !important;\r\n    -o-transition: none !important;\r\n    transition: none !important;\r\n  }\r\n  .waves-button,\r\n  .waves-circle {\r\n    -webkit-transform: translateZ(0);\r\n    -moz-transform: translateZ(0);\r\n    -ms-transform: translateZ(0);\r\n    -o-transform: translateZ(0);\r\n    transform: translateZ(0);\r\n    -webkit-mask-image: -webkit-radial-gradient(circle, white 100%, black 100%);\r\n  }\r\n  .waves-button,\r\n  .waves-button:hover,\r\n  .waves-button:visited,\r\n  .waves-button-input {\r\n    white-space: nowrap;\r\n    vertical-align: middle;\r\n    cursor: pointer;\r\n    border: none;\r\n    outline: none;\r\n    color: inherit;\r\n    background-color: rgba(0, 0, 0, 0);\r\n    font-size: 1em;\r\n    line-height: 1em;\r\n    text-align: center;\r\n    text-decoration: none;\r\n    z-index: 1;\r\n  }\r\n  .waves-button {\r\n    padding: 0.85em 1.1em;\r\n    border-radius: 0.2em;\r\n  }\r\n  .waves-button-input {\r\n    margin: 0;\r\n    padding: 0.85em 1.1em;\r\n  }\r\n  .waves-input-wrapper {\r\n    border-radius: 0.2em;\r\n    vertical-align: bottom;\r\n  }\r\n  .waves-input-wrapper.waves-button {\r\n    padding: 0;\r\n  }\r\n  .waves-input-wrapper .waves-button-input {\r\n    position: relative;\r\n    top: 0;\r\n    left: 0;\r\n    z-index: 1;\r\n  }\r\n  .waves-circle {\r\n    text-align: center;\r\n    width: 2.5em;\r\n    height: 2.5em;\r\n    line-height: 2.5em;\r\n    border-radius: 50%;\r\n  }\r\n  .waves-float {\r\n    -webkit-mask-image: none;\r\n    -webkit-box-shadow: 0px 1px 1.5px 1px rgba(0, 0, 0, 0.12);\r\n    box-shadow: 0px 1px 1.5px 1px rgba(0, 0, 0, 0.12);\r\n    -webkit-transition: all 300ms;\r\n    -moz-transition: all 300ms;\r\n    -o-transition: all 300ms;\r\n    transition: all 300ms;\r\n  }\r\n  .waves-float:active {\r\n    -webkit-box-shadow: 0px 8px 20px 1px rgba(0, 0, 0, 0.3);\r\n    box-shadow: 0px 8px 20px 1px rgba(0, 0, 0, 0.3);\r\n  }\r\n  .waves-block {\r\n    display: block;\r\n  }\r\n\r\n.waves-effect.waves-light {\r\n    .waves-ripple {\r\n        background-color: rgba($white, 0.4);\r\n    }\r\n}\r\n\r\n.waves-effect.waves-primary {\r\n    .waves-ripple {\r\n        background-color: rgba($primary, 0.4);\r\n    }\r\n}\r\n.waves-effect.waves-success {\r\n    .waves-ripple {\r\n        background-color: rgba($success, 0.4);\r\n    }\r\n}\r\n.waves-effect.waves-info {\r\n    .waves-ripple {\r\n        background-color: rgba($info, 0.4);\r\n    }\r\n}\r\n.waves-effect.waves-warning {\r\n    .waves-ripple {\r\n        background-color: rgba($warning, 0.4);\r\n    }\r\n}\r\n.waves-effect.waves-danger {\r\n    .waves-ripple {\r\n        background-color: rgba($danger, 0.4);\r\n    }\r\n}", "//\r\n// avatar.scss\r\n//\r\n\r\n.avatar-xs {\r\n  height: 2rem;\r\n  width: 2rem;\r\n}\r\n\r\n.avatar-sm {\r\n  height: 3rem;\r\n  width: 3rem;\r\n}\r\n\r\n.avatar-md {\r\n  height: 4.5rem;\r\n  width: 4.5rem;\r\n}\r\n\r\n.avatar-lg {\r\n  height: 6rem;\r\n  width: 6rem;\r\n}\r\n\r\n.avatar-xl {\r\n  height: 7.5rem;\r\n  width: 7.5rem;\r\n}\r\n\r\n.avatar-title {\r\n  align-items: center;\r\n  background-color: $primary;\r\n  color: $white;\r\n  display: flex;\r\n  font-weight: $font-weight-medium;\r\n  height: 100%;\r\n  justify-content: center;\r\n  width: 100%;\r\n}\r\n\r\n// avatar group\r\n.avatar-group {\r\n  padding-left: 12px;\r\n  display: flex;\r\n  flex-wrap: wrap;\r\n  .avatar-group-item {\r\n    margin-left: -12px;\r\n    border: 2px solid var(--#{$prefix}secondary-bg);\r\n    border-radius: 50%;\r\n    transition: all 0.2s;\r\n    &:hover{\r\n      position: relative;\r\n      transform: translateY(-2px);\r\n    }\r\n  }\r\n}", "\r\n//\r\n// accordion.scss\r\n//\r\n\r\n.custom-accordion {\r\n    .accordion-list {\r\n        display: flex;\r\n        border-radius: 7px;\r\n        background-color: var(--#{$prefix}tertiary-bg);\r\n        padding: 12px 20px;\r\n        color: var(--#{$prefix}body-color);\r\n        font-weight: $font-weight-semibold;\r\n        align-items: center;\r\n        justify-content: space-between;\r\n        &.collapsed {\r\n            i.accor-plus-icon {\r\n                &:before {\r\n                    content: \"\\F0415\";\r\n                }\r\n            }\r\n        }\r\n\r\n        .accor-plus-icon{\r\n            display: inline-block;\r\n            font-size: 16px;\r\n            height: 24px;\r\n            width: 24px;\r\n            line-height: 22px;\r\n            background-color: var(--#{$prefix}secondary-bg);\r\n            text-align: center;\r\n            border-radius: 50%;\r\n        }\r\n    }\r\n\r\n    \r\n    a {\r\n\r\n        &.collapsed {\r\n            i.accor-down-icon {\r\n                &:before {\r\n                    content: \"\\F0140\";\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n\r\n    .card-body {\r\n        color: var(--#{prefix}secondary-color);\r\n    }\r\n}", "//\r\n// _helper.scss\r\n//\r\n\r\n.font-size-10 {\r\n    font-size: 10px !important;\r\n}\r\n\r\n.font-size-11 {\r\n    font-size: 11px !important;\r\n}\r\n\r\n.font-size-12 {\r\n    font-size: 12px !important;\r\n}\r\n\r\n.font-size-13 {\r\n    font-size: 13px !important;\r\n}\r\n\r\n.font-size-14 {\r\n    font-size: 14px !important;\r\n}\r\n\r\n.font-size-15 {\r\n    font-size: 15px !important;\r\n}\r\n\r\n.font-size-16 {\r\n    font-size: 16px !important;\r\n}\r\n\r\n.font-size-17 {\r\n    font-size: 17px !important;\r\n}\r\n\r\n.font-size-18 {\r\n    font-size: 18px !important;\r\n}\r\n\r\n.font-size-20 {\r\n    font-size: 20px !important;\r\n}\r\n\r\n.font-size-22 {\r\n    font-size: 22px !important;\r\n}\r\n\r\n.font-size-24 {\r\n    font-size: 24px !important;\r\n}\r\n\r\n// Social\r\n.social-list-item {\r\n    height: 2rem;\r\n    width: 2rem;\r\n    line-height: calc(2rem - 4px);\r\n    display: block;\r\n    border: 2px solid $gray-500;\r\n    border-radius: 50%;\r\n    color: $gray-500;\r\n    text-align: center;\r\n    transition: all 0.4s;\r\n\r\n    &:hover {\r\n        color: var(--#{$prefix}secondary-color);\r\n        background-color: $gray-200;\r\n    }\r\n}\r\n\r\n\r\n.w-xs {\r\n    min-width: 80px;\r\n}\r\n\r\n.w-sm {\r\n    min-width: 95px;\r\n}\r\n\r\n.w-md {\r\n    min-width: 110px;\r\n}\r\n\r\n.w-lg {\r\n    min-width: 140px;\r\n}\r\n\r\n.w-xl {\r\n    min-width: 160px;\r\n}\r\n\r\n\r\n// alert\r\n\r\n.alert-dismissible {\r\n    .btn-close {\r\n        font-size: 10px;\r\n        padding: $alert-padding-y * 1.4 $alert-padding-x;\r\n    }\r\n}\r\n\r\n.chartjs-chart {\r\n    max-height: 300px;\r\n}", "// \r\n// preloader.scss\r\n//\r\n\r\n#preloader {\r\n    position: fixed;\r\n    top: 0;\r\n    left: 0;\r\n    right: 0;\r\n    bottom: 0;\r\n    background-color: var(--#{$prefix}secondary-bg);\r\n    z-index: 9999;\r\n}\r\n\r\n#status {\r\n    width: 40px;\r\n    height: 40px;\r\n    position: absolute;\r\n    left: 50%;\r\n    top: 50%;\r\n    margin: -20px 0 0 -20px;\r\n}\r\n\r\n.spinner-chase {\r\n    margin: 0 auto;\r\n    width: 40px;\r\n    height: 40px;\r\n    position: relative;\r\n    animation: spinner-chase 2.5s infinite linear both;\r\n}\r\n\r\n.chase-dot {\r\n    width: 100%;\r\n    height: 100%;\r\n    position: absolute;\r\n    left: 0;\r\n    top: 0; \r\n    animation: chase-dot 2.0s infinite ease-in-out both; \r\n    &:before {\r\n        content: '';\r\n        display: block;\r\n        width: 25%;\r\n        height: 25%;\r\n        background-color: $primary;\r\n        border-radius: 100%;\r\n        animation: chase-dot-before 2.0s infinite ease-in-out both; \r\n    }\r\n\r\n    &:nth-child(1) { \r\n        animation-delay: -1.1s; \r\n        &:before{\r\n            animation-delay: -1.1s;\r\n        }\r\n    }\r\n    &:nth-child(2) { \r\n        animation-delay: -1.0s;\r\n        &:before{\r\n            animation-delay: -1.0s;\r\n        }\r\n    }\r\n    &:nth-child(3) { \r\n        animation-delay: -0.9s;\r\n        &:before{\r\n            animation-delay: -0.9s;\r\n        } \r\n    }\r\n    &:nth-child(4) { \r\n        animation-delay: -0.8s; \r\n        &:before{\r\n            animation-delay: -0.8s;\r\n        } \r\n    }\r\n    &:nth-child(5) { \r\n        animation-delay: -0.7s; \r\n        &:before{\r\n            animation-delay: -0.7s;\r\n        } \r\n    }\r\n    &:nth-child(6) { \r\n        animation-delay: -0.6s; \r\n        &:before{\r\n            animation-delay: -0.6s;\r\n        }\r\n    }\r\n}\r\n\r\n@keyframes spinner-chase {\r\n    100% { \r\n        transform: rotate(360deg); \r\n    } \r\n}\r\n\r\n@keyframes chase-dot {\r\n    80%, 100% { \r\n        transform: rotate(360deg); \r\n    } \r\n}\r\n\r\n@keyframes chase-dot-before {\r\n    50% {\r\n        transform: scale(0.4); \r\n    } \r\n    100%, 0% {\r\n        transform: scale(1.0); \r\n    } \r\n}", "//\r\n// Forms.scss\r\n//\r\n\r\n[type=\"tel\"],\r\n[type=\"url\"],\r\n[type=\"email\"],\r\n[type=\"number\"] {\r\n  &::placeholder {\r\n    text-align: left\r\n      /*rtl: right*/\r\n    ;\r\n  }\r\n}\r\n\r\n.form-check {\r\n  position: relative;\r\n  text-align: left\r\n    /*rtl: right*/\r\n  ;\r\n}\r\n\r\n\r\n// checkbox input right\r\n\r\n.form-check-right {\r\n  padding-left: 0;\r\n  display: inline-block;\r\n  padding-right: $form-check-padding-start;\r\n\r\n  .form-check-input {\r\n    float: right;\r\n    margin-left: 0;\r\n    margin-right: $form-check-padding-start * -1;\r\n  }\r\n\r\n  .form-check-label {\r\n    display: block;\r\n  }\r\n}\r\n\r\n\r\n\r\n// checkbox\r\n\r\n.form-checkbox-outline {\r\n\r\n  .form-check-input {\r\n    border-width: 2px;\r\n    background-color: var(--#{$prefix}secondary-bg);\r\n\r\n    &:active {\r\n      filter: none;\r\n    }\r\n\r\n    &:checked {\r\n      background-color: var(--#{$prefix}secondary-bg)  !important;\r\n\r\n      &[type=checkbox] {\r\n        background-image: none;\r\n\r\n      }\r\n\r\n      &:after {\r\n        position: absolute;\r\n        content: '\\F012C';\r\n        font-family: \"Material Design Icons\";\r\n        top: -4px !important;\r\n        left: 1px;\r\n        /*rtl: -4px */\r\n        font-size: 16px;\r\n        color: var(--#{$prefix}body-color);\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n// radio\r\n\r\n.form-radio-outline {\r\n  .form-check-input {\r\n    background-color: var(--#{$prefix}secondary-bg);\r\n    position: relative;\r\n\r\n    &:active {\r\n      filter: none;\r\n    }\r\n\r\n    &:checked {\r\n      background-color: var(--#{$prefix}secondary-bg)  !important;\r\n\r\n      &[type=checkbox] {\r\n        background-image: none;\r\n\r\n      }\r\n\r\n      &:after {\r\n        position: absolute;\r\n        content: '';\r\n        top: 3px !important;\r\n        left: 3px;\r\n        width: 5px;\r\n        height: 5px;\r\n        border-radius: 50%;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n\r\n\r\n// checkbox color\r\n\r\n@each $color,\r\n$value in $theme-colors {\r\n  .form-check-#{$color} {\r\n    .form-check-input {\r\n      &:checked {\r\n        background-color: $value;\r\n        border-color: $value;\r\n      }\r\n    }\r\n  }\r\n\r\n  .form-radio-#{$color} {\r\n    .form-check-input {\r\n      &:checked {\r\n        border-color: $value;\r\n        background-color: $value;\r\n\r\n        &:after {\r\n          background-color: $value;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.form-check,\r\n.form-check-input,\r\n.form-check-label {\r\n  cursor: pointer;\r\n  margin-bottom: 0;\r\n}\r\n\r\n// Switch sizes\r\n\r\n.form-switch-md {\r\n  padding-left: 2.5rem;\r\n  min-height: 24px;\r\n  line-height: 24px;\r\n\r\n  .form-check-input {\r\n    width: 40px;\r\n    height: 20px;\r\n    left: -0.5rem;\r\n    position: relative;\r\n  }\r\n\r\n  .form-check-label {\r\n    vertical-align: middle;\r\n  }\r\n}\r\n\r\n\r\n.form-switch-lg {\r\n  padding-left: 2.75rem;\r\n  min-height: 28px;\r\n  line-height: 28px;\r\n\r\n  .form-check-input {\r\n    width: 48px;\r\n    height: 24px;\r\n    left: -0.75rem;\r\n    position: relative;\r\n  }\r\n}\r\n\r\n.input-group-text {\r\n  margin-bottom: 0px;\r\n}", "// \r\n// Widgets.scss\r\n// \r\n\r\n.mini-stats-wid{\r\n    .mini-stat-icon{\r\n        overflow: hidden;\r\n        position: relative;\r\n        &:before, &:after{\r\n            content: \"\";\r\n            position: absolute;\r\n            width: 8px;\r\n            height: 54px;\r\n            background-color: rgba($white,.1);\r\n            left: 16px;\r\n            transform: rotate(32deg);\r\n            top: -5px;\r\n            transition: all 0.4s;\r\n        }\r\n\r\n        &::after{\r\n            left: -12px;\r\n            width: 12px;\r\n            transition: all 0.2s;\r\n        }\r\n    }\r\n\r\n    &:hover{\r\n        .mini-stat-icon{\r\n            &::after{\r\n                left: 60px;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\n", "// \r\n// _demos.scss\r\n// \r\n\r\n// Lightbox \r\n\r\n.mfp-popup-form {\r\n    max-width: 1140px;\r\n}\r\n.mfp-close{\r\n  color: var(--#{$prefix}body-color) !important;\r\n}\r\n\r\n// Modals\r\n\r\n.bs-example-modal {\r\n    position: relative;\r\n    top: auto;\r\n    right: auto;\r\n    bottom: auto;\r\n    left: auto;\r\n    z-index: 1;\r\n    display: block;\r\n  }\r\n\r\n[dir=\"rtl\"]{\r\n  .modal-open{\r\n    padding-left: 0px !important;\r\n  }\r\n}\r\n\r\n// Icon demo ( Demo only )\r\n.icon-demo-content {\r\n    text-align: center;\r\n    color: $gray-500;\r\n  \r\n    i{\r\n      display: block;\r\n      font-size: 24px;\r\n      margin-bottom: 16px;\r\n      color: var(--#{prefix}secondary-color);\r\n      transition: all 0.4s;\r\n    }\r\n  \r\n    .col-lg-4 {\r\n      margin-top: 24px;\r\n  \r\n      &:hover {\r\n        i {\r\n          color: $primary;\r\n          transform: scale(1.5);\r\n        }\r\n      }\r\n    }\r\n}\r\n\r\n\r\n// Grid\r\n\r\n.grid-structure {\r\n    .grid-container {\r\n        background-color: var(--#{$prefix}gray-100);\r\n        margin-top: 10px;\r\n        font-size: .8rem;\r\n        font-weight: $font-weight-medium;\r\n        padding: 10px 20px;\r\n    }\r\n}\r\n\r\n\r\n// card radio\r\n\r\n.card-radio{\r\n  background-color: var(--#{$prefix}secondary-bg);\r\n  border: 2px solid var(--#{$prefix}border-color);\r\n  border-radius: var(--#{$prefix}border-radius);\r\n  padding: 1rem;\r\n  overflow: hidden;\r\n  text-overflow: ellipsis;\r\n  white-space: nowrap;\r\n\r\n  &:hover{\r\n    cursor: pointer;\r\n  }\r\n}\r\n\r\n.card-radio-label{\r\n  display: block;\r\n}\r\n\r\n\r\n.card-radio-input{\r\n  display: none;\r\n  &:checked + .card-radio {\r\n    border-color: $primary !important;\r\n  }\r\n}\r\n\r\n.navs-carousel{\r\n  .owl-nav{\r\n      margin-top: 16px;\r\n      button{\r\n          width: 30px;\r\n          height: 30px;\r\n          line-height: 28px !important;\r\n          font-size: 20px !important;\r\n          border-radius: 50% !important;\r\n          background-color: rgba($primary, 0.25) !important;\r\n          color: $primary !important;\r\n          margin: 4px 8px !important;\r\n      }\r\n  }\r\n}\r\n\r\n\r\n", "// \r\n// print.scss\r\n//\r\n\r\n// Used invoice page\r\n@media print {\r\n    .vertical-menu,\r\n    .right-bar,\r\n    .page-title-box,\r\n    .navbar-header,\r\n    .footer {\r\n        display: none !important;\r\n    }\r\n    .card-body,\r\n    .main-content,\r\n    .right-bar,\r\n    .page-content,\r\n    body {\r\n        padding: 0;\r\n        margin: 0;\r\n    }\r\n\r\n    .card{\r\n        border: 0;\r\n    }\r\n}", "[data-simplebar] {\r\n  position: relative;\r\n  flex-direction: column;\r\n  flex-wrap: wrap;\r\n  justify-content: flex-start;\r\n  align-content: flex-start;\r\n  align-items: flex-start;\r\n}\r\n\r\n.simplebar-wrapper {\r\n  overflow: hidden;\r\n  width: inherit;\r\n  height: inherit;\r\n  max-width: inherit;\r\n  max-height: inherit;\r\n}\r\n\r\n.simplebar-mask {\r\n  direction: inherit;\r\n  position: absolute;\r\n  overflow: hidden;\r\n  padding: 0;\r\n  margin: 0;\r\n  left: 0;\r\n  top: 0;\r\n  bottom: 0;\r\n  right: 0;\r\n  width: auto !important;\r\n  height: auto !important;\r\n  z-index: 0;\r\n}\r\n\r\n.simplebar-offset {\r\n  direction: inherit !important;\r\n  box-sizing: inherit !important;\r\n  resize: none !important;\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0 !important;\r\n  bottom: 0;\r\n  right: 0 !important;\r\n  padding: 0;\r\n  margin: 0;\r\n  -webkit-overflow-scrolling: touch;\r\n}\r\n\r\n.simplebar-content-wrapper {\r\n  direction: inherit;\r\n  box-sizing: border-box !important;\r\n  position: relative;\r\n  display: block;\r\n  height: 100%; /* Required for horizontal native scrollbar to not appear if parent is taller than natural height */\r\n  width: auto;\r\n  visibility: visible;\r\n  overflow: auto; /* Scroll on this element otherwise element can't have a padding applied properly */\r\n  max-width: 100%; /* Not required for horizontal scroll to trigger */\r\n  max-height: 100%; /* Needed for vertical scroll to trigger */\r\n  scrollbar-width: none;\r\n  padding: 0px !important;\r\n}\r\n\r\n.simplebar-content-wrapper::-webkit-scrollbar,\r\n.simplebar-hide-scrollbar::-webkit-scrollbar {\r\n  display: none;\r\n}\r\n\r\n.simplebar-content:before,\r\n.simplebar-content:after {\r\n  content: ' ';\r\n  display: table;\r\n}\r\n\r\n.simplebar-placeholder {\r\n  max-height: 100%;\r\n  max-width: 100%;\r\n  width: 100%;\r\n  pointer-events: none;\r\n}\r\n\r\n.simplebar-height-auto-observer-wrapper {\r\n  box-sizing: inherit !important;\r\n  height: 100%;\r\n  width: 100%;\r\n  max-width: 1px;\r\n  position: relative;\r\n  float: left;\r\n  max-height: 1px;\r\n  overflow: hidden;\r\n  z-index: -1;\r\n  padding: 0;\r\n  margin: 0;\r\n  pointer-events: none;\r\n  flex-grow: inherit;\r\n  flex-shrink: 0;\r\n  flex-basis: 0;\r\n}\r\n\r\n.simplebar-height-auto-observer {\r\n  box-sizing: inherit;\r\n  display: block;\r\n  opacity: 0;\r\n  position: absolute;\r\n  top: 0;\r\n  left: 0;\r\n  height: 1000%;\r\n  width: 1000%;\r\n  min-height: 1px;\r\n  min-width: 1px;\r\n  overflow: hidden;\r\n  pointer-events: none;\r\n  z-index: -1;\r\n}\r\n\r\n.simplebar-track {\r\n  z-index: 1;\r\n  position: absolute;\r\n  right: 0;\r\n  bottom: 0;\r\n  pointer-events: none;\r\n  overflow: hidden;\r\n}\r\n\r\n[data-simplebar].simplebar-dragging .simplebar-content {\r\n  pointer-events: none;\r\n  user-select: none;\r\n  -webkit-user-select: none;\r\n}\r\n\r\n[data-simplebar].simplebar-dragging .simplebar-track {\r\n  pointer-events: all;\r\n}\r\n\r\n.simplebar-scrollbar {\r\n  position: absolute;\r\n  right: 2px;\r\n  width: 4px;\r\n  min-height: 10px;\r\n}\r\n\r\n.simplebar-scrollbar:before {\r\n  position: absolute;\r\n  content: '';\r\n  background: #a2adb7;\r\n  border-radius: 7px;\r\n  left: 0;\r\n  right: 0;\r\n  opacity: 0;\r\n  transition: opacity 0.2s linear;\r\n}\r\n\r\n.simplebar-scrollbar.simplebar-visible:before {\r\n  /* When hovered, remove all transitions from drag handle */\r\n  opacity: 0.5;\r\n  transition: opacity 0s linear;\r\n}\r\n\r\n.simplebar-track.simplebar-vertical {\r\n  top: 0;\r\n  width: 11px;\r\n}\r\n\r\n.simplebar-track.simplebar-vertical .simplebar-scrollbar:before {\r\n  top: 2px;\r\n  bottom: 2px;\r\n}\r\n\r\n.simplebar-track.simplebar-horizontal {\r\n  left: 0;\r\n  height: 11px;\r\n}\r\n\r\n.simplebar-track.simplebar-horizontal .simplebar-scrollbar:before {\r\n  height: 100%;\r\n  left: 2px;\r\n  right: 2px;\r\n}\r\n\r\n.simplebar-track.simplebar-horizontal .simplebar-scrollbar {\r\n  right: auto;\r\n  left: 0;\r\n  top: 2px;\r\n  height: 7px;\r\n  min-height: 0;\r\n  min-width: 10px;\r\n  width: auto;\r\n}\r\n\r\n/* Rtl support */\r\n[data-simplebar-direction='rtl'] .simplebar-track.simplebar-vertical {\r\n  right: auto;\r\n  left: 0;\r\n}\r\n\r\n.hs-dummy-scrollbar-size {\r\n  direction: rtl;\r\n  position: fixed;\r\n  opacity: 0;\r\n  visibility: hidden;\r\n  height: 500px;\r\n  width: 500px;\r\n  overflow-y: hidden;\r\n  overflow-x: scroll;\r\n}\r\n\r\n.simplebar-hide-scrollbar {\r\n  position: fixed;\r\n  left: 0;\r\n  visibility: hidden;\r\n  overflow-y: scroll;\r\n  scrollbar-width: none;\r\n}\r\n\r\n.custom-scroll {\r\n  height: 100%;\r\n}", "/* ==============\r\n  Calendar\r\n===================*/\r\n\r\n.lnb-calendars-item {\r\n    display: inline-block;\r\n    margin-right: 7px\r\n        /*rtl: margin-left: 7px*/\r\n    ;\r\n}\r\n\r\ninput[type=\"checkbox\"].tui-full-calendar-checkbox-round+span {\r\n    margin-right: 4px\r\n        /*rtl: 0*/\r\n    ;\r\n    margin-left: 0\r\n        /*rtl: 4px*/\r\n    ;\r\n}\r\n\r\n.tui-full-calendar-layout,\r\n.tui-full-calendar-timegrid-timezone {\r\n    background-color: var(--#{$prefix}secondary-bg) !important;\r\n}\r\n\r\n.tui-full-calendar-dayname-container,\r\n.tui-full-calendar-left,\r\n.tui-full-calendar-splitter,\r\n.tui-full-calendar-time-date,\r\n.tui-full-calendar-weekday-grid-line,\r\n.tui-full-calendar-timegrid-timezone,\r\n.tui-full-calendar-timegrid-gridline {\r\n    border-color: var(--#{$prefix}border-color) !important;\r\n}\r\n\r\n.tui-full-calendar-weekday-exceed-in-week {\r\n    text-align: center;\r\n    width: 30px;\r\n    height: 30px;\r\n    line-height: 28px;\r\n    border-radius: 4px;\r\n    background-color: var(--#{$prefix}secondary-bg) !important;\r\n    color: var(--#{$prefix}body-color);\r\n    border-color: var(--#{$prefix}border-color);\r\n}\r\n\r\n.tui-full-calendar-timegrid-hour {\r\n    color: var(--#{$prefix}body-color) !important;\r\n}\r\n\r\n.tui-full-calendar-weekday-schedule-title {\r\n    color: var(--#{$prefix}emphasis-color) !important;\r\n\r\n    .tui-full-calendar-time-schedule {\r\n        font-weight: $font-weight-semibold;\r\n    }\r\n}\r\n\r\n.tui-full-calendar-popup-container {\r\n    background-color: var(--#{$prefix}secondary-bg) !important;\r\n    border-color: var(--#{$prefix}border-color) !important;\r\n}\r\n\r\n.tui-full-calendar-dropdown {\r\n    &:hover {\r\n        .tui-full-calendar-dropdown-button {\r\n            border-color: var(--#{$prefix}border-color)\r\n        }\r\n    }\r\n}\r\n\r\n.tui-full-calendar-popup-section-item {\r\n    &:hover, &:focus {\r\n        border-color: var(--#{$prefix}border-color);\r\n    }\r\n}\r\n\r\n.tui-full-calendar-arrow-bottom .tui-full-calendar-popup-arrow-fill {\r\n    border-top-color: var(--#{$prefix}border-color) !important;\r\n}\r\n\r\n.tui-full-calendar-arrow-top .tui-full-calendar-popup-arrow-fill {\r\n    border-bottom-color: var(--#{$prefix}border-color) !important;\r\n}\r\n\r\n.tui-full-calendar-arrow-bottom .tui-full-calendar-popup-arrow-borde {\r\n    border-bottom-color: var(--#{$prefix}border-color) !important;\r\n}\r\n\r\n.tui-full-calendar-button {\r\n    color: $input-color;\r\n    background-color: $input-bg  !important;\r\n    border-color: var(--#{$prefix}border-color);\r\n}\r\n\r\n.tui-full-calendar-popup-section-item {\r\n    border-color: var(--#{$prefix}border-color);\r\n}\r\n\r\n.tui-full-calendar-month-dayname,\r\n.tui-full-calendar-weekday-border {\r\n    border-top-color: var(--#{$prefix}border-color) !important;\r\n}\r\n\r\n.tui-full-calendar-arrow-top .tui-full-calendar-popup-arrow-border {\r\n    border-bottom-color: var(--#{$prefix}border-color) !important;\r\n}\r\n\r\n.tui-full-calendar-dropdown-menu {\r\n    border-color: var(--#{$prefix}border-color) !important;\r\n}\r\n\r\n.tui-full-calendar-dropdown-menu-item,\r\n.tui-full-calendar-dropdown-menu {\r\n    background-color: $input-bg  !important;\r\n}\r\n\r\n.tui-full-calendar-arrow-bottom .tui-full-calendar-popup-arrow-border {\r\n    border-top-color: var(--#{$prefix}border-color);\r\n}\r\n\r\n.tui-full-calendar-content {\r\n    background-color: $input-bg  !important;\r\n    color: $input-color  !important;\r\n}\r\n\r\n.tui-full-calendar-confirm {\r\n    background-color: $danger  !important;\r\n    color: $white  !important;\r\n\r\n    &:hover {\r\n        background-color: $red-600  !important;\r\n        color: $white  !important;\r\n    }\r\n}\r\n\r\n.tui-full-calendar-month-dayname-item {\r\n    span {\r\n        color: var(--#{$prefix}emphasis-color) !important;\r\n    }\r\n}\r\n\r\n.tui-full-calendar-weekday-grid-line {\r\n    &.tui-full-calendar-near-month-day {\r\n        &.tui-full-calendar-extra-date {\r\n            .tui-full-calendar-weekday-grid-header {\r\n                .tui-full-calendar-weekday-grid-date {\r\n                    color: var(--#{$prefix}secondary-color) !important;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\n.tui-full-calendar-weekday-grid-line {\r\n    &.tui-full-calendar-near-month-day {\r\n        .tui-full-calendar-weekday-grid-header {\r\n            .tui-full-calendar-weekday-grid-date {\r\n                color: var(--#{$prefix}emphasis-color) !important;\r\n            }\r\n        }\r\n    }\r\n}", "/* ==============\r\n  Calendar\r\n===================*/\r\n\r\n\r\n.fc td,\r\n.fc th {\r\n  border: $table-border-width solid $table-border-color;\r\n}\r\n\r\n\r\n\r\n.fc {\r\n  .fc-toolbar {\r\n    h2 {\r\n      font-size: 16px;\r\n      line-height: 30px;\r\n      text-transform: uppercase;\r\n    }\r\n\r\n    @media (max-width: 767.98px) {\r\n\r\n      .fc-left,\r\n      .fc-right,\r\n      .fc-center {\r\n        float: none;\r\n        display: block;\r\n        text-align: center;\r\n        clear: both;\r\n        margin: 10px 0;\r\n      }\r\n\r\n      >*>* {\r\n        float: none;\r\n      }\r\n\r\n      .fc-today-button {\r\n        display: none;\r\n      }\r\n    }\r\n\r\n    .btn {\r\n      text-transform: capitalize;\r\n    }\r\n\r\n  }\r\n\r\n  .fc-col-header-cell-cushion {\r\n    color: var(--#{$prefix}body-color);\r\n  }\r\n\r\n  .fc-daygrid-day-number {\r\n    color: var(--#{$prefix}body-color);\r\n  }\r\n}\r\n\r\n.fc-daygrid-event-dot {\r\n  display: none;\r\n}\r\n\r\n.fc-prev-button,\r\n.fc-next-button {\r\n  position: relative;\r\n  padding: 6px 8px !important;\r\n}\r\n\r\n\r\n.fc-toolbar-chunk {\r\n  .fc-button-group {\r\n    .fc-button {\r\n      background-color: var(--#{$prefix}primary);\r\n      border-color: var(--#{$prefix}primary);\r\n      ;\r\n      box-shadow: none;\r\n\r\n      &:hover,\r\n      &.active {\r\n        color: $white;\r\n        background-color: var(--#{$prefix}primary);\r\n        border-color: var(--#{$prefix}primary);\r\n      }\r\n    }\r\n  }\r\n\r\n  .fc-today-button {\r\n    background-color: var(--#{$prefix}primary) !important;\r\n    border-color: var(--#{$prefix}primary) !important;\r\n  }\r\n}\r\n\r\n.fc {\r\n\r\n  .fc-button-primary:not(:disabled).fc-button-active,\r\n  .fc-button-primary:not(:disabled):active {\r\n    background-color: var(--#{$prefix}primary) !important;\r\n    border-color: var(--#{$prefix}primary) !important;\r\n    color: $white !important;\r\n  }\r\n}\r\n\r\n.fc-toolbar {\r\n  @media (max-width: 575.98px) {\r\n    flex-direction: column;\r\n    gap: 16px;\r\n  }\r\n}\r\n\r\n.fc {\r\n  th.fc-widget-header {\r\n    background: $gray-300;\r\n    color: $gray-700;\r\n    line-height: 20px;\r\n    padding: 10px 0;\r\n    text-transform: uppercase;\r\n    font-weight: $font-weight-bold;\r\n  }\r\n}\r\n\r\n.fc-unthemed {\r\n\r\n  .fc-content,\r\n  .fc-divider,\r\n  .fc-list-heading td,\r\n  .fc-list-view,\r\n  .fc-popover,\r\n  .fc-row,\r\n  tbody,\r\n  td,\r\n  th,\r\n  thead {\r\n    border-color: $gray-300;\r\n  }\r\n\r\n  td.fc-today {\r\n    background: lighten($gray-200, 2%);\r\n  }\r\n}\r\n\r\n.fc-button {\r\n  background: var(--#{$prefix}secondary-bg);\r\n  border-color: var(--#{$prefix}border-color);\r\n  color: $gray-700;\r\n  text-transform: capitalize;\r\n  box-shadow: none;\r\n  padding: 6px 12px !important;\r\n  height: auto !important;\r\n}\r\n\r\n.fc-state-down,\r\n.fc-state-active,\r\n.fc-state-disabled {\r\n  background-color: $primary;\r\n  color: $white;\r\n  text-shadow: none;\r\n}\r\n\r\n.fc-event {\r\n  border-radius: 2px;\r\n  border: none;\r\n  cursor: move;\r\n  font-size: 0.8125rem;\r\n  margin: 5px 7px;\r\n  padding: 5px 5px;\r\n  text-align: center;\r\n  color: $white;\r\n\r\n  &.bg-dark {\r\n    .fc-event-time {\r\n      color: var(--#{$prefix}secondary-bg) !important;\r\n    }\r\n    background-color: var(--#{$prefix}secondary-color) !important;\r\n    .fc-event-title {\r\n      color: var(--#{$prefix}secondary-bg) !important;\r\n    }\r\n  }\r\n}\r\n\r\n.fc-event,\r\n.fc-event-dot {\r\n  background-color: $primary;\r\n}\r\n\r\n.fc-event .fc-content {\r\n  color: $white;\r\n}\r\n\r\n#external-events {\r\n  .external-event {\r\n    text-align: left;\r\n    padding: 8px 16px;\r\n    margin-left: 0;\r\n    margin-right: 0;\r\n  }\r\n}\r\n\r\n\r\n.fc-day-grid-event.fc-h-event.fc-event.fc-start.fc-end.bg-dark {\r\n  .fc-content {\r\n    color: $light;\r\n  }\r\n}\r\n\r\n\r\n// RTL\r\n[dir=\"rtl\"] {\r\n  .fc-header-toolbar {\r\n    direction: ltr !important;\r\n  }\r\n\r\n  .fc-toolbar>*>:not(:first-child) {\r\n    margin-left: .75em;\r\n  }\r\n}\r\n\r\n.fc-theme-standard .fc-scrollgrid {\r\n  border-color: var(--#{$prefix}border-color);\r\n}\r\n\r\n.fc .fc-daygrid-week-number {\r\n  background-color: var(--#{$prefix}body-bg);\r\n  color: var(--#{$prefix}body-color);\r\n}\r\n\r\n.fc .fc-daygrid-more-link {\r\n  padding: 5px;\r\n  font-size: 11px;\r\n  font-weight: 600;\r\n}\r\n\r\n.fc .fc-daygrid-more-link:hover {\r\n  background-color: rgba(var(--#{$prefix}primary-rgb), 0.1);\r\n}\r\n\r\n.fc .fc-popover-header {\r\n  padding: 6px 12px;\r\n}\r\n\r\n.fc-theme-standard .fc-popover-header {\r\n  background: var(--#{$prefix}body-bg);\r\n}\r\n\r\n.fc-theme-standard .fc-popover {\r\n    background: var(--#{$prefix}secondary-bg);\r\n    border: 1px solid var(--#{$prefix}border-color);\r\n}", "\r\n\r\n/* ==============\r\n  Druafula\r\n===================*/\r\n\r\n\r\n.task-box{\r\n  \r\n  border: 1px solid var(--#{$prefix}border-color);\r\n}\r\n\r\n.gu-transit {\r\n    border: 1px dashed var(--#{$prefix}border-color) !important;\r\n    background-color: var(--#{$prefix}gray-200) !important;\r\n}", "//\r\n// session-timeout.scss\r\n//\r\n\r\n#session-timeout-dialog {\r\n    .close {\r\n        display: none;\r\n    }\r\n\r\n    .countdown-holder {\r\n        color: $danger;\r\n        font-weight: $font-weight-medium;\r\n    }\r\n\r\n    .btn-default {\r\n        background-color: $white;\r\n        color: $danger;\r\n        box-shadow: none;\r\n    }\r\n}", "//\r\n// Range slider\r\n//\r\n\r\n.irs {\r\n  font-family: $font-family-base;\r\n}\r\n\r\n.irs--square {\r\n  cursor: pointer;\r\n\r\n  .irs-bar,\r\n  .irs-to,\r\n  .irs-from,\r\n  .irs-single {\r\n    background: $primary  !important;\r\n    font-size: 11px;\r\n  }\r\n\r\n  .irs-to,\r\n  .irs-from,\r\n  .irs-single {\r\n    &:before {\r\n      border-top-color: $primary;\r\n    }\r\n  }\r\n\r\n  .irs-line {\r\n    background: var(--#{$prefix}tertiary-bg);\r\n    border-color: var(--#{$prefix}tertiary-bg);\r\n  }\r\n\r\n  .irs-grid-text {\r\n    font-size: 11px;\r\n    color: var(--#{$prefix}secondary-color);\r\n  }\r\n\r\n  .irs-min,\r\n  .irs-max {\r\n    color: var(--#{$prefix}secondary-color);\r\n    background: var(--#{$prefix}tertiary-bg);\r\n    font-size: 11px;\r\n  }\r\n\r\n  .irs-handle {\r\n    border: 2px solid $primary;\r\n    width: 12px;\r\n    height: 12px;\r\n    top: 26px;\r\n    background-color: var(--#{$prefix}secondary-bg) !important;\r\n    cursor: pointer;\r\n\r\n    &:active {\r\n      cursor: pointer;\r\n    }\r\n  }\r\n\r\n  .irs-grid-pol {\r\n    background-color: var(--#{$prefix}secondary-color);\r\n  }\r\n}", "//\r\n//  Sweetalert2\r\n//\r\n\r\n.swal2-container {\r\n    .swal2-title {\r\n        font-size: 20px;\r\n        font-weight: $font-weight-medium;\r\n    }\r\n}\r\n\r\n.swal2-modal {\r\n    font-size: 14px;\r\n}\r\n\r\n.swal2-icon {\r\n    &.swal2-question {\r\n        border-color: $info;\r\n        color: $info;\r\n    }\r\n    &.swal2-success {\r\n        [class^=\"swal2-success-line\"] {\r\n            background-color: $success;\r\n        }\r\n\r\n        .swal2-success-ring {\r\n            border-color: rgba($success, 0.3);\r\n        }\r\n    }\r\n    &.swal2-warning {\r\n        border-color: $warning;\r\n        color: $warning;\r\n    }\r\n}\r\n\r\n.swal2-styled {\r\n    &:focus {\r\n        box-shadow: none;\r\n    }\r\n}\r\n\r\n.swal2-progress-steps {\r\n    .swal2-progress-step {\r\n        background: $primary;\r\n        &.swal2-active-progress-step {\r\n            background: $primary;\r\n            & ~ .swal2-progress-step,\r\n            & ~ .swal2-progress-step-line {\r\n                background: rgba($primary, 0.3);\r\n            }\r\n        }\r\n    }\r\n\r\n    .swal2-progress-step-line {\r\n        background: $primary;\r\n    }\r\n}\r\n\r\n.swal2-loader {\r\n    border-color: $primary transparent $primary transparent;\r\n}\r\n.swal2-popup {\r\n    background: var(--#{$prefix}secondary-bg);\r\n}\r\n\r\n.swal2-title, .swal2-html-container{\r\n     color: var(--#{$prefix}emphasis-color);\r\n}\r\n\r\n.swal2-file,\r\n.swal2-input,\r\n.swal2-textarea {\r\n    border-color: var(--#{$prefix}border-color);\r\n    color: var(--#{$prefix}body-color);\r\n    &:focus {\r\n        box-shadow: none;\r\n        border-color: var(--#{$prefix}border-color);\r\n    }\r\n}\r\n\r\ndiv:where(.swal2-container) div:where(.swal2-popup) {\r\n    color: var(--#{$prefix}secondary-color);\r\n}", "\r\n//\r\n// Rating\r\n//\r\n\r\n.symbol{\r\n  border-color: var(--#{$prefix}secondary-bg);\r\n}\r\n\r\n.rating-symbol-background, .rating-symbol-foreground {\r\n  font-size: 24px;\r\n}\r\n\r\n.symbol-empty {\r\n  background-color: var(--#{$prefix}gray-400);\r\n}\r\n\r\n.rating-symbol-foreground {\r\n  top: 0px;\r\n}\r\n\r\n.rating-star{\r\n  > span{\r\n    display: inline-block;\r\n    vertical-align: middle;\r\n\r\n    &.badge{\r\n      margin-left: 4px;\r\n    }\r\n  }\r\n}", "\r\n//\r\n// toastr.scss\r\n//\r\n\r\n\r\n/* =============\r\n   Notification\r\n============= */\r\n#toast-container {\r\n    > div {\r\n        box-shadow: $box-shadow;\r\n        opacity: 1;\r\n        &:hover {\r\n            box-shadow: $box-shadow;\r\n            opacity: 0.9;\r\n        }\r\n    }\r\n\r\n    &.toast-top-full-width, &.toast-bottom-full-width{\r\n        > div{\r\n          min-width: 96%;\r\n          margin: 4px auto;\r\n        }\r\n      }\r\n\r\n}\r\n\r\n\r\n@each $color, $value in $theme-colors {\r\n    .toast-#{$color} {\r\n        border: 2px solid $value !important;\r\n        background-color: rgba(($value), 0.8) !important;\r\n    }\r\n}\r\n\r\n\r\n// for error\r\n\r\n.toast-error {\r\n    background-color: rgba($danger,0.8);\r\n    border: 2px solid $danger;\r\n}\r\n\r\n.toastr-options{\r\n    padding: 24px;\r\n    background-color: var(--#{$prefix}tertiary-bg);\r\n    margin-bottom: 0;\r\n    border: 1px solid var(--#{$prefix}border-color);\r\n}", "\r\n//\r\n// Parsley\r\n//\r\n\r\n.error {\r\n  color: $danger;\r\n}\r\n\r\n.parsley-error {\r\n  border-color: $danger;\r\n}\r\n\r\n.parsley-errors-list {\r\n  display: none;\r\n  margin: 0;\r\n  padding: 0;\r\n  &.filled {\r\n    display: block;\r\n  }\r\n  > li {\r\n    font-size: 12px;\r\n    list-style: none;\r\n    color: $danger;\r\n    margin-top: 5px;\r\n  }\r\n}", "//\r\n// Select 2\r\n//\r\n\r\n.select2-container {\r\n    .select2-selection--single {\r\n        background-color: var(--#{$prefix}secondary-bg);\r\n        border: 1px solid $input-border-color;\r\n        height: 38px;\r\n\r\n        &:focus {\r\n            outline: none;\r\n        }\r\n\r\n        .select2-selection__rendered {\r\n            line-height: 36px;\r\n            padding-left: $input-padding-x;\r\n            color: var(--#{$prefix}secondary-color);\r\n        }\r\n\r\n\r\n        .select2-selection__arrow {\r\n            height: 34px;\r\n            width: 34px;\r\n            right: 3px;\r\n\r\n            b {\r\n                border-color: var(--#{$prefix}gray-500) transparent transparent transparent;\r\n                border-width: 6px 6px 0 6px;\r\n            }\r\n        }\r\n\r\n        .select2-selection__placeholder {\r\n            color: var(--#{$prefix}body-color);\r\n        }\r\n    }\r\n}\r\n\r\n[dir=\"rtl\"] {\r\n    .select2-selection__rendered {\r\n        text-align: end;\r\n    }\r\n}\r\n\r\n\r\n.select2-container--open {\r\n    .select2-selection--single {\r\n        .select2-selection__arrow {\r\n            b {\r\n                border-color: transparent transparent var(--#{$prefix}gray-500) transparent !important;\r\n                border-width: 0 6px 6px 6px !important;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.select2-container--default {\r\n    .select2-search--dropdown {\r\n        padding: 10px;\r\n        background-color: var(--#{$prefix}secondary-bg);\r\n\r\n        .select2-search__field {\r\n            border: 1px solid var(--#{$prefix}border-color-translucent);\r\n            background-color: var(--#{$prefix}secondary-bg);\r\n            color: var(--#{$prefix}secondary-color);\r\n            outline: none;\r\n        }\r\n    }\r\n\r\n    .select2-results__option--highlighted[aria-selected] {\r\n        background-color: $primary;\r\n    }\r\n\r\n    .select2-results__option[aria-selected=\"true\"] {\r\n        background-color: var(--#{$prefix}light);\r\n        color: var(--#{$prefix}emphasis-color);\r\n\r\n        &:hover {\r\n            background-color: $primary;\r\n            color: $white;\r\n        }\r\n    }\r\n\r\n    .select2-selection--multiple {\r\n        .select2-selection__choice__remove {\r\n            border-right: none;\r\n            color: var(--#{$prefix}emphasis-color);\r\n        }\r\n    }\r\n}\r\n\r\n.select2-results__option {\r\n    padding: 6px 12px;\r\n}\r\n\r\n.select2-container[dir=\"rtl\"] .select2-selection--single .select2-selection__rendered {\r\n    padding-left: $input-padding-x;\r\n}\r\n\r\n.select2-dropdown {\r\n    border: 1px solid var(--#{$prefix}border-color);\r\n    background-color: var(--#{$prefix}secondary-bg);\r\n    box-shadow: $box-shadow;\r\n}\r\n\r\n.select2-search {\r\n    input {\r\n        border: 1px solid var(--#{$prefix}border-color);\r\n    }\r\n}\r\n\r\n.select2-container {\r\n    .select2-selection--multiple {\r\n        min-height: 38px;\r\n        background-color: var(--#{$prefix}secondary-bg);\r\n        border: 1px solid var(--#{$prefix}border-color-translucent) !important;\r\n        padding: 2px $input-padding-x;\r\n\r\n        .select2-selection__rendered {\r\n            padding: 2px $input-padding-x;\r\n        }\r\n\r\n        .select2-search__field {\r\n            border: 0;\r\n            color: var(--#{$prefix}emphasis-color);\r\n            margin: 0;\r\n            margin-top: 7px;\r\n\r\n            &::placeholder {\r\n                color: var(--#{$prefix}secondary-color);\r\n            }\r\n        }\r\n\r\n        .select2-selection__choice {\r\n            background-color: var(--#{$prefix}tertiary-bg);\r\n            border: 1px solid var(--#{$prefix}border-color);\r\n            border-radius: 1px;\r\n            padding: 0 7px;\r\n        }\r\n\r\n        .select2-selection__rendered {\r\n            padding: 0;\r\n        }\r\n    }\r\n}\r\n\r\n.select2-container--default {\r\n    &.select2-container--focus {\r\n        .select2-selection--multiple {\r\n            border-color: var(--#{$prefix}border-color-translucent);\r\n        }\r\n    }\r\n\r\n    .select2-results__group {\r\n        font-weight: $font-weight-semibold;\r\n    }\r\n\r\n    .select2-selection--multiple {\r\n        .select2-selection__choice__display {\r\n            padding-left: 16px;\r\n        }\r\n    }\r\n}\r\n\r\n// ajax select\r\n\r\n.select2-result-repository__avatar {\r\n    float: left;\r\n    width: 60px;\r\n    margin-right: 10px;\r\n\r\n    img {\r\n        width: 100%;\r\n        height: auto;\r\n        border-radius: 2px;\r\n    }\r\n}\r\n\r\n.select2-result-repository__statistics {\r\n    margin-top: 7px;\r\n}\r\n\r\n.select2-result-repository__forks,\r\n.select2-result-repository__stargazers,\r\n.select2-result-repository__watchers {\r\n    display: inline-block;\r\n    font-size: 11px;\r\n    margin-right: 1em;\r\n    color: var(--#{$prefix}secondary-color);\r\n\r\n    .fa {\r\n        margin-right: 4px;\r\n\r\n        &.fa-flash {\r\n            &::before {\r\n                content: \"\\f0e7\";\r\n                font-family: \"Font Awesome 5 Free\";\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.select2-results__option--highlighted {\r\n\r\n    .select2-result-repository__forks,\r\n    .select2-result-repository__stargazers,\r\n    .select2-result-repository__watchers {\r\n        color: rgba($white, 0.8);\r\n    }\r\n}\r\n\r\n.select2-result-repository__meta {\r\n    overflow: hidden;\r\n}\r\n\r\n// templating-select\r\n\r\n.img-flag {\r\n    margin-right: 7px;\r\n    height: 15px;\r\n    width: 18px;\r\n}\r\n\r\n\r\n.select2-container--default .select2-results__option--selected {\r\n    background-color: var(--#{$prefix}tertiary-bg);\r\n}", "//\r\n//  Sweetalert2\r\n//\r\n\r\n/* CSS Switch */\r\ninput[switch] {\r\n  display: none;\r\n  + label {\r\n    font-size: 1em;\r\n    line-height: 1;\r\n    width: 56px;\r\n    height: 24px;\r\n    background-color: $gray-400;\r\n    background-image: none;\r\n    border-radius: 2rem;\r\n    padding: 0.16667rem;\r\n    cursor: pointer;\r\n    display: inline-block;\r\n    text-align: center;\r\n    position: relative;\r\n    font-weight: $font-weight-medium;\r\n    transition: all 0.1s ease-in-out;\r\n    &:before {\r\n      color: $dark;\r\n      content: attr(data-off-label);\r\n      display: block;\r\n      font-family: inherit;\r\n      font-weight: 500;\r\n      font-size: 12px;\r\n      line-height: 21px;\r\n      position: absolute;\r\n      right: 1px;\r\n      margin: 3px;\r\n      top: -2px;\r\n      text-align: center;\r\n      min-width: 1.66667rem;\r\n      overflow: hidden;\r\n      transition: all 0.1s ease-in-out;\r\n    }\r\n\r\n    &:after {\r\n      content: '';\r\n      position: absolute;\r\n      left: 3px;\r\n      background-color: $gray-200;\r\n      box-shadow: none;\r\n      border-radius: 2rem;\r\n      height: 20px;\r\n      width: 20px;\r\n      top: 2px;\r\n      transition: all 0.1s ease-in-out;\r\n    }\r\n  }\r\n\r\n  &:checked + label {\r\n    background-color: $primary;\r\n  }\r\n}\r\n\r\ninput[switch]:checked + label {\r\n  background-color: $primary;\r\n  &:before {\r\n    color: $white;\r\n    content: attr(data-on-label);\r\n    right: auto;\r\n    left: 3px;\r\n  }\r\n\r\n  &:after {\r\n    left: 33px;\r\n    background-color: $gray-200;\r\n  }\r\n}\r\n\r\ninput[switch=\"bool\"] + label {\r\n  background-color: $danger;\r\n}\r\ninput[switch=\"bool\"] + label:before,input[switch=\"bool\"]:checked + label:before,\r\ninput[switch=\"default\"]:checked + label:before{\r\n  color: $white;\r\n}\r\n\r\ninput[switch=\"bool\"]:checked + label {\r\n  background-color: $success;\r\n}\r\n\r\ninput[switch=\"default\"]:checked + label {\r\n  background-color: #a2a2a2;\r\n}\r\n\r\ninput[switch=\"primary\"]:checked + label {\r\n  background-color: $primary;\r\n}\r\n\r\ninput[switch=\"success\"]:checked + label {\r\n  background-color: $success;\r\n}\r\n\r\ninput[switch=\"info\"]:checked + label {\r\n  background-color: $info;\r\n}\r\n\r\ninput[switch=\"warning\"]:checked + label {\r\n  background-color: $warning;\r\n}\r\n\r\ninput[switch=\"danger\"]:checked + label {\r\n  background-color: $danger;\r\n}\r\n\r\ninput[switch=\"dark\"]:checked + label {\r\n  background-color: $dark;\r\n  &:before {\r\n    color: $light;\r\n  }\r\n}\r\n\r\n.square-switch{\r\n  margin-right: 7px;\r\n  input[switch]+label, input[switch]+label:after{\r\n    border-radius: 4px;\r\n  }\r\n}\r\n", "\r\n//\r\n// colorpicker.scss\r\n//\r\n\r\n.sp-container{\r\n  background-color: $dropdown-bg;\r\n  button{\r\n    padding: .25rem .5rem;\r\n      font-size: .71094rem;\r\n      border-radius: .2rem;\r\n      font-weight: 400;\r\n      color: $dark;\r\n  \r\n      &.sp-palette-toggle{\r\n        background-color: $light;\r\n      }\r\n      \r\n      &.sp-choose{\r\n        background-color: $success;\r\n        margin-left: 5px;\r\n        margin-right: 0;\r\n      }\r\n  }\r\n}\r\n\r\n.sp-palette-container{\r\n  border-right: 1px solid $border-color;\r\n}\r\n\r\n.sp-input{\r\n  background-color: $input-bg;\r\n  border-color: $input-border-color !important;\r\n  color: $input-color;\r\n  &:focus{\r\n    outline: none;\r\n  }\r\n}\r\n\r\n\r\n[dir=\"rtl\"]{\r\n\r\n  .sp-alpha{\r\n    direction: ltr;\r\n  }\r\n\r\n  .sp-original-input-container {\r\n    .sp-add-on{\r\n      border-top-right-radius: 0!important;\r\n      border-bottom-right-radius: 0!important;\r\n      border-top-left-radius: 4px!important;\r\n      border-bottom-left-radius: 4px!important\r\n    }\r\n  } \r\n\r\n  input.spectrum.with-add-on{\r\n    border: 1px solid $input-border-color;\r\n    border-left: 0;\r\n    border-top-left-radius: 0;\r\n    border-bottom-left-radius: 0;\r\n    border-top-right-radius: $input-border-radius;\r\n    border-bottom-right-radius: $input-border-radius;\r\n\r\n  }\r\n}", "\r\n\r\n/* Timepicker */\r\n.bootstrap-timepicker-widget {\r\n  table {\r\n    td {\r\n      a {\r\n        color: $input-color;\r\n        &:hover {\r\n          background-color: transparent;\r\n          border-color: transparent;\r\n          border-radius: 4px;\r\n          color: $primary;\r\n          text-decoration: none;\r\n        }\r\n      }\r\n      input {\r\n        width: 32px;\r\n        height: 32px;\r\n        border: 0;\r\n        color: var(--#{$prefix}body-color);\r\n        border: 1px solid var(--#{$prefix}border-color);\r\n        background-color: $input-bg;\r\n\r\n      }\r\n    }\r\n  }\r\n\r\n  &.dropdown-menu:after{\r\n    border-bottom-color: var(--#{$prefix}gray-200);\r\n  }\r\n  &.timepicker-orient-bottom:after{\r\n    border-top-color: var(--#{$prefix}gray-200);\r\n  }\r\n}\r\n\r\n\r\n.timepicker-orient-top{\r\n  top: $input-height !important;\r\n}\r\n\r\n.timepicker-orient-bottom{\r\n  top: auto !important;\r\n  bottom: $input-height !important;\r\n}\r\n\r\n.bootstrap-timepicker-widget{\r\n  left: 0 !important;\r\n  right: auto !important;\r\n}\r\n\r\n\r\n.bootstrap-timepicker-widget{\r\n  &.timepicker-orient-left{\r\n    &:before{\r\n      left: 6px;\r\n      right: auto;\r\n    }\r\n\r\n    &::after{\r\n      left: 7px;\r\n      right: auto;\r\n    }\r\n  }\r\n}", "\r\n//\r\n// Datepicker\r\n//\r\n\r\n.datepicker {\r\n  border: 1px solid var(--#{$prefix}border-color);\r\n  padding: 8px;\r\n  z-index: 999 !important;\r\n  \r\n\r\n  table{\r\n    tr{\r\n      th{\r\n        font-weight: 500;\r\n      }\r\n      td{\r\n        &.active, &.active:hover, .active.disabled, &.active.disabled:hover,\r\n        &.today,  &.today:hover, &.today.disabled, &.today.disabled:hover, \r\n        &.selected, &.selected:hover, &.selected.disabled, &.selected.disabled:hover,\r\n        span.active.active, span.active:hover.active{\r\n          background-color: $primary !important;\r\n          background-image: none;\r\n          box-shadow: none;\r\n          color: $white !important;\r\n        }\r\n\r\n        &.day.focused,\r\n        &.day:hover,\r\n        span.focused,\r\n        span:hover {\r\n            background: var(--#{$prefix}light);\r\n        }\r\n\r\n        &.new,\r\n        &.old,\r\n        span.new,\r\n        span.old {\r\n            color: var(--#{$prefix}gray-500);\r\n            opacity: 0.6;\r\n        }\r\n\r\n        &.range, &.range.disabled, &.range.disabled:hover, &.range:hover{\r\n            background-color: var(--#{$prefix}tertiary-bg);\r\n        }\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n.table-condensed{\r\n  >thead>tr>th, >tbody>tr>td {\r\n    padding: 7px;\r\n  }\r\n}\r\n\r\n.bootstrap-datepicker-inline{\r\n  .datepicker-inline{\r\n    width: auto !important;\r\n    display: inline-block;\r\n  }\r\n}\r\n\r\n\r\n// DATEPICKER\r\n\r\n.datepicker-container{\r\n  border: 1px solid var(--#{$prefix}border-color);\r\n  box-shadow: none;\r\n  background-color: $dropdown-bg;\r\n\r\n  &.datepicker-inline{\r\n    width: 212px;\r\n  }\r\n}\r\n\r\n.datepicker{\r\n  color: var(--#{$prefix}secondary-color);\r\n    .datepicker-switch:hover,.next:hover,.prev:hover,  tfoot tr th:hover {\r\n      background: var(--#{$prefix}light);\r\n    } \r\n  }\r\n\r\n\r\n.datepicker-panel{\r\n  \r\n  >ul{\r\n    >li{\r\n      background-color: $dropdown-bg;\r\n      border-radius: 4px;\r\n\r\n      &.picked, &.picked:hover{\r\n        background-color: rgba($primary, 0.25);\r\n        color: $primary;\r\n      }\r\n\r\n      &.highlighted, &.highlighted:hover, &:hover{\r\n        background-color: $primary;\r\n        color: $white;\r\n      }\r\n\r\n      \r\n      &.muted, &.muted:hover{\r\n        color: var(--#{$prefix}gray-500);\r\n        opacity: 0.6;\r\n      }\r\n    }\r\n\r\n    \r\n\r\n    &[data-view=week]{\r\n      >li{\r\n        font-weight: $font-weight-medium;\r\n      }\r\n\r\n      >li, >li:hover{\r\n        background-color: $dropdown-bg;\r\n      }\r\n    }\r\n  }\r\n}\r\n", "//\r\n// Bootstrap touchspin\r\n//\r\n\r\n.bootstrap-touchspin {\r\n  &.input-group {\r\n    &>.input-group-prepend {\r\n\r\n      &>.btn,\r\n      &>.input-group-text {\r\n        border-top-right-radius: 0;\r\n        border-bottom-right-radius: 0;\r\n      }\r\n    }\r\n  }\r\n\r\n  &.input-group {\r\n    &>.input-group-append {\r\n\r\n      &>.btn,\r\n      &>.input-group-text {\r\n        border-top-left-radius: 0;\r\n        border-bottom-left-radius: 0;\r\n      }\r\n    }\r\n  }\r\n}", "//\r\n// Form editors.scss\r\n//\r\n\r\n// Tinymce\r\n\r\nform {\r\n    .tox-tinymce {\r\n        border: 1px solid var(--#{$prefix}border-color) !important;\r\n    }\r\n\r\n    .tox-toolbar-overlord {\r\n        background-color: var(--#{$prefix}secondary-bg) !important;\r\n    }\r\n    .tox {\r\n        :not(svg):not(rect) {\r\n            color: $white !important;\r\n        }\r\n\r\n        &:not(.tox-tinymce-inline) {\r\n            .tox-editor-header {\r\n                box-shadow: var(--#{$prefix}box-shadow);\r\n            }\r\n        }\r\n\r\n        .tox-mbtn {\r\n            color: $body-secondary-color;\r\n\r\n            &:hover:not(:disabled):not(.tox-mbtn--active) {\r\n                background-color: $body-tertiary-bg;\r\n            }\r\n        }\r\n\r\n        .tox-tbtn {\r\n            &:hover {\r\n                color: $body-tertiary-color;\r\n                background-color: $body-tertiary-bg;\r\n            }\r\n        }\r\n\r\n        .tox-tbtn--disabled svg,\r\n        .tox-tbtn--disabled:hover svg,\r\n        .tox-tbtn:disabled svg,\r\n        .tox-tbtn:disabled:hover svg {\r\n            fill: $body-tertiary-color;\r\n        }\r\n\r\n        .tox-tbtn--bespoke {\r\n            background-color: $body-tertiary-bg;\r\n        }\r\n\r\n        .tox-editor-header {\r\n            background-color: var(--#{$prefix}secondary-bg) !important;\r\n        }\r\n\r\n        .tox-menubar {\r\n            background-color: var(--#{$prefix}secondary-bg) !important;\r\n\r\n            .tox-mbtn {\r\n                background-color: var(--#{$prefix}tertiary-bg) !important;\r\n                .tox-mbtn__select-label {\r\n                    color: $white !important;\r\n                }\r\n            }\r\n        }\r\n\r\n        .tox-toolbar__primary {\r\n            background-color: var(--#{$prefix}secondary-bg) !important;\r\n            .tox-toolbar__group {\r\n                .tox-tbtn {\r\n                    background-color: var(--#{$prefix}tertiary-bg) !important;\r\n                    .tox-icon {\r\n                        svg {\r\n                            fill: $white !important;\r\n                        }\r\n                    }\r\n                }\r\n            }\r\n        }\r\n\r\n        .tox-edit-area {\r\n            background-color: var(--#{$prefix}secondary-bg) !important;\r\n        }\r\n\r\n        .tox-promotion {\r\n            background-color: var(--#{$prefix}secondary-bg) !important;\r\n        }\r\n\r\n        .tox-statusbar {\r\n            background-color: var(--#{$prefix}secondary-bg) !important;\r\n            color: var(--#{$prefix}body-color) !important;\r\n        }\r\n    }\r\n}\r\n.tox .tox-collection--list .tox-collection__group{\r\n    border-color: var(--#{$prefix}border-color) !important;\r\n}\r\n\r\n.tox-collection{\r\n    background-color: var(--#{$prefix}secondary-bg) !important;\r\n}\r\n\r\n.tox-collection__item-label{\r\n    color: $white !important;\r\n}\r\n\r\n.tox .tox-collection--list .tox-collection__item--enabled{\r\n    background-color: var(--#{$prefix}tertiary-bg) !important;\r\n}\r\n\r\n.tox-collection__item-icon{\r\n    svg{\r\n        fill: var(--#{$prefix}body-color) !important;\r\n    }\r\n}", "\r\n//\r\n// Form-Upload\r\n//\r\n\r\n/* Dropzone */\r\n.dropzone {\r\n  min-height: 230px;\r\n  border: 2px dashed var(--#{$prefix}border-color);\r\n  background: var(--#{$prefix}secondary-bg);\r\n  border-radius: 6px;\r\n\r\n  .dz-message {\r\n    font-size: 24px;\r\n    width: 100%;\r\n    margin: 3rem 0;\r\n  }\r\n}", "//\r\n// Form Wizard\r\n//\r\n\r\n\r\n.form-wizard-wrapper {\r\n  label {\r\n    font-size: 14px;\r\n    text-align: right;\r\n  }\r\n}\r\n\r\n.wizard{\r\n\r\n  // step\r\n  .steps{\r\n    >ul{\r\n      display: flex;\r\n      flex-wrap: wrap;\r\n      padding-left: 0;\r\n      margin-bottom: 0;\r\n      list-style: none;\r\n\r\n      @media (max-width: 1199.98px) {\r\n        flex-direction: column;\r\n      }\r\n\r\n      > a, > li{\r\n        flex-basis: 0;\r\n        flex-grow: 1;\r\n      }\r\n\r\n      >li{\r\n        width: 100%;\r\n\r\n        a{\r\n          display: block;\r\n          padding: .5rem 1rem;\r\n          color: var(--#{$prefix}secondary-color);\r\n          font-weight: $font-weight-medium;\r\n\r\n          background-color: rgba($primary, 0.1);\r\n        }\r\n      }\r\n\r\n      .current-info{\r\n        position: absolute;\r\n        left: -999em;\r\n    }\r\n\r\n    \r\n    }\r\n\r\n    .number {\r\n      display: inline-block;\r\n      width: 38px;\r\n      height: 38px;\r\n      line-height: 34px;\r\n      border: 2px solid $primary;\r\n      color: $primary;\r\n      text-align: center;\r\n      border-radius: 50%;\r\n      margin-right: .5rem;\r\n    }\r\n\r\n    .current {\r\n      a, a:active, a:hover {\r\n        background-color: rgba($primary, 0.2);\r\n        color: var(--#{$prefix}emphasis-color);\r\n        .number {\r\n          background-color: $primary;\r\n          color: $white;\r\n        }\r\n      }\r\n    }\r\n  }\r\n\r\n  // content\r\n  >.content {\r\n    background-color: transparent;\r\n    padding: 14px;\r\n    margin-top: 0;\r\n    border-radius: 0;\r\n    min-height: 150px;\r\n    > .title{\r\n      position: absolute;\r\n      left: -999em;\r\n    }\r\n    >.body {\r\n      width: 100%;\r\n      height: 100%;\r\n      padding: 14px 0 0;\r\n      position: static\r\n    }\r\n  }\r\n\r\n  // actions\r\n\r\n  >.actions {\r\n    position: relative;\r\n    display: block;\r\n    text-align: right;\r\n    width: 100%;\r\n    > ul {\r\n        display: block;\r\n        text-align: right;\r\n        padding-left: 0;\r\n          > li{\r\n            display: inline-block;\r\n            margin: 0 0.5em;\r\n\r\n          }\r\n      }\r\n      a, a:active, a:hover {\r\n        background-color: $primary;\r\n        border-radius: 4px;\r\n        padding: 8px 15px;\r\n        color: $white;\r\n      }\r\n\r\n      .disabled {\r\n        a, a:active, a:hover {\r\n          opacity: .65;\r\n          background-color: $primary;\r\n          color: $white;\r\n          cursor: not-allowed;\r\n        }\r\n      }\r\n  }\r\n\r\n  // verticl wixard\r\n  &.vertical-wizard{\r\n    display: flex;\r\n    flex-wrap: wrap;\r\n\r\n    // step\r\n    .steps{\r\n      >ul{\r\n        flex-direction: column;\r\n\r\n        >li{\r\n            width: 100% !important;\r\n        }\r\n        \r\n      }\r\n    }\r\n\r\n    .steps, .content, .actions{\r\n      width: 100%;\r\n    }\r\n\r\n    .steps{\r\n        @media (min-width: 1200px) {\r\n          width: 25%;\r\n      }\r\n    }\r\n\r\n      .content{\r\n        padding: 24px;\r\n        @media (min-width: 1200px) {\r\n          width: 75%;\r\n          padding: 12px 24px;\r\n      }\r\n\r\n      >.body{\r\n        padding: 0;\r\n      }\r\n      \r\n    }\r\n  }\r\n}\r\n", "/* \r\nDatatable\r\n*/\r\n\r\n// datatable\r\n\r\ndiv.dataTables_wrapper {\r\n  div.dataTables_filter {\r\n    text-align: right;\r\n\r\n    @media (max-width: 767px) {\r\n      text-align: center;\r\n    }\r\n\r\n\r\n    input {\r\n      margin-left: 0.5em;\r\n      margin-right: 0;\r\n    }\r\n  }\r\n}\r\n\r\n.table,\r\ntable {\r\n  &.dataTable {\r\n    border-collapse: collapse !important;\r\n    &.dtr-inline.collapsed>tbody>tr>td {\r\n      position: relative;\r\n\r\n      &.dtr-control {\r\n        padding-left: 30px;\r\n\r\n        &:before {\r\n          top: 50%;\r\n          left: 5px;\r\n          height: 14px;\r\n          width: 14px;\r\n          margin-top: -9px;\r\n          display: block;\r\n          position: absolute;\r\n          color: $white;\r\n          border: 2px solid $white;\r\n          border-radius: 14px;\r\n          box-sizing: content-box;\r\n          text-align: center;\r\n          text-indent: 0 !important;\r\n          line-height: 14px;\r\n          content: '+';\r\n          background-color: $primary;\r\n        }\r\n      }\r\n    }\r\n  }\r\n}", "\r\n//\r\n// Responsive Table\r\n//\r\n\r\n.table-rep-plugin {\r\n  .btn-toolbar {\r\n    display: block;\r\n  }\r\n  .table-responsive {\r\n    border: none !important;\r\n  }\r\n  .btn-group{\r\n    .btn-default {\r\n      background-color: $secondary;\r\n      color: $light;\r\n      border: 1px solid $secondary;\r\n      &.btn-primary {\r\n          background-color: $primary;\r\n          border-color: $primary;\r\n          color: $white;\r\n          box-shadow: 0 0 0 2px rgba($primary, .5);\r\n      }\r\n  }\r\n    &.pull-right {\r\n      float: right;\r\n      .dropdown-menu {\r\n        right: 0;\r\n        transform: none !important;\r\n        top: 100% !important;\r\n      }\r\n    }\r\n  }\r\n  tbody {\r\n    th {\r\n      font-size: 14px;\r\n      font-weight: normal;\r\n    }\r\n  }\r\n\r\n  .checkbox-row {\r\n    padding-left: 40px;\r\n    color: $dropdown-color !important;\r\n\r\n    &:hover{\r\n      background-color: lighten($gray-200, 2%) !important;\r\n    }\r\n\r\n    label {\r\n      display: inline-block;\r\n      padding-left: 5px;\r\n      position: relative;\r\n      &::before {\r\n        -o-transition: 0.3s ease-in-out;\r\n        -webkit-transition: 0.3s ease-in-out;\r\n        background-color: $white;\r\n        border-radius: 3px;\r\n        border: 1px solid $gray-300;\r\n        content: \"\";\r\n        display: inline-block;\r\n        height: 17px;\r\n        left: 0;\r\n        margin-left: -20px;\r\n        position: absolute;\r\n        transition: 0.3s ease-in-out;\r\n        width: 17px;\r\n        outline: none !important;\r\n      }\r\n      &::after {\r\n        color: $gray-200;\r\n        display: inline-block;\r\n        font-size: 11px;\r\n        height: 16px;\r\n        left: 0;\r\n        margin-left: -20px;\r\n        padding-left: 3px;\r\n        padding-top: 1px;\r\n        position: absolute;\r\n        top: -1px;\r\n        width: 16px;\r\n      }\r\n    }\r\n    input[type=\"checkbox\"] {\r\n      cursor: pointer;\r\n      opacity: 0;\r\n      z-index: 1;\r\n      outline: none !important;\r\n\r\n      &:disabled + label {\r\n        opacity: 0.65;\r\n      }\r\n    }\r\n    input[type=\"checkbox\"]:focus + label {\r\n      &::before {\r\n        outline-offset: -2px;\r\n        outline: none;\r\n      }\r\n    }\r\n    input[type=\"checkbox\"]:checked + label {\r\n      &::after {\r\n        content: \"\\f00c\";\r\n        font-family: 'Font Awesome 5 Free';\r\n        font-weight: 900;\r\n      }\r\n    }\r\n    input[type=\"checkbox\"]:disabled + label {\r\n      &::before {\r\n        background-color: $gray-100;\r\n        cursor: not-allowed;\r\n      }\r\n    }\r\n    input[type=\"checkbox\"]:checked + label {\r\n      &::before {\r\n        background-color: $primary;\r\n        border-color: $primary;\r\n      }\r\n      &::after {\r\n        color: $white;\r\n      }\r\n    }\r\n  }\r\n\r\n  .fixed-solution {\r\n    .sticky-table-header{\r\n      top: $header-height !important;\r\n      background-color: $primary;\r\n      table{\r\n        color: $white;\r\n      }\r\n    }\r\n  }\r\n\r\n  table.focus-on tbody tr.focused th,\r\n  table.focus-on tbody tr.focused td,\r\n  .sticky-table-header {\r\n      background: $primary;\r\n      border-color: $primary;\r\n      color: $white;\r\n\r\n      table {\r\n          color: $white;\r\n      }\r\n  }\r\n\r\n    table.focus-on tbody tr.unfocused th,\r\n    table.focus-on tfoot tr.unfocused th,\r\n    table.focus-on tbody tr.unfocused td,\r\n    table.focus-on tfoot tr.unfocused td {\r\n      color: var(--#{$prefix}body-color);\r\n    }\r\n}\r\n\r\nbody[data-layout=\"horizontal\"] {\r\n  @media (min-width: 992px) {\r\n    .fixed-solution {\r\n      .sticky-table-header{\r\n        top: $header-height + 50px !important;;\r\n      }\r\n    }\r\n  }\r\n}", "\r\n//\r\n// Table editable\r\n//\r\n\r\n.table-edits{\r\n  input, select{\r\n    height: $input-height-sm;\r\n    padding: $input-padding-y-sm $input-padding-x-sm;\r\n    border: 1px solid $input-border-color;\r\n    background-color: $input-bg;\r\n    color: $input-color;\r\n    border-radius: $input-border-radius;\r\n    &:focus{\r\n      outline: none;\r\n      border-color: $input-focus-border-color;\r\n    }\r\n  }\r\n}", "//\r\n// apexcharts.scss\r\n//\r\n.apex-charts {\r\n    min-height: 10px !important;\r\n\r\n    text {\r\n        font-family: $font-family-base !important;\r\n        fill: var(--#{$prefix}gray-500);\r\n    }\r\n\r\n    .apexcharts-canvas {\r\n        margin: 0 auto;\r\n    }\r\n}\r\n\r\n.apexcharts-tooltip-title,\r\n.apexcharts-tooltip-text {\r\n    font-family: $font-family-base !important;\r\n}\r\n\r\n.apexcharts-legend-series {\r\n    font-weight: $font-weight-medium;\r\n}\r\n\r\n.apexcharts-gridline {\r\n    pointer-events: none;\r\n    stroke: $apex-grid-color;\r\n}\r\n\r\n.apexcharts-legend-text {\r\n    color: #74788d !important;\r\n    font-family: $font-family-base !important;\r\n    font-size: 13px !important;\r\n}\r\n\r\n.apexcharts-pie-label {\r\n    fill: $white !important;\r\n}\r\n\r\n.apexcharts-yaxis,\r\n.apexcharts-xaxis {\r\n    text {\r\n        font-family: $font-family-base !important;\r\n        fill: var(--#{$prefix}secondary-color);\r\n    }\r\n}\r\n\r\n.apexcharts-gridline {\r\n    stroke: var(--#{$prefix}border-color);\r\n}\r\n\r\n.apexcharts-radialbar-track.apexcharts-track {\r\n    path {\r\n        stroke: var(--#{$prefix}border-color);\r\n    }\r\n}\r\n\r\n.apexcharts-tooltip {\r\n    background-color: var(--#{$prefix}secondary-bg) !important;\r\n    border: 1px solid var(--#{$prefix}border-color) !important;\r\n\r\n    .apexcharts-tooltip-title {\r\n        background-color: var(--#{$prefix}border-color) !important;\r\n        border-bottom: 1px solid var(--#{$prefix}border-color) !important;\r\n    }\r\n}\r\n\r\n.apexcharts-pie-area {\r\n    stroke: var(--#{$prefix}secondary-bg);\r\n}\r\n\r\n.apexcharts-grid-borders {\r\n    line {\r\n        stroke: var(--#{$prefix}border-color);\r\n    }\r\n}\r\n\r\n.apexcharts-pie-label {\r\n    fill: var(--#{$prefix}white) !important;\r\n}\r\n\r\n.apexcharts-xaxis-tick {\r\n    stroke: var(--#{$prefix}border-color);\r\n}", "\r\n//\r\n// echarts.scss\r\n//\r\n\r\n.e-charts{\r\n    height: 350px;\r\n}", "\r\n\r\n/* Flot chart */\r\n.flot-charts-height {\r\n  height: 320px;\r\n}\r\n\r\n.flotTip {\r\n  padding: 8px 12px;\r\n  background-color: rgba($dark, 0.9);\r\n  z-index: 100;\r\n  color: $gray-100;\r\n  box-shadow: $box-shadow;\r\n  border-radius: 4px;\r\n}\r\n\r\n.legendLabel{\r\n  color: $gray-500;\r\n}", "//\r\n// sparkline.scss\r\n//\r\n\r\n.jqstooltip {\r\n  box-sizing: content-box;\r\n  width: auto !important;\r\n  height: auto !important;\r\n  background-color: $gray-800 !important;\r\n  box-shadow: $box-shadow-lg;\r\n  padding: 5px 10px !important;\r\n  border-radius: 3px;\r\n  border-color: $gray-900 !important;\r\n}\r\n\r\n.jqsfield {\r\n  color: $gray-200 !important;\r\n  font-size: 12px !important;\r\n  line-height: 18px !important;\r\n  font-family: $font-family-base !important;\r\n  font-weight: $font-weight-medium !important;\r\n}\r\n", "\r\n//\r\n// Google map\r\n//\r\n\r\n.gmaps, .gmaps-panaroma {\r\n  height: 300px !important;\r\n  background: $gray-100;\r\n  border-radius: 3px;\r\n}\r\n\r\n.gmaps-overlay {\r\n  display: block;\r\n  text-align: center;\r\n  color: $white;\r\n  font-size: 16px;\r\n  line-height: 40px;\r\n  background: $primary;\r\n  border-radius: 4px;\r\n  padding: 10px 20px;\r\n}\r\n\r\n.gmaps-overlay_arrow {\r\n  left: 50%;\r\n  margin-left: -16px;\r\n  width: 0;\r\n  height: 0;\r\n  position: absolute;\r\n  &.above {\r\n    bottom: -15px;\r\n    border-left: 16px solid transparent;\r\n    border-right: 16px solid transparent;\r\n    border-top: 16px solid $primary;\r\n  }\r\n  &.below {\r\n    top: -15px;\r\n    border-left: 16px solid transparent;\r\n    border-right: 16px solid transparent;\r\n    border-bottom: 16px solid $primary;\r\n  }\r\n  \r\n}", "//\r\n// vector-maps.scss\r\n//\r\n\r\n.jvectormap-label {\r\n    border: none;\r\n    background: $gray-800;\r\n    color: $gray-100;\r\n    font-family: $font-family-base;\r\n    font-size: $font-size-base;\r\n    padding: 5px 8px;\r\n}", "//\r\n// leaflet-maps.scss\r\n//\r\n\r\n.leaflet-map {\r\n    height: 300px;\r\n    &.leaflet-container{\r\n        z-index: 99;\r\n    }\r\n}", "// \r\n// authentication.scss\r\n//\r\n\r\n\r\n// authentication home icon\r\n.home-btn {\r\n    position: absolute;\r\n    top: 15px;\r\n    right: 25px;\r\n}\r\n\r\n// auth 2\r\n\r\n.auth-logo{\r\n    .auth-logo-dark{\r\n        display: var(--#{$prefix}display-block);\r\n    }\r\n    .auth-logo-light{\r\n        display: var(--#{$prefix}display-none);\r\n    }\r\n}\r\n\r\n.auth-body-bg{\r\n    background-color: var(--#{$prefix}secondary-bg);\r\n}\r\n\r\n\r\n// auth-pass-inputgroup\r\n\r\n.auth-pass-inputgroup{\r\n    input[type=\"input\"] + .btn .mdi-eye-outline{\r\n        &:before{\r\n            content: \"\\F06D1\";\r\n        }\r\n    }\r\n}\r\n\r\n// authentication full page\r\n\r\n.auth-full-bg{\r\n    background-color: rgba($primary, 0.25);\r\n    display: flex;\r\n\r\n    @media (min-width: 1200px){\r\n        height: 100vh;\r\n    }\r\n    \r\n\r\n    &::before{\r\n        content: \"\";\r\n        position: absolute;\r\n        width: 300px;\r\n        height: 300px;\r\n        border-radius: 50%;\r\n    }\r\n\r\n    .bg-overlay{\r\n        background: url(\"../images/bg-auth-overlay.png\");\r\n        background-size: cover;\r\n        background-repeat: no-repeat;\r\n        background-position: center;\r\n    }\r\n}\r\n\r\n.auth-full-page-content{\r\n    display: flex;\r\n\r\n    @media (min-width: 1200px){\r\n        min-height: 100vh;\r\n    }\r\n}\r\n\r\n.auth-review-carousel{\r\n    &.owl-theme {\r\n        .owl-dots {\r\n            .owl-dot{\r\n                span{\r\n                    background-color: rgba($primary, 0.25);\r\n                }\r\n                &.active, &:hover{\r\n                    span{\r\n                        background-color: $primary;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}", "// \r\n// ecommerce.scss\r\n//\r\n\r\n// product\r\n\r\n.search-box{\r\n    .form-control{\r\n        border-radius: 30px;\r\n        padding-left: 40px;\r\n    }\r\n    .search-icon{\r\n        font-size: 16px;    \r\n        position: absolute;\r\n        left: 13px;\r\n        top: 0;\r\n        line-height: 38px;\r\n    }\r\n}\r\n\r\n.product-list{\r\n    li{\r\n        a{\r\n            display: block;\r\n            padding: 4px 0px;\r\n            color: var(--#{$prefix}body-color);\r\n        }\r\n    }\r\n}\r\n\r\n.product-view-nav{\r\n    &.nav-pills {\r\n        .nav-item{\r\n            margin-left: 4px;\r\n        }\r\n        .nav-link{\r\n            width: 36px;\r\n            height: 36px;\r\n            font-size: 16px;\r\n            padding: 0;\r\n            line-height: 36px;\r\n            text-align: center;\r\n            border-radius: 50%;\r\n        }\r\n    }\r\n}\r\n\r\n.product-ribbon{\r\n    position: absolute;\r\n    right: 0px;\r\n    top: 0px;\r\n}\r\n\r\n// Product Details\r\n\r\n.product-detai-imgs{\r\n    .nav{\r\n        .nav-link{\r\n            margin: 7px 0px;\r\n\r\n            &.active{\r\n                background-color: var(--#{$prefix}tertiary-bg);\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.product-color{\r\n    a{\r\n        display: inline-block;\r\n        text-align: center;\r\n        color: var(--#{$prefix}body-color);\r\n        .product-color-item{\r\n            margin: 7px;\r\n        }\r\n        &.active, &:hover{\r\n            color: $primary;\r\n            .product-color-item{\r\n                border-color: $primary !important;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n// ecommerce cart\r\n\r\n.visa-card{\r\n    .visa-logo{\r\n        line-height: 0.5;\r\n    }\r\n\r\n    .visa-pattern{\r\n        position: absolute;\r\n        font-size: 385px;\r\n        color: rgba($white, 0.05);\r\n        line-height: 0.4;\r\n        right: 0px;\r\n        bottom: 0px;\r\n    }\r\n}\r\n\r\n\r\n// checkout\r\n\r\n.checkout-tabs{\r\n    .nav-pills{\r\n        .nav-link{\r\n            margin-bottom: 24px;\r\n            text-align: center;\r\n            background-color: var(--#{$prefix}secondary-bg);\r\n            box-shadow: $box-shadow;\r\n\r\n            &.active{\r\n                background-color: $primary;\r\n            }\r\n            .check-nav-icon{\r\n                font-size: 36px;\r\n            }\r\n        }\r\n    }\r\n}", "/* ==============\r\n  Email\r\n===================*/\r\n.email-leftbar {\r\n  width: 236px;\r\n  float: left;\r\n  padding: 20px;\r\n  border-radius: 5px;\r\n}\r\n\r\n.email-rightbar {\r\n  margin-left: 260px;\r\n}\r\n\r\n.chat-user-box {\r\n  p.user-title {\r\n    color: var(--#{$prefix}emphasis-color);\r\n    font-weight: 500;\r\n  }\r\n  p {\r\n    font-size: 12px;\r\n  }\r\n}\r\n\r\n@media (max-width: 767px) {\r\n  .email-leftbar {\r\n    float: none;\r\n    width: 100%;\r\n  }\r\n  .email-rightbar {\r\n    margin: 0;\r\n  }\r\n}\r\n\r\n\r\n.mail-list {\r\n  a {\r\n    display: block;\r\n    color: var(--#{$prefix}secondary-color);\r\n    line-height: 24px;\r\n    padding: 8px 5px;\r\n    &.active {\r\n      color: $danger;\r\n      font-weight: 500;\r\n    }\r\n  }\r\n}\r\n\r\n.message-list {\r\n  display: block;\r\n  padding-left: 0;\r\n\r\n  li {\r\n    position: relative;\r\n    display: block;\r\n    height: 50px;\r\n    line-height: 50px;\r\n    cursor: default;\r\n    transition-duration: .3s;\r\n\r\n    a{\r\n      color: var(--#{$prefix}secondary-color);\r\n    }\r\n\r\n    &:hover {\r\n      background: var(--#{$prefix}tertiary-bg);\r\n      transition-duration: .05s;\r\n    }\r\n\r\n    .col-mail {\r\n      float: left;\r\n      position: relative;\r\n    }\r\n\r\n    .col-mail-1 {\r\n      width: 320px;\r\n\r\n      .star-toggle,\r\n      .checkbox-wrapper-mail,\r\n      .dot {\r\n        display: block;\r\n        float: left;\r\n      }\r\n\r\n      .dot {\r\n        border: 4px solid transparent;\r\n        border-radius: 100px;\r\n        margin: 22px 26px 0;\r\n        height: 0;\r\n        width: 0;\r\n        line-height: 0;\r\n        font-size: 0;\r\n      }\r\n\r\n      .checkbox-wrapper-mail {\r\n        margin: 15px 10px 0 20px;\r\n      }\r\n\r\n      .star-toggle {\r\n        margin-top: 18px;\r\n        margin-left: 5px;\r\n      }\r\n\r\n      .title {\r\n        position: absolute;\r\n        top: 0;\r\n        left: 110px;\r\n        right: 0;\r\n        text-overflow: ellipsis;\r\n        overflow: hidden;\r\n        white-space: nowrap;\r\n        margin-bottom: 0;\r\n      }\r\n    }\r\n\r\n    .col-mail-2 {\r\n      position: absolute;\r\n      top: 0;\r\n      left: 320px;\r\n      right: 0;\r\n      bottom: 0;\r\n\r\n      .subject,\r\n      .date {\r\n        position: absolute;\r\n        top: 0;\r\n      }\r\n\r\n      .subject {\r\n        left: 0;\r\n        right: 200px;\r\n        text-overflow: ellipsis;\r\n        overflow: hidden;\r\n        white-space: nowrap;\r\n      }\r\n\r\n      .date {\r\n        right: 0;\r\n        width: 170px;\r\n        padding-left: 80px;\r\n      }\r\n    }\r\n\r\n    &.active,\r\n    &.active:hover {\r\n      box-shadow: inset 3px 0 0 $primary;\r\n    }\r\n\r\n    \r\n  &.unread  {\r\n    background-color: var(--#{$prefix}tertiary-bg);\r\n    font-weight: 500;\r\n    color: var(--#{$prefix}body-color);\r\n      a{\r\n        color: var(--#{$prefix}body-color);\r\n        font-weight: 500;\r\n      }\r\n    }\r\n  }\r\n\r\n\r\n  .checkbox-wrapper-mail {\r\n    cursor: pointer;\r\n    height: 20px;\r\n    width: 20px;\r\n    position: relative;\r\n    display: inline-block;\r\n    background-color: var(--#{$prefix}secondary-bg);\r\n    box-shadow: inset 0 0 0 1px var(--#{$prefix}border-color-translucent);\r\n    border-radius: 1px;\r\n\r\n    input {\r\n      opacity: 0;\r\n      cursor: pointer;\r\n    }\r\n    input:checked ~ label {\r\n      opacity: 1;\r\n    }\r\n\r\n    label {\r\n      position: absolute;\r\n      height: 20px;\r\n      width: 20px;\r\n      left: 0;\r\n      cursor: pointer;\r\n      opacity: 0;\r\n      margin-bottom: 0;\r\n      transition-duration: .05s;\r\n      top: 0;\r\n      &:before {\r\n        content: \"\\F012C\";\r\n        font-family: \"Material Design Icons\";\r\n        top: 0;\r\n        height: 20px;\r\n        color: var(--#{$prefix}body-color);\r\n        width: 20px;\r\n        position: absolute;\r\n        margin-top: -16px;\r\n        left: 4px;\r\n        font-size: 13px;\r\n      }\r\n    }\r\n  }\r\n}\r\n\r\n@media (max-width: 575.98px) { \r\n  .message-list li .col-mail-1 {\r\n      width: 200px;\r\n  }\r\n}", "// \r\n// File manager.scss\r\n//\r\n\r\n// file manager\r\n\r\n.filemanager-sidebar{\r\n    \r\n    @media (min-width: 1200px){\r\n        min-width: 230px;\r\n        max-width: 230px;\r\n    }\r\n\r\n    @media (min-width: 1366px){\r\n        min-width: 280px;\r\n        max-width: 280px;\r\n    }\r\n\r\n}\r\n\r\n.categories-list{\r\n    padding: 4px 0;\r\n    li{\r\n        a{\r\n            display: block;\r\n            padding: 8px 12px;\r\n            color: var(--#{$prefix}body-color);\r\n            font-weight: $font-weight-medium;\r\n        }\r\n\r\n        &.active{\r\n            a{\r\n                color: $primary;\r\n            }\r\n        }\r\n\r\n        ul{\r\n            padding-left: 16px;\r\n            li{\r\n                a{\r\n                    padding: 4px 12px;\r\n                    color: var(--#{$prefix}secondary-color);\r\n                    font-size: 13px;\r\n                    font-weight: $font-weight-normal;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\n", "//\r\n// Chat.scss\r\n//\r\n\r\n.chat-leftsidebar {\r\n    @media (min-width: 992px) {\r\n        min-width: 260px;\r\n    }\r\n\r\n    @media (min-width: 1200px) {\r\n        min-width: 380px;\r\n    }\r\n\r\n    .chat-leftsidebar-nav {\r\n        .nav {\r\n            background-color: var(--#{$prefix}secondary-bg);\r\n        }\r\n\r\n        .tab-content {\r\n            min-height: 488px;\r\n        }\r\n    }\r\n}\r\n\r\n.chat-noti-dropdown {\r\n    &.active {\r\n        &:before {\r\n            content: \"\";\r\n            position: absolute;\r\n            width: 8px;\r\n            height: 8px;\r\n            background-color: $danger;\r\n            border-radius: 50%;\r\n            right: 0;\r\n        }\r\n    }\r\n\r\n    .btn {\r\n        padding: 6px;\r\n        box-shadow: none;\r\n        font-size: 20px;\r\n    }\r\n}\r\n\r\n.chat-search-box {\r\n    .form-control {\r\n        border: 0;\r\n    }\r\n}\r\n\r\n.chat-list {\r\n    margin: 0;\r\n    li {\r\n        &.active {\r\n            a {\r\n                background-color: var(--#{$prefix}secondary-bg);\r\n                border-color: transparent;\r\n                box-shadow: $box-shadow;\r\n            }\r\n        }\r\n        a {\r\n            display: block;\r\n            padding: 14px 16px;\r\n            color: var(--#{$prefix}secondary-color);\r\n            transition: all 0.4s;\r\n            border-top: 1px solid var(--#{$prefix}border-color);\r\n            border-radius: 4px;\r\n            &:hover {\r\n                background-color: var(--#{$prefix}secondary-bg);\r\n                border-color: transparent;\r\n                box-shadow: $box-shadow;\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.user-chat-nav {\r\n    .dropdown {\r\n        .nav-btn {\r\n            color: var(--#{$prefix}body-color);\r\n            height: 40px;\r\n            width: 40px;\r\n            line-height: 42px;\r\n            box-shadow: none;\r\n            padding: 0;\r\n            font-size: 16px;\r\n            background-color: var(--#{$prefix}light);\r\n            border-radius: 50%;\r\n            border: none;\r\n        }\r\n\r\n        .dropdown-menu {\r\n            box-shadow: $box-shadow;\r\n            border: 1px solid var(--#{$prefix}border-color);\r\n        }\r\n    }\r\n}\r\n\r\n.chat-conversation {\r\n    li {\r\n        clear: both;\r\n    }\r\n\r\n    .chat-day-title {\r\n        position: relative;\r\n        text-align: center;\r\n        margin-bottom: 24px;\r\n\r\n        .title {\r\n            background-color: var(--#{$prefix}secondary-bg);\r\n            position: relative;\r\n            z-index: 1;\r\n            padding: 6px 24px;\r\n        }\r\n\r\n        &:before {\r\n            content: \"\";\r\n            position: absolute;\r\n            width: 100%;\r\n            height: 1px;\r\n            left: 0;\r\n            right: 0;\r\n            background-color: var(--#{$prefix}border-color);\r\n            top: 10px;\r\n        }\r\n        .badge {\r\n            font-size: 12px;\r\n        }\r\n    }\r\n    .conversation-list {\r\n        margin-bottom: 24px;\r\n        display: inline-block;\r\n        position: relative;\r\n\r\n        .ctext-wrap {\r\n            padding: 12px 24px;\r\n            background-color: rgba($primary, 0.1);\r\n            border-radius: 8px 8px 8px 0px;\r\n            overflow: hidden;\r\n\r\n            .conversation-name {\r\n                font-weight: $font-weight-semibold;\r\n                color: $primary;\r\n                margin-bottom: 4px;\r\n            }\r\n        }\r\n\r\n        .dropdown {\r\n            float: right;\r\n            .dropdown-toggle {\r\n                font-size: 18px;\r\n                padding: 4px;\r\n                color: var(--#{$prefix}secondary-color);\r\n                @media (max-width: 575.98px) {\r\n                    display: none;\r\n                }\r\n            }\r\n\r\n            .dropdown-menu {\r\n                box-shadow: $box-shadow;\r\n                border: 1px solid var(--#{$prefix}border-color);\r\n            }\r\n        }\r\n\r\n        .chat-time {\r\n            font-size: 12px;\r\n        }\r\n    }\r\n\r\n    .right {\r\n        .conversation-list {\r\n            float: right;\r\n            .ctext-wrap {\r\n                background-color: var(--#{$prefix}light);\r\n                text-align: right;\r\n                border-radius: 8px 8px 0px 8px;\r\n            }\r\n            .dropdown {\r\n                float: left;\r\n            }\r\n\r\n            &.last-chat {\r\n                .conversation-list {\r\n                    &:before {\r\n                        right: 0;\r\n                        left: auto;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    .last-chat {\r\n        .conversation-list {\r\n            &:before {\r\n                content: \"\\F0009\";\r\n                font-family: \"Material Design Icons\";\r\n                position: absolute;\r\n                color: $primary;\r\n                right: 0;\r\n                bottom: 0;\r\n                font-size: 16px;\r\n\r\n                @media (max-width: 575.98px) {\r\n                    display: none;\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.chat-input-section {\r\n    border-top: 1px solid var(--#{$prefix}border-color);\r\n}\r\n\r\n.chat-input {\r\n    border-radius: 30px;\r\n    background-color: var(--#{$prefix}light) !important;\r\n    border-color: var(--#{$prefix}light) !important;\r\n    padding-right: 120px;\r\n}\r\n\r\n.chat-input-links {\r\n    position: absolute;\r\n    right: 16px;\r\n    top: 50%;\r\n    transform: translateY(-50%);\r\n    li {\r\n        a {\r\n            font-size: 16px;\r\n            line-height: 36px;\r\n            padding: 0px 4px;\r\n            display: inline-block;\r\n        }\r\n    }\r\n}\r\n\r\n.chat-send {\r\n    @media (max-width: 575.98px) {\r\n        min-width: auto;\r\n    }\r\n}\r\n", "// \r\n// projects.scss\r\n//\r\n\r\n// project list\r\n\r\n.project-list-table {\r\n  border-collapse: separate;\r\n  border-spacing: 0 12px;\r\n\r\n  tr {\r\n    background-color: var(--#{$prefix}secondary-bg);\r\n  }\r\n}", "// \r\n// Contacts.scss\r\n//\r\n\r\n.contact-links{\r\n  a{\r\n    color: var(--#{$prefix}body-color);\r\n  }\r\n}\r\n\r\n// profile\r\n\r\n.profile-user-wid{\r\n  margin-top: -26px;\r\n}", "// \r\n// crypto.scss\r\n//\r\n\r\n@media (min-width: 576px) {\r\n    .currency-value {\r\n        position: relative;\r\n\r\n        &:after {\r\n            content: \"\\F04E1\";\r\n            font-family: \"Material Design Icons\";\r\n            font-size: 24px;\r\n            position: absolute;\r\n            width: 45px;\r\n            height: 45px;\r\n            line-height: 45px;\r\n            border-radius: 50%;\r\n            text-align: center;\r\n            right: 0;\r\n            top: 50%;\r\n            transform: translateY(-50%);\r\n            background-color: $primary;\r\n            color: $white;\r\n            z-index: 9;\r\n            right: -34px;\r\n        }\r\n    }\r\n\r\n}\r\n\r\n.crypto-buy-sell-nav-content {\r\n    border: 2px solid var(--#{$prefix}border-color);\r\n    border-top: 0;\r\n}\r\n\r\n\r\n// KYC Application\r\n\r\n.kyc-doc-verification {\r\n    .dropzone {\r\n        min-height: 180px;\r\n\r\n        .dz-message {\r\n            margin: 24px 0px;\r\n        }\r\n    }\r\n}\r\n\r\n\r\n/******************\r\n    Ico Landing\r\n*******************/\r\n\r\n.section {\r\n    position: relative;\r\n    padding-top: 80px;\r\n    padding-bottom: 80px;\r\n\r\n    &.bg-white {\r\n        background-color: var(--#{$prefix}secondary-bg) !important;\r\n    }\r\n}\r\n\r\n.small-title {\r\n    color: var(--#{$prefix}secondary-color);\r\n    margin-bottom: 8px;\r\n}\r\n\r\n\r\n// Navigation\r\n\r\n.navigation {\r\n    padding: 0 16px;\r\n    width: 100%;\r\n    z-index: 999;\r\n    margin-bottom: 0px;\r\n    transition: all 0.5s ease-in-out;\r\n    border-bottom: 1px solid rgba($white, 0.1);\r\n\r\n    @media (max-width: 991.98px) {\r\n        background-color: var(--#{$prefix}topnav-bg);\r\n    }\r\n\r\n    .navbar-logo {\r\n        line-height: 70px;\r\n        transition: all 0.4s;\r\n\r\n        .logo-dark {\r\n            display: none;\r\n\r\n            @media (max-width: 991.98px) {\r\n                display: block;\r\n            }\r\n        }\r\n\r\n        .logo-light {\r\n            display: block;\r\n\r\n            @media (max-width: 991.98px) {\r\n                display: none;\r\n            }\r\n        }\r\n    }\r\n\r\n\r\n    .navbar-nav {\r\n        .nav-item {\r\n            .nav-link {\r\n                color: rgba($white, 0.6);\r\n                line-height: 58px;\r\n                padding: 6px 16px;\r\n                font-weight: $font-weight-medium;\r\n                transition: all 0.4s;\r\n\r\n                @media (max-width: 991.98px) {\r\n                    color: var(--#{$prefix}header-item-color);\r\n                }\r\n\r\n                &:hover,\r\n                &.active {\r\n                    color: rgba($white, 0.9);\r\n\r\n                    @media (max-width: 991.98px) {\r\n                        color: $primary;\r\n                    }\r\n                }\r\n\r\n                @media (max-width: 991.98px) {\r\n                    line-height: 28px !important;\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    &.nav-sticky {\r\n        background-color: var(--#{$prefix}topnav-bg);\r\n        box-shadow: $box-shadow;\r\n\r\n        .navbar-logo {\r\n            line-height: 60px;\r\n\r\n            .logo-dark {\r\n                display: var(--#{$prefix}display-block);\r\n            }\r\n\r\n            .logo-light {\r\n                display: var(--#{$prefix}display-none);\r\n            }\r\n        }\r\n\r\n        .navbar-nav {\r\n            .nav-item {\r\n                .nav-link {\r\n                    line-height: 48px;\r\n                    color: var(--#{$prefix}header-item-color);\r\n\r\n                    &:hover,\r\n                    &.active {\r\n                        color: $primary;\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n.bg-overlay {\r\n    position: absolute;\r\n    height: 100%;\r\n    width: 100%;\r\n    right: 0;\r\n    bottom: 0;\r\n    left: 0;\r\n    top: 0;\r\n    opacity: 0.7;\r\n    background-color: $black;\r\n}\r\n\r\n.hero-section {\r\n    padding-top: 220px;\r\n    padding-bottom: 190px;\r\n\r\n    &.bg-ico-hero {\r\n        background-image: url(\"../images/crypto/bg-ico-hero.jpg\");\r\n        background-size: cover;\r\n        background-position: top;\r\n    }\r\n\r\n    @media (max-width: 575.98px) {\r\n        padding-top: 140px;\r\n        padding-bottom: 80px;\r\n    }\r\n\r\n    .hero-title {\r\n        font-size: 42px;\r\n\r\n        @media (max-width: 575.98px) {\r\n            font-size: 26px;\r\n        }\r\n    }\r\n\r\n    .ico-countdown {\r\n        font-size: 22px;\r\n        margin-right: -12px;\r\n        margin-left: -12px;\r\n\r\n        @media (max-width: 575.98px) {\r\n            display: block;\r\n        }\r\n\r\n        .coming-box {\r\n            margin-right: 12px;\r\n            margin-left: 12px;\r\n            border: 1px solid var(--#{$prefix}border-color);\r\n            border-radius: 4px;\r\n            padding: 8px;\r\n            background-color: var(--#{$prefix}secondary-bg);\r\n\r\n            @media (max-width: 575.98px) {\r\n                display: inline-block;\r\n                width: 40%;\r\n                margin-bottom: 24px;\r\n            }\r\n\r\n            span {\r\n                background-color: var(--#{$prefix}light);\r\n                font-size: 12px;\r\n                padding: 4px;\r\n                margin-top: 8px;\r\n            }\r\n        }\r\n    }\r\n\r\n    .softcap-progress {\r\n        overflow: visible;\r\n\r\n        .progress-bar {\r\n            overflow: visible;\r\n        }\r\n\r\n        .progress-label {\r\n            position: relative;\r\n            text-align: right;\r\n            color: var(--#{$prefix}body-color);\r\n            bottom: 20px;\r\n            font-size: 12px;\r\n            font-weight: $font-weight-medium;\r\n        }\r\n    }\r\n}\r\n\r\n\r\n// currency price\r\n\r\n.currency-price {\r\n    position: relative;\r\n    bottom: 40px;\r\n}\r\n\r\n\r\n// Clients\r\n\r\n.client-images {\r\n    img {\r\n        max-height: 34px;\r\n        width: auto !important;\r\n        margin: 12px auto;\r\n        opacity: 0.7;\r\n        transition: all 0.4s;\r\n    }\r\n}\r\n\r\n\r\n// Features\r\n\r\n.features-number {\r\n    opacity: 0.1;\r\n}\r\n\r\n\r\n// Team\r\n\r\n.team-box {\r\n    .team-social-links {\r\n        a {\r\n            color: var(--#{$prefix}body-color);\r\n            font-size: 14px;\r\n        }\r\n    }\r\n}\r\n\r\n// Blog\r\n\r\n.blog-box {\r\n    .blog-badge {\r\n        position: absolute;\r\n        top: 12px;\r\n        right: 12px;\r\n    }\r\n}\r\n\r\n// landing footer\r\n\r\n.landing-footer {\r\n    padding: 80px 0 40px;\r\n    background-color: $sidebar-dark-bg;\r\n    color: rgba($white, 0.5);\r\n\r\n    .footer-list-title {\r\n        color: rgba($white, 0.9);\r\n    }\r\n\r\n    .footer-list-menu {\r\n        li {\r\n            a {\r\n                display: block;\r\n                color: rgba($white, 0.5);\r\n                margin-bottom: 14px;\r\n                transition: all 0.4s;\r\n\r\n                &:hover {\r\n                    color: rgba($white, 0.8);\r\n                }\r\n            }\r\n        }\r\n    }\r\n\r\n    .blog-post {\r\n\r\n        .post {\r\n            display: block;\r\n            color: rgba($white, 0.5);\r\n            padding: 16px 0px;\r\n            border-bottom: 1px solid rgba($white, 0.1);\r\n\r\n            .post-title {\r\n                color: rgba($white, 0.8);\r\n                font-size: 14px;\r\n            }\r\n\r\n            &:first-of-type {\r\n                padding-top: 0;\r\n            }\r\n\r\n            &:last-of-type {\r\n                padding-bottom: 0;\r\n                border-bottom: 0;\r\n            }\r\n        }\r\n    }\r\n\r\n    .footer-border {\r\n        border-color: rgba($white, 0.1);\r\n    }\r\n}", "// \r\n// coming-soon.scss\r\n//\r\n\r\n.counter-number {\r\n    font-size: 32px;\r\n    font-weight: $font-weight-semibold;\r\n    text-align: center;\r\n    display: flex;\r\n    span {\r\n        font-size: 16px;\r\n        font-weight: $font-weight-normal;\r\n        display: block;\r\n        padding-top: 5px;\r\n    }\r\n}\r\n\r\n.coming-box {\r\n    width: 25%;\r\n}\r\n", "// \r\n// timeline.scss\r\n//\r\n\r\n/************** Horizontal timeline **************/ \r\n\r\n\r\n.hori-timeline{\r\n    .events{\r\n        .event-list{\r\n            text-align: center;\r\n            display: block;\r\n\r\n            .event-down-icon{\r\n                position: relative;\r\n                &::before{\r\n                    content: \"\";\r\n                    position: absolute;\r\n                    width: 100%;\r\n                    top: 16px;\r\n                    left: 0;\r\n                    right: 0;\r\n                    border-bottom: 3px dashed var(--#{$prefix}border-color);\r\n                }\r\n                .down-arrow-icon{\r\n                    position: relative;\r\n                    background-color: var(--#{$prefix}secondary-bg);\r\n                    padding: 4px;\r\n                }\r\n            }\r\n\r\n            &:hover{\r\n                .down-arrow-icon{\r\n                    animation: fade-down 1.5s infinite linear;\r\n                }\r\n            }\r\n\r\n            &.active{\r\n                .down-arrow-icon{\r\n                    animation: fade-down 1.5s infinite linear;\r\n                    &:before {\r\n                        content: \"\\ec4c\";\r\n                    }\r\n                }\r\n            }\r\n        }\r\n    }\r\n}\r\n\r\n\r\n/************** vertical timeline **************/ \r\n\r\n.verti-timeline{\r\n    border-left: 3px dashed var(--#{$prefix}border-color);\r\n    margin: 0 10px;\r\n    .event-list{\r\n        position: relative;\r\n        padding: 0px 0px 40px 30px;\r\n\r\n        .event-timeline-dot{\r\n            position: absolute;\r\n            left: -9px;\r\n            top: 0px;\r\n            z-index: 9;\r\n            font-size: 16px;\r\n        }\r\n        .event-content{\r\n            position: relative;\r\n            border: 2px solid var(--#{$prefix}border-color);\r\n            border-radius: 7px;\r\n        }\r\n\r\n        &.active{\r\n            .event-timeline-dot{\r\n                color: $primary;\r\n            }\r\n        }\r\n\r\n        &:last-child{\r\n            padding-bottom: 0px;\r\n        }\r\n    }\r\n}", "// \r\n// Extras pages.scss\r\n//\r\n\r\n\r\n// pricing\r\n\r\n\r\n.plan-box{\r\n    .plan-btn{\r\n        position: relative;\r\n\r\n        &::before{\r\n            content: \"\";\r\n            position: absolute;\r\n            width: 100%;\r\n            height: 2px;\r\n            background: var(--#{$prefix}border-color);\r\n            left: 0px;\r\n            right: 0px;\r\n            top: 12px;\r\n        }\r\n    }\r\n}\r\n\r\n// blog\r\n\r\n.blog-play-icon{\r\n    position: absolute;\r\n    top: 50%;\r\n    left: 0;\r\n    right: 0;\r\n    transform: translateY(-50%);\r\n    margin: 0px auto;\r\n}", "//\r\n// _jobs.scss\r\n//\r\n\r\n.jobs-categories {\r\n    a {\r\n        color: var(--#{$prefix}body-color); \r\n        transition: all 0.5s ease;\r\n        &:hover  {\r\n            color: $primary;\r\n        }\r\n    }\r\n}"]}