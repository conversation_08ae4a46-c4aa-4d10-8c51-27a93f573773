﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <AssemblyVersion>1.0.0.0</AssemblyVersion>
    <UserSecretsId>d011f135-3b0d-4002-8746-81105ce490a7</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <AspireProjRef>true</AspireProjRef>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Logs\**" />
    <Content Remove="Logs\**" />
    <EmbeddedResource Remove="Logs\**" />
    <None Remove="Logs\**" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="GoogleAuthenticator" />
    <PackageReference Include="Microsoft.Azure.AppConfiguration.AspNetCore" />
    <PackageReference Include="Microsoft.Data.SqlClient" />
    <PackageReference Include="Microsoft.FeatureManagement" />
    <PackageReference Include="Microsoft.Identity.Client" />
    <PackageReference Include="Microsoft.Identity.Web.MicrosoftGraph" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" />
    <PackageReference Include="Newtonsoft.Json" />
    <PackageReference Include="SQLitePCLRaw.bundle_e_sqlite3" />
    <PackageReference Include="SQLitePCLRaw.lib.e_sqlite3" />
    <PackageReference Include="Telerik.Licensing" />
	<PackageReference Include="Telerik.FontIcons" />
	<PackageReference Include="Telerik.SvgIcons" />
	<PackageReference Include="Telerik.UI.for.AspNet.Core" />  
  </ItemGroup>

  <ItemGroup>
    <Folder Include="DbFunctions\" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\AuthLogin\AuthLogin.csproj" />
    <ProjectReference Include="..\iRISDashboard\iRISDashboard.csproj" />
    <ProjectReference Include="..\iRISHomeWeb.ServiceDefaults\iRISHomeWeb.ServiceDefaults.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Reference Include="Administration">
      <HintPath>Projects\Administration.dll</HintPath>
    </Reference>
    <Reference Include="AdvancedEncryptionStandard">
      <HintPath>Projects\AdvancedEncryptionStandard.dll</HintPath>
    </Reference>
    <Reference Include="Dashboard">
      <HintPath>Projects\Dashboard.dll</HintPath>
    </Reference>
    <Reference Include="JSONLogger">
      <HintPath>Projects\JSONLogger.dll</HintPath>
    </Reference>
    <Reference Include="PasswordUtil">
      <HintPath>Projects\PasswordUtil.dll</HintPath>
    </Reference>
    <Reference Include="Reports">
      <HintPath>Projects\Reports.dll</HintPath>
    </Reference>
    <Reference Include="Settings">
      <HintPath>Projects\Settings.dll</HintPath>
    </Reference>
    <Reference Include="TwoFactorAuthentication">
      <HintPath>Projects\TwoFactorAuthentication.dll</HintPath>
    </Reference>
  </ItemGroup>

  <ItemGroup>
    <Content Update="wwwroot\assets\Dashboard\assets\Sqlite\SqliteGetDashboardSettings.sql">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
    <Content Update="wwwroot\assets\Dashboard\assets\Sqlite\GetAuthloginSettings.sql">
      <ExcludeFromSingleFile>true</ExcludeFromSingleFile>
      <CopyToPublishDirectory>PreserveNewest</CopyToPublishDirectory>
    </Content>
  </ItemGroup>

</Project>
