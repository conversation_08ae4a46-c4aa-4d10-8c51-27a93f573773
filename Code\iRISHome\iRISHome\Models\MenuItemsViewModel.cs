﻿namespace iRISHome.Models
{
    public class MenuItemsViewModel
    {
        public int ModuleID { get; set; }
        public string? ModuleName { get; set; }
        public string? Description { get; set; }
        public string? Link { get; set; }
        public int ParentModuleID { get; set; }
        public string? ModuleIcon { get; set; }
        public string? BookmarkIcon { get; set; }
        public string? BookmarkTitle { get; set; }
        public string? BookmarkStatus { get; set; }
        public string? BookmarkDataId { get; set; }
        public string? FavouriteStatus { get; set; }
        public List<MenuItemsViewModel> SubModules = new List<MenuItemsViewModel>();
    }
}
