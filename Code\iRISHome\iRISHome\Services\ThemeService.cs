using Microsoft.AspNetCore.Http;

namespace iRISHome.Services
{
    public class ThemeService
    {
        private readonly IHttpContextAccessor _httpContextAccessor;
        private const string ThemeKey = "current_theme";
        
        public ThemeService(IHttpContextAccessor httpContextAccessor)
        {
            _httpContextAccessor = httpContextAccessor;
        }

        public void SetTheme(string theme)
        {
            // Store theme preference in session or cookie
            _httpContextAccessor.HttpContext?.Session.SetString(ThemeKey, theme);
            
            // Optionally store in cookie for persistence across sessions
            _httpContextAccessor.HttpContext?.Response.Cookies.Append(ThemeKey, theme, new CookieOptions 
            { 
                HttpOnly = true,
                Secure = true,
                SameSite = SameSiteMode.Strict,
                Expires = DateTimeOffset.UtcNow.AddYears(1)
            });
        }

        public string GetCurrentTheme()
        {
            // Try get from session first
            var theme = _httpContextAccessor.HttpContext?.Session.GetString(ThemeKey);
            
            // Fall back to cookie if not in session
            if (string.IsNullOrEmpty(theme))
            {
                theme = _httpContextAccessor.HttpContext?.Request.Cookies[ThemeKey];
            }

            // Default to light if no preference stored
            return theme ?? "light-mode-switch";
        }
    }
}