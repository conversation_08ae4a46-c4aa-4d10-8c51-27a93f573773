
BEGIN TRANSACTION;

SELECT 
    'To-Do List' AS SettingName, 
    'Manage To-Do List settings.' AS Description,
    '/iRISDashboard/GetProjectSettings?ProjectType=To-Do List' AS LinkToConfigure, 
    '' AS Category, 
    'fa fa-lightbulb' AS SettingIcon
	
UNION ALL
 SELECT 'iRISupply' AS SettingName, 'Manage iRISupply dashboard settings.' AS Description 
	,'/iRISDashboard/GetProjectSettings?ProjectType=iRISupply' AS LinkToConfigure, '' AS Category,'fa fa-lightbulb' AS SettingIcon

UNION ALL
	
 SELECT 'iRIScope' AS SettingName, 'Manage iRIScope dashboard settings.' AS Description 
	,'/iRISDashboard/GetProjectSettings?ProjectType=iRIScope' AS LinkToConfigure, '' AS Category,'fa fa-lightbulb' AS SettingIcon

UNION ALL
	
 SELECT 'iRISecureBlood' AS SettingName, 'Manage iRISecureBlood dashboard settings.' AS Description 
	,'/iRISDashboard/GetProjectSettings?ProjectType=iRISecureBlood' AS LinkToConfigure, '' AS Category,'fa fa-lightbulb' AS SettingIcon

UNION ALL
	
 SELECT 'iRISpecimen' AS SettingName, 'Manage iRISpecimen dashboard settings.' AS Description 
	,'/iRISDashboard/GetProjectSettings?ProjectType=iRISpecimen' AS LinkToConfigure, '' AS Category,'fa fa-lightbulb' AS SettingIcon;

COMMIT;

