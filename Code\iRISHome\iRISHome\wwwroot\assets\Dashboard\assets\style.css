body {
    background-color: #f2f8ff;
    font-family: "Open Sans", sans-serif;
    color: #444444;
}

a {
    color: #1977cc;
    text-decoration: none;
}

    a:hover {
        color: #3291e6;
        text-decoration: none;
    }

h1,
h2,
h3,
h4,
h5,
h6 {
    font-family: "Raleway", sans-serif;
}

html {
    scroll-padding-top: 100px;
}

.pt-5 {
    padding-top: 6rem !important;
}

/*--------------------------------------------------------------
# Back to top button
--------------------------------------------------------------*/
.back-to-top {
    position: fixed;
    visibility: hidden;
    opacity: 0;
    right: 15px;
    bottom: 15px;
    z-index: 996;
    background: #1977cc;
    width: 40px;
    height: 40px;
    border-radius: 4px;
    transition: all 0.4s;
}

    .back-to-top i {
        font-size: 28px;
        color: #fff;
        line-height: 0;
    }

    .back-to-top:hover {
        background: #298ce5;
        color: #fff;
    }

    .back-to-top.active {
        visibility: visible;
        opacity: 1;
    }

.datepicker-dropdown {
    padding: 20px !important;
}

/*--------------------------------------------------------------
# Top Bar
--------------------------------------------------------------*/
#topbar {
    background: #fff;
    height: 40px;
    font-size: 14px;
    transition: all 0.5s;
    z-index: 996;
}

    #topbar.topbar-scrolled {
        top: -40px;
    }

    #topbar .contact-info a {
        line-height: 1;
        color: #444444;
        transition: 0.3s;
    }

        #topbar .contact-info a:hover {
            color: #1977cc;
        }

    #topbar .contact-info i {
        color: #1977cc;
        padding-right: 4px;
        margin-left: 15px;
        line-height: 0;
    }

        #topbar .contact-info i:first-child {
            margin-left: 0;
        }

    #topbar .social-links a {
        color: #437099;
        padding-left: 15px;
        display: inline-block;
        line-height: 1px;
        transition: 0.3s;
    }

        #topbar .social-links a:hover {
            color: #1977cc;
        }

        #topbar .social-links a:first-child {
            border-left: 0;
        }

/*--------------------------------------------------------------
# Header
--------------------------------------------------------------*/
#header {
    background: #fff;
    transition: all 0.5s;
    z-index: 997;
    padding: 5px 0;
    box-shadow: 0px 2px 15px rgba(25, 119, 204, 0.1);
}

    #header.header-scrolled {
        top: 0;
    }

    #header .logo {
        font-size: 30px;
        margin: 0;
        padding: 0;
        line-height: 1;
        font-weight: 700;
        letter-spacing: 0.5px;
        font-family: "Poppins", sans-serif;
    }

        #header .logo a {
            color: #2c4964;
        }

        #header .logo img {
            max-height: 60px;
        }

/*--------------------------------------------------------------
# Navigation Menu
--------------------------------------------------------------*/
.navbar {
    padding: 0;
}

    .navbar ul {
        margin: 0;
        padding: 0;
        display: flex;
        list-style: none;
        align-items: center;
    }

    .navbar li {
        position: relative;
    }

    .navbar > ul > li {
        position: relative;
        white-space: nowrap;
        padding: 8px 0 8px 20px;
    }

    .navbar a,
    .navbar a:focus {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 17px;
        color: #2c4964;
        white-space: nowrap;
        transition: 0.3s;
        border-bottom: 2px solid #fff;
        padding: 5px 2px;
    }

        .navbar a i,
        .navbar a:focus i {
            font-size: 12px;
            line-height: 0;
            margin-left: 5px;
        }

        .navbar a:hover,
        .navbar .active,
        .navbar .active:focus,
        .navbar li:hover > a {
            color: #1977cc;
            border-color: #1977cc;
        }

    .navbar .dropdown ul {
        display: block;
        position: absolute;
        left: 20px;
        top: calc(100% + 30px);
        margin: 0;
        padding: 10px 0;
        z-index: 99;
        opacity: 0;
        visibility: hidden;
        background: #fff;
        box-shadow: 0px 0px 30px rgba(127, 137, 161, 0.25);
        transition: 0.3s;
    }

        .navbar .dropdown ul li {
            min-width: 200px;
        }

        .navbar .dropdown ul a {
            padding: 10px 20px;
            font-size: 14px;
            font-weight: 500;
            text-transform: none;
            color: #082744;
            border: none;
        }

            .navbar .dropdown ul a i {
                font-size: 12px;
            }

            .navbar .dropdown ul a:hover,
            .navbar .dropdown ul .active:hover,
            .navbar .dropdown ul li:hover > a {
                color: #1977cc;
            }

    .navbar .dropdown:hover > ul {
        opacity: 1;
        top: 100%;
        visibility: visible;
    }

    .navbar .dropdown .dropdown ul {
        top: 0;
        left: calc(100% - 30px);
        visibility: hidden;
    }

    .navbar .dropdown .dropdown:hover > ul {
        opacity: 1;
        top: 0;
        left: 100%;
        visibility: visible;
    }

@media (max-width: 1366px) {
    .navbar .dropdown .dropdown ul {
        left: -90%;
    }

    .navbar .dropdown .dropdown:hover > ul {
        left: -100%;
    }
}

/**
* Mobile Navigation 
*/
.mobile-nav-toggle {
    color: #2c4964;
    font-size: 28px;
    cursor: pointer;
    display: none;
    line-height: 0;
    transition: 0.5s;
}

    .mobile-nav-toggle.bi-x {
        color: #fff;
    }

@media (max-width: 991px) {
    .mobile-nav-toggle {
        display: block;
    }

    .navbar ul {
        display: none;
    }
}

.navbar-mobile {
    position: fixed;
    overflow: hidden;
    top: 0;
    right: 0;
    left: 0;
    bottom: 0;
    background: rgba(28, 47, 65, 0.9);
    transition: 0.3s;
    z-index: 999;
}

    .navbar-mobile .mobile-nav-toggle {
        position: absolute;
        top: 15px;
        right: 15px;
    }

    .navbar-mobile ul {
        display: block;
        position: absolute;
        top: 55px;
        right: 15px;
        bottom: 15px;
        left: 15px;
        padding: 10px 0;
        background-color: #fff;
        overflow-y: auto;
        transition: 0.3s;
    }

    .navbar-mobile > ul > li {
        padding: 0;
    }

    .navbar-mobile a,
    .navbar-mobile a:focus {
        padding: 10px 20px;
        font-size: 15px;
        color: #2c4964;
        border: none;
    }

        .navbar-mobile a:hover,
        .navbar-mobile .active,
        .navbar-mobile li:hover > a {
            color: #1977cc;
        }

    .navbar-mobile .getstarted,
    .navbar-mobile .getstarted:focus {
        margin: 15px;
    }

    .navbar-mobile .dropdown ul {
        position: static;
        display: none;
        margin: 10px 20px;
        padding: 10px 0;
        z-index: 99;
        opacity: 1;
        visibility: visible;
        background: #fff;
        box-shadow: 0px 0px 30px rgba(127, 137, 161, 0.25);
    }

        .navbar-mobile .dropdown ul li {
            min-width: 200px;
        }

        .navbar-mobile .dropdown ul a {
            padding: 10px 20px;
        }

            .navbar-mobile .dropdown ul a i {
                font-size: 12px;
            }

            .navbar-mobile .dropdown ul a:hover,
            .navbar-mobile .dropdown ul .active:hover,
            .navbar-mobile .dropdown ul li:hover > a {
                color: #1977cc;
            }

    .navbar-mobile .dropdown > .dropdown-active {
        display: block;
    }

/*--------------------------------------------------------------
# Sections General
--------------------------------------------------------------*/
.section-bg {
    background-color: #f1f7fd;
}

.section-title {
    text-align: center;
    padding-bottom: 30px;
}

    .section-title h2 {
        font-size: 32px;
        font-weight: bold;
        margin-bottom: 20px;
        padding-bottom: 20px;
        display: inline-block;
        position: relative;
        color: #2c4964;
    }

        .section-title h2::before {
            content: "";
            position: absolute;
            display: block;
            height: 1px;
            background: #ddd;
            bottom: 1px;
            left: 50%;
            transform: translateX(-50%);
            width: var(--before-width, 200px);
        }

        .section-title h2::after {
            content: "";
            position: absolute;
            display: block;
            width: 40px;
            height: 3px;
            background: #1977cc;
            bottom: 0;
            left: 50%;
            transform: translateX(-50%);
            width: 50%;
        }

    .section-title p {
        margin-bottom: 0;
    }

/*--------------------------------------------------------------
# Services
--------------------------------------------------------------*/
.services .icon-box {
    text-align: center;
    border: 1px solid #d5e1ed;
    padding: 60px 20px;
    transition: all ease-in-out 0.3s;
    border-radius: 15px;
    align-items: center;
}

    .services .icon-box .icon {
        margin: 0 auto;
        width: 64px;
        height: 64px;
        background: #1977cc;
        border-radius: 5px;
        transition: all 0.3s ease-out 0s;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-bottom: 20px;
        transform-style: preserve-3d;
        position: relative;
        z-index: 2;
    }

        .services .icon-box .icon i {
            color: #fff;
            font-size: 28px;
            transition: ease-in-out 0.3s;
        }

        .services .icon-box .icon::before {
            position: absolute;
            content: "";
            left: -8px;
            top: -8px;
            height: 100%;
            width: 100%;
            background: rgba(25, 119, 204, 0.2);
            border-radius: 5px;
            transition: all 0.3s ease-out 0s;
            transform: translateZ(-1px);
            z-index: -1;
        }

    .services .icon-box h4 {
        font-weight: 700;
        margin-bottom: 15px;
        font-size: 21px;
        color: #2c4964;
    }

    .services .icon-box p {
        line-height: 24px;
        font-size: 14px;
        margin-bottom: 0;
        color: #2c4964;
    }


    .services .icon-box:hover {
        background: #1977cc;
        border-color: #1977cc;
    }

#moreText {
    display: none;
}

.icon-box:hover #moreText {
    display: inline;
}

.icon-box:hover #dotText {
    display: none;
}

.icon-box p {
    text-align: center;
}

.services .icon-box:hover .icon {
    background: #fff;
}

    .services .icon-box:hover .icon i {
        color: #1977cc;
    }

    .services .icon-box:hover .icon::before {
        background: rgba(255, 255, 255, 0.3);
    }

.services .icon-box:hover h4,
.services .icon-box:hover p {
    color: #fff;
    font-weight: 700;
}

/*--------------------------------------------------------------
# Departments
--------------------------------------------------------------*/
.departments {
    overflow: hidden;
}

    .departments .nav-tabs {
        border: 0;
    }

    .departments .nav-link {
        border: 0;
        padding: 12px 15px 12px 0;
        transition: 0.3s;
        color: #2c4964;
        border-radius: 0;
        border-right: 2px solid #ebf1f6;
        font-weight: 600;
        font-size: 15px;
    }

        .departments .nav-link:hover {
            color: #1977cc;
        }

        .departments .nav-link.active {
            color: #1977cc;
            border-color: #1977cc;
        }

    .departments .tab-pane.active {
        animation: fadeIn 0.5s ease-out;
    }

    .departments .details h3 {
        font-size: 26px;
        font-weight: 600;
        margin-bottom: 20px;
        color: #2c4964;
    }

    .departments .details p {
        color: #777777;
    }

        .departments .details p:last-child {
            margin-bottom: 0;
        }

@media (max-width: 992px) {
    .departments .nav-link {
        border: 0;
        padding: 15px;
    }

        .departments .nav-link.active {
            color: #fff;
            background: #1977cc;
        }
}
