﻿@inject Microsoft.AspNetCore.Http.IHttpContextAccessor HttpContextAccessor
@using Microsoft.AspNetCore.Http;
@{
    var session = HttpContextAccessor.HttpContext?.Session;
    var DisplayName = session?.GetString("DisplayName") ?? string.Empty;
}
<header id="page-topbar">
    <div class="navbar-header">
        <div class="d-flex">
            <!-- LOGO -->
            <div class="navbar-brand-box">
                <a href="" class="logo logo-dark">
                    <span class="logo-sm">
                        <img src="~/assets/images/maicon.png" alt="" height="30">
                    </span>
                    <span class="logo-lg">
                        <img src="~/assets/images/logo-light.png" alt="" height="30" style="width: 185px;height: 60px;">
                    </span>
                </a>

                <a href="@Url.Action("Index","iRISDashboard")" class="logo logo-light">
                    <span class="logo-sm">
                        <img src="~/assets/images/maicon.png" alt="" height="30">
                    </span>
                    <span class="logo-lg">
                        <img src="~/assets/images/logo-light.png" alt="" height="30" style="width: 185px;height: 60px;">
                    </span>
                </a>
            </div>

            <button type="button" class="btn btn-sm px-3 font-size-16 header-item waves-effect" id="vertical-menu-btn">
                <i class="fa fa-fw fa-bars"></i>
            </button>
            <div style="padding: calc(44px/ 2) 0;padding-right:10px;">
                <span>
                    <label class="" style="padding-left: 0px;font-size: larger;font-weight:bold;background: none;">iRISHome</label>
                </span>
            </div>
            @* <!-- App Search-->
            <form class="app-search d-none d-lg-block">
                <div class="position-relative">
                    <input type="text" class="form-control" placeholder="Search...">
                    <span class="bx bx-search-alt"></span>
                </div>
            </form> *@

            
        </div>

        <div class="d-flex">

            <div class="dropdown d-inline-block d-lg-none ms-2">
                <button type="button" class="btn header-item noti-icon waves-effect" id="page-header-search-dropdown"
                        data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <i class="mdi mdi-magnify"></i>
                </button>
                <div class="dropdown-menu dropdown-menu-lg dropdown-menu-end p-0"
                     aria-labelledby="page-header-search-dropdown">

                    <form class="p-3">
                        <div class="form-group m-0">
                            <div class="input-group">
                                <input type="text" class="form-control" placeholder="Search ..." aria-label="Recipient's username">
                                <div class="input-group-append">
                                    <button class="btn btn-primary" type="submit"><i class="mdi mdi-magnify"></i></button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>

            <div class="dropdown d-none d-lg-inline-block ms-1">
                <button type="button" class="btn header-item noti-icon waves-effect" data-bs-toggle="fullscreen">
                    <i class="bx bx-fullscreen"></i>
                </button>
            </div>
            @await Component.InvokeAsync("TopBar")
           

            <div class="dropdown d-inline-block">
                <button type="button" class="btn header-item waves-effect" id="page-header-user-dropdown"
                        data-bs-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <img class="rounded-circle header-profile-user" src="~/assets/images/UserAvatar.png"
                         alt="Header Avatar">
                    <span class="d-none d-xl-inline-block ms-1" key="t-henry">@ViewBag.UserName</span>
                    <i class="mdi mdi-chevron-down d-none d-xl-inline-block"></i>
                </button>
                <div class="dropdown-menu dropdown-menu-end">
                    <!-- item-->
                    <a class="dropdown-item" href=@Url.Action("Index", "User")><i class="bx bx-user font-size-16 align-middle me-1"></i> <span key="t-profile">Profile</span></a>
                    @* <div class="dropdown-divider"></div> *@
                    <a class="dropdown-item text-danger" href="@Url.Action("Logout","AuthLogin")"><i class="bx bx-power-off font-size-16 align-middle me-1 text-danger"></i> <span key="t-logout">Logout</span></a>
                </div>
            </div>

            <div class="dropdown d-inline-block">
                <button type="button" class="btn header-item noti-icon right-bar-toggle waves-effect">
                    <i class="bx bx-cog bx-spin"></i>
                </button>
            </div>

        </div>
    </div>
</header>
