# Keycloak Authentication Configuration with Azure App Configuration Fallback

This application implements a fallback mechanism for Keycloak authentication configuration that prioritizes Azure App Configuration but falls back to local appsettings.json when values are missing.

## How It Works

### Priority Order
1. **Azure App Configuration** (Primary source)
2. **appsettings.json** (Fallback source)

### Configuration Keys
The following configuration keys are used for Keycloak authentication:

```json
{
  "KeycloakAuthentication": {
    "OpenIdConnect": {
      "ClientId": "your-client-id",
      "ClientSecret": "your-client-secret", 
      "Authority": "https://your-keycloak-server/realms/your-realm"
    }
  }
}
```

### Fallback Logic
The application uses the `GetKeycloakConfiguration()` method which:

1. **Checks Azure App Configuration first**: If Azure App Configuration is successfully connected, it attempts to retrieve all three Keycloak configuration values.

2. **Falls back to appsettings.json**: If ANY of the three values (<PERSON><PERSON><PERSON><PERSON>, <PERSON>lient<PERSON><PERSON><PERSON>, Authority) are missing from Azure App Configuration, it creates a separate configuration builder to read from local appsettings files.

3. **Uses individual fallback**: Each configuration value is checked individually - if a value exists in Azure App Configuration, it's used; if not, the value from appsettings.json is used.

### Configuration Sources
- **Azure App Configuration**: Centralized configuration management in Azure
- **appsettings.json**: Local configuration file in the application
- **appsettings.Development.json**: Environment-specific overrides (optional)

## Setup Instructions

### 1. Configure appsettings.json
Add the Keycloak configuration section to your `appsettings.json`:

```json
{
  "KeycloakAuthentication": {
    "OpenIdConnect": {
      "ClientId": "your-actual-client-id",
      "ClientSecret": "your-actual-client-secret",
      "Authority": "https://your-keycloak-server/realms/your-realm"
    }
  }
}
```

### 2. Configure Azure App Configuration (Optional)
In Azure App Configuration, add the following keys:
- `KeycloakAuthentication:OpenIdConnect:ClientId`
- `KeycloakAuthentication:OpenIdConnect:ClientSecret`
- `KeycloakAuthentication:OpenIdConnect:Authority`

### 3. Behavior Examples

#### Scenario 1: All values in Azure App Configuration
- Result: Uses all values from Azure App Configuration
- Console output: "ALL VALUES FOUND IN AZURE APP CONFIGURATION"

#### Scenario 2: Some values missing from Azure App Configuration
- Result: Uses available values from Azure App Configuration, missing values from appsettings.json
- Console output: Shows which values come from which source

#### Scenario 3: Azure App Configuration unavailable
- Result: Uses all values from appsettings.json
- Console output: "USING APPSETTINGS.JSON FALLBACK"

## Debugging
The application provides detailed console output showing:
- Which configuration source is being used for each value
- Whether Azure App Configuration is available
- The final configuration values (without exposing secrets)
- Keycloak server connectivity test results

## Security Notes
- Client secrets are never displayed in console output
- The application only shows whether a secret is "FOUND" or "NOT FOUND"
- Ensure appsettings.json is properly secured and not committed to version control with real secrets
