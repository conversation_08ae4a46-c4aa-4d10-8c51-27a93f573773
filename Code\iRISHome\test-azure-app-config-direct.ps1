# Direct Azure App Configuration Test
Write-Host "=== Direct Azure App Configuration Test ===" -ForegroundColor Green

# Read connection string from appsettings.json
$appsettingsPath = "Code\iRISHome\iRISHome\appsettings.json"
if (Test-Path $appsettingsPath) {
    $appsettings = Get-Content $appsettingsPath | ConvertFrom-Json
    $connectionString = $appsettings.AppConfig.ConnectionString
    $endpoint = $appsettings.AppConfig.Endpoint
    
    Write-Host ""
    $connStatus = if ($connectionString) { 'SET' } else { 'NOT SET' }
    Write-Host "Connection String: $connStatus" -ForegroundColor Yellow
    Write-Host "Endpoint: $endpoint" -ForegroundColor Yellow
    
    # Extract the App Configuration name from the endpoint
    $configName = ($endpoint -split '\.')[0] -replace 'https://', ''
    
    Write-Host ""
    Write-Host "Testing Azure App Configuration: $configName" -ForegroundColor Cyan
    
    # Test with Azure CLI
    try {
        Write-Host "Getting specific Keycloak keys..." -ForegroundColor Yellow
        
        # Test each key individually
        $keys = @(
            "KeycloakAuthentication:OpenIdConnect:ClientId",
            "KeycloakAuthentication:OpenIdConnect:ClientSecret", 
            "KeycloakAuthentication:OpenIdConnect:Authority"
        )
        
        foreach ($key in $keys) {
            try {
                $result = az appconfig kv show --name $configName --key $key --output json 2>$null | ConvertFrom-Json
                if ($result) {
                    $hasValue = -not [string]::IsNullOrEmpty($result.value)
                    $status = if ($hasValue) { "HAS_VALUE" } else { "EMPTY" }
                    $color = if ($hasValue) { "Green" } else { "Red" }
                    Write-Host "  $key = $status" -ForegroundColor $color
                    
                    if ($hasValue) {
                        # Show first few characters of the value for verification
                        $preview = $result.value.Substring(0, [Math]::Min(10, $result.value.Length)) + "..."
                        Write-Host "    Preview: $preview" -ForegroundColor Gray
                    }
                } else {
                    Write-Host "  $key = NOT FOUND" -ForegroundColor Red
                }
            } catch {
                Write-Host "  $key = ERROR: $($_.Exception.Message)" -ForegroundColor Red
            }
        }
        
        # Test connection string format
        Write-Host ""
        Write-Host "=== CONNECTION STRING ANALYSIS ===" -ForegroundColor Cyan
        if ($connectionString) {
            $parts = $connectionString -split ';'
            foreach ($part in $parts) {
                if ($part.StartsWith('Endpoint=')) {
                    $endpointFromConn = $part -replace 'Endpoint=', ''
                    Write-Host "Endpoint from connection string: $endpointFromConn" -ForegroundColor Green
                }
                elseif ($part.StartsWith('Id=')) {
                    $id = $part -replace 'Id=', ''
                    Write-Host "Access Key ID: $id" -ForegroundColor Green
                }
                elseif ($part.StartsWith('Secret=')) {
                    Write-Host "Secret: SET (hidden)" -ForegroundColor Green
                }
            }
        }
        
    } catch {
        Write-Host "Error testing Azure App Configuration: $($_.Exception.Message)" -ForegroundColor Red
    }
    
} else {
    Write-Host "Error: appsettings.json not found at $appsettingsPath" -ForegroundColor Red
}

Write-Host ""
Write-Host "=== Test Complete ===" -ForegroundColor Green
